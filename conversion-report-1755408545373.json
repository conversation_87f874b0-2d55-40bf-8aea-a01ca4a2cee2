{"timestamp": "2025-08-17T05:29:05.373Z", "stats": {"filesProcessed": 1, "repositoryFiles": 0, "serviceFiles": 1, "controllerFiles": 0, "throwsConverted": 0, "functionsUpdated": 20, "messagePatternUpdated": 0, "importsAdded": 1, "errors": []}, "conversions": [{"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "createCharacter", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "initializeFromAuth", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "loginCharacter", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "logoutCharacter", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "getCharacterInfo", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "updateCharacter", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "getCharacterList", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "addCurrency", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "subtractCurrency", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "buyEnergy", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "levelUp", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "completeCreateStep", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "finishGuide", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "setCharacterBelief", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "useRedeemCode", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "updateContinuedBuff", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "checkResourceIsEnough", "changes": ["返回类型更新为Result<T>"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "searchByName", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "getScoutData", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.service.ts", "layer": "service", "className": "CharacterService", "methodName": "updateScoutData", "changes": ["返回类型更新为Result<T>", "转换为Result传递模式"]}], "errors": []}