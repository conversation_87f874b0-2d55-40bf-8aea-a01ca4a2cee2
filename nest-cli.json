{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "compilerOptions": {"deleteOutDir": true, "webpack": false, "assets": [], "watchAssets": false}, "generateOptions": {"spec": false}, "monorepo": true, "root": "apps/gateway", "projects": {"gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.app.json"}}, "auth": {"type": "application", "root": "apps/auth", "entryFile": "main", "sourceRoot": "apps/auth/src", "compilerOptions": {"tsConfigPath": "apps/auth/tsconfig.app.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "character": {"type": "application", "root": "apps/character", "entryFile": "main", "sourceRoot": "apps/character/src", "compilerOptions": {"tsConfigPath": "apps/character/tsconfig.app.json"}}, "hero": {"type": "application", "root": "apps/hero", "entryFile": "main", "sourceRoot": "apps/hero/src", "compilerOptions": {"tsConfigPath": "apps/hero/tsconfig.app.json"}}, "economy": {"type": "application", "root": "apps/economy", "entryFile": "main", "sourceRoot": "apps/economy/src", "compilerOptions": {"tsConfigPath": "apps/economy/tsconfig.app.json"}}, "social": {"type": "application", "root": "apps/social", "entryFile": "main", "sourceRoot": "apps/social/src", "compilerOptions": {"tsConfigPath": "apps/social/tsconfig.app.json"}}, "activity": {"type": "application", "root": "apps/activity", "entryFile": "main", "sourceRoot": "apps/activity/src", "compilerOptions": {"tsConfigPath": "apps/activity/tsconfig.app.json"}}, "game-config": {"type": "library", "root": "libs/game-config", "entryFile": "index", "sourceRoot": "libs/game-config/src", "compilerOptions": {"tsConfigPath": "libs/game-config/tsconfig.lib.json"}}, "game-constants": {"type": "library", "root": "libs/game-constants", "entryFile": "index", "sourceRoot": "libs/game-constants/src", "compilerOptions": {"tsConfigPath": "libs/game-constants/tsconfig.lib.json"}}, "game-utils": {"type": "library", "root": "libs/game-utils", "entryFile": "index", "sourceRoot": "libs/game-utils/src", "compilerOptions": {"tsConfigPath": "libs/game-utils/tsconfig.lib.json"}}, "game-types": {"type": "library", "root": "libs/game-types", "entryFile": "index", "sourceRoot": "libs/game-types/src", "compilerOptions": {"tsConfigPath": "libs/game-types/tsconfig.lib.json"}}, "match": {"type": "application", "root": "apps/match", "entryFile": "main", "sourceRoot": "apps/match/src", "compilerOptions": {"tsConfigPath": "apps/match/tsconfig.app.json"}}, "transaction-test": {"type": "application", "root": "apps/transaction-test", "entryFile": "main", "sourceRoot": "apps/transaction-test/src", "compilerOptions": {"tsConfigPath": "apps/transaction-test/tsconfig.app.json"}}}}