{"timestamp": "2025-08-17T04:55:37.229Z", "stats": {"filesProcessed": 0, "repositoryFiles": 0, "serviceFiles": 0, "controllerFiles": 0, "throwsConverted": 0, "functionsUpdated": 19, "messagePatternUpdated": 19, "importsAdded": 1, "errors": []}, "conversions": [{"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "createCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "loginCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "logoutCharacter", "changes": ["返回类型更新为MicroserviceResponse"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getCharacterInfo", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "updateCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getCharacterList", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "buyEnergy", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "levelUp", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "completeCreateStep", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "finishGuide", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "setCharacterBelief", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "useRedeemCode", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "updateContinuedBuff", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "searchCharacterByName", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getPersonInfo", "changes": ["返回类型更新为MicroserviceResponse"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "createRole", "changes": ["返回类型更新为MicroserviceResponse"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "modifyCharacterName", "changes": ["返回类型更新为MicroserviceResponse"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getEnergyReward", "changes": ["返回类型更新为MicroserviceResponse"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "costCashTask", "changes": ["返回类型更新为MicroserviceResponse"]}], "errors": []}