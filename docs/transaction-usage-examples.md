# Mongoose事务组件库使用指南

## 🚀 快速开始

### 1. 初始化事务管理器

```typescript
// 在应用启动时初始化
import mongoose from 'mongoose';
import { TransactionManager } from '@libs/common/transaction';

// 在main.ts或app.module.ts中
TransactionManager.initialize(mongoose);
```

### 2. 创建Repository

```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { Character, CharacterDocument } from './character.schema';
import { Result } from '@libs/common/types/result.type';

@Injectable()
export class CharacterRepository extends BaseRepository<CharacterDocument> {
  constructor(
    @InjectModel(Character.name) characterModel: Model<CharacterDocument>
  ) {
    super(characterModel, 'CharacterRepository');
  }

  // 自定义业务方法
  async transferToGuild(
    characterId: string,
    guildId: string,
    session?: ClientSession
  ): Promise<Result<CharacterDocument>> {
    return this.wrapOperation(async (session) => {
      return await this.model.findByIdAndUpdate(
        characterId,
        { 
          $set: { 
            guildId, 
            transferDate: new Date() 
          } 
        },
        { session, new: true }
      ).exec();
    })(session);
  }

  async findByGuildId(
    guildId: string,
    session?: ClientSession
  ): Promise<Result<CharacterDocument[]>> {
    return this.find({ guildId }, session);
  }
}
```

### 3. 创建Service

```typescript
import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/transaction';
import { Result } from '@libs/common/types/result.type';
import { CharacterRepository } from './character.repository';
import { GuildRepository } from './guild.repository';

@Injectable()
export class CharacterService extends BaseService {
  constructor(
    private readonly characterRepo: CharacterRepository,
    private readonly guildRepo: GuildRepository
  ) {
    super('CharacterService');
  }

  /**
   * 角色转会事务
   */
  async transferCharacter(
    characterId: string,
    fromGuildId: string,
    toGuildId: string
  ): Promise<Result<CharacterDocument>> {
    return this.executeTransaction(async (session) => {
      // 1. 检查角色存在
      const characterResult = await this.characterRepo.findById(characterId, session);
      const characterError = this.propagateError<CharacterDocument>(characterResult);
      if (characterError) return characterError;

      const character = characterResult.data;
      if (!character) {
        return this.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 检查目标公会存在
      const guildResult = await this.guildRepo.findById(toGuildId, session);
      const guildError = this.propagateError<CharacterDocument>(guildResult);
      if (guildError) return guildError;

      if (!guildResult.data) {
        return this.error('目标公会不存在', 'GUILD_NOT_FOUND');
      }

      // 3. 执行转会
      const transferResult = await this.characterRepo.transferToGuild(characterId, toGuildId, session);
      if (this.isError(transferResult)) {
        return transferResult; // 类型匹配，直接返回
      }

      // 4. 更新公会成员数
      const removeResult = await this.guildRepo.removeMember(fromGuildId, characterId, session);
      const removeError = this.propagateError<CharacterDocument>(removeResult);
      if (removeError) return removeError;

      const addResult = await this.guildRepo.addMember(toGuildId, characterId, session);
      const addError = this.propagateError<CharacterDocument>(addResult);
      if (addError) return addError;

      return transferResult;
    });
  }

  /**
   * 批量角色操作
   */
  async batchUpdateCharacters(
    updates: Array<{ id: string; data: Partial<CharacterDocument> }>
  ): Promise<Result<void>> {
    return this.executeTransaction(async (session) => {
      for (const update of updates) {
        const result = await this.characterRepo.updateById(update.id, update.data, session);
        const error = this.propagateError<void>(result);
        if (error) return error;
      }

      return this.empty();
    });
  }
}
```

### 4. Controller使用

```typescript
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MicroserviceResponse, MicroserviceResponseUtils } from '@libs/common/types/microservice-response.type';
import { CharacterService } from './character.service';

@Controller()
export class CharacterController {
  constructor(private readonly characterService: CharacterService) {}

  @MessagePattern('character.transfer')
  async transferCharacter(@Payload() payload: {
    characterId: string;
    fromGuildId: string;
    toGuildId: string;
  }): Promise<MicroserviceResponse<CharacterDocument>> {
    const result = await this.characterService.transferCharacter(
      payload.characterId,
      payload.fromGuildId,
      payload.toGuildId
    );
    
    return MicroserviceResponseUtils.fromResult(result);
  }

  @MessagePattern('character.batchUpdate')
  async batchUpdateCharacters(@Payload() payload: {
    updates: Array<{ id: string; data: Partial<CharacterDocument> }>;
  }): Promise<MicroserviceResponse<void>> {
    const result = await this.characterService.batchUpdateCharacters(payload.updates);
    
    return MicroserviceResponseUtils.fromResult(result);
  }
}
```

## 🔧 高级用法

### 1. 自定义事务配置

```typescript
// Service中使用自定义配置
async criticalOperation(): Promise<Result<any>> {
  return this.executeTransaction(async (session) => {
    // 事务逻辑...
    return this.success(result);
  }, {
    maxRetries: 5,        // 最多重试5次
    retryDelay: 2000,     // 重试延迟2秒
    timeout: 60000,       // 超时60秒
    readConcern: 'majority', // 读关注级别
    writeConcern: 'majority' // 写关注级别
  });
}
```

### 2. 复杂业务场景

```typescript
// 球员交易场景
async tradePlayer(
  playerId: string,
  buyerId: string,
  sellerId: string,
  price: number
): Promise<Result<TradeResult>> {
  return this.executeTransaction(async (session) => {
    // 1. 检查买家余额
    const buyerResult = await this.userRepo.findById(buyerId, session);
    const buyerError = this.propagateError<TradeResult>(buyerResult);
    if (buyerError) return buyerError;

    const buyer = buyerResult.data;
    const balanceError = this.validateCondition<TradeResult>(
      buyer.coins >= price,
      '余额不足',
      'INSUFFICIENT_BALANCE'
    );
    if (balanceError) return balanceError;

    // 2. 扣除买家金币
    const deductResult = await this.userRepo.updateById(
      buyerId,
      { $inc: { coins: -price } },
      session
    );
    const deductError = this.propagateError<TradeResult>(deductResult);
    if (deductError) return deductError;

    // 3. 增加卖家金币
    const addResult = await this.userRepo.updateById(
      sellerId,
      { $inc: { coins: price } },
      session
    );
    const addError = this.propagateError<TradeResult>(addResult);
    if (addError) return addError;

    // 4. 转移球员所有权
    const transferResult = await this.playerRepo.updateById(
      playerId,
      { userId: buyerId },
      session
    );
    const transferError = this.propagateError<TradeResult>(transferResult);
    if (transferError) return transferError;

    // 5. 记录交易历史
    const historyResult = await this.tradeHistoryRepo.create({
      playerId,
      buyerId,
      sellerId,
      price,
      tradeDate: new Date()
    }, session);
    const historyError = this.propagateError<TradeResult>(historyResult);
    if (historyError) return historyError;

    return this.success({
      playerId,
      newOwner: buyerId,
      price,
      tradeId: historyResult.data._id
    });
  });
}
```

## 📋 最佳实践

### 1. Repository层
- ✅ 继承BaseRepository获得基础CRUD操作
- ✅ 使用wrapOperation包装自定义操作
- ✅ 所有方法都支持可选的session参数
- ✅ 返回Result类型确保类型安全

### 2. Service层
- ✅ 继承BaseService获得事务管理能力
- ✅ 使用executeTransaction执行事务操作
- ✅ 使用propagateError传递Repository错误
- ✅ 使用validate*方法进行业务验证

### 3. 错误处理
- ✅ 每个Repository调用后立即检查错误
- ✅ 使用有意义的错误代码和消息
- ✅ 让事务自动处理回滚，不要手动干预

### 4. 性能优化
- ✅ 控制事务范围，避免长时间持有事务
- ✅ 使用批量操作替代循环单条操作
- ✅ 合理设置重试次数和延迟时间

## ⚠️ 注意事项

1. **初始化**：必须在应用启动时调用`TransactionManager.initialize(mongoose)`
2. **Session传递**：确保事务内所有操作都使用同一个session
3. **错误检查**：每个Repository调用后都要检查Result
4. **避免嵌套事务**：MongoDB不支持嵌套事务
5. **合理重试**：只对可重试的错误进行重试

这个组件库提供了简洁、实用、类型安全的事务处理能力，让开发者专注于业务逻辑而不是事务管理的复杂性。
