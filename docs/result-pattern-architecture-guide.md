# Result模式架构优化指南

## 📋 文档概述

本文档详细描述了微服务架构中Result模式的异常处理优化方案，旨在替代传统的异常抛出机制，建立统一的错误处理和响应格式规范。

**文档版本**: v1.0
**创建日期**: 2025-01-17
**适用范围**: 所有微服务（Character、Hero、Activity、Economy、Social等）

---

## 🎯 核心目标

### 1. 问题分析

**当前架构问题**：
- Controller层MessagePattern接口缺乏统一的WebSocket请求返回接口约束
- 响应链repository->service->controller各环节遇到异常直接throw，网关和客户端无法知晓异常详情
- 错误信息在调用链传播过程中丢失上下文
- 客户端收到的错误信息不够详细，难以进行精确的错误处理

### 2. 解决方案

**Result传递链架构**：
```
Repository层 -> Service层 -> Controller层 -> 网关层 -> 客户端
Result<T>   -> Result<T>  -> MicroserviceResponse -> WebSocket响应 -> 统一错误处理
```

**核心优势**：
- ✅ 错误信息完整传递，保持调用链上下文
- ✅ 类型安全的错误处理，编译时检查
- ✅ 避免异常传播性能损耗
- ✅ 统一的微服务响应格式
- ✅ 客户端可预期的错误处理

---

## 🏗️ 架构设计

### 1. Result类型定义

```typescript
// libs/common/src/types/result.type.ts

/**
 * 成功结果类型
 */
export interface SuccessResult<T = any> {
  success: true;
  data: T;
  message?: string;
}

/**
 * 失败结果类型
 */
export interface FailureResult {
  success: false;
  code: string;
  message: string;
  details?: any;
}

/**
 * Result联合类型
 */
export type Result<T = any> = SuccessResult<T> | FailureResult;

/**
 * Result工具类
 */
export class ResultUtils {
  static success<T>(data: T, message?: string): SuccessResult<T> {
    return { success: true, data, message };
  }

  static failure(code: string, message: string, details?: any): FailureResult {
    return { success: false, code, message, details };
  }

  static isSuccess<T>(result: Result<T>): result is SuccessResult<T> {
    return result.success === true;
  }

  static isFailure(result: Result<any>): result is FailureResult {
    return result.success === false;
  }
}
```

### 2. 微服务响应接口规范

```typescript
/**
 * 微服务统一响应接口
 */
export interface MicroserviceResponse<T = any> {
  code: number;        // 0表示成功，非0表示错误
  message: string;     // 响应消息
  data: T;            // 响应数据
  timestamp?: number;  // 时间戳
  requestId?: string;  // 请求ID，用于链路追踪
}

/**
 * 微服务响应工具类
 */
export class MicroserviceResponseUtils {
  /**
   * 从Result创建微服务响应
   */
  static fromResult<T>(result: Result<T>, requestId?: string): MicroserviceResponse<T> {
    if (ResultUtils.isSuccess(result)) {
      return {
        code: 0,
        message: result.message || '操作成功',
        data: result.data,
        timestamp: Date.now(),
        requestId
      };
    } else {
      return {
        code: this.getErrorCode(result.code),
        message: result.message,
        data: null as T,
        timestamp: Date.now(),
        requestId
      };
    }
  }

  /**
   * 错误码映射
   */
  private static getErrorCode(code: string): number {
    const errorCodeMap: Record<string, number> = {
      // 通用错误 1000-1999
      'UNKNOWN_ERROR': 1000,
      'INVALID_PARAMETER': 1001,
      'PERMISSION_DENIED': 1002,

      // 资源错误 2000-2999
      'RESOURCE_NOT_FOUND': 2000,
      'RESOURCE_ALREADY_EXISTS': 2001,
      'RESOURCE_CONFLICT': 2002,

      // Character服务特定错误 5000-5999
      'CHARACTER_NOT_FOUND': 5000,
      'CHARACTER_NAME_TAKEN': 5001,
      'CHARACTER_LEVEL_MAX': 5002,

      // 数据库错误 9000-9999
      'DATABASE_ERROR': 9000,
      'DOCUMENT_VALIDATION_ERROR': 9001,
    };

    return errorCodeMap[code] || 1000;
  }
}
```

---

## 🔄 调用链详细设计

### 以character.create接口为例

#### 1. Controller层 (接口层)

**职责**: 统一响应格式，MessagePattern接口约束

```typescript
// apps/character/src/modules/character/character.controller.ts

@Controller()
export class CharacterController {

  /**
   * 创建新角色
   *
   * 🎯 Controller层职责：
   * 1. 接收WebSocket请求
   * 2. 调用Service层业务逻辑
   * 3. 将Result转换为统一的MicroserviceResponse格式
   * 4. 返回给网关层
   */
  @MessagePattern('character.create')
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(
    @Payload() payload: {
      createDto: CreateCharacterDto;
      injectedContext?: InjectedContext
    }
  ): Promise<MicroserviceResponse> {
    this.logger.log(`创建角色请求: ${JSON.stringify(payload.createDto)}`);

    // 调用Service层，获取Result
    const result = await this.characterService.createCharacter(payload.createDto);

    // 转换为统一响应格式
    return MicroserviceResponseUtils.fromResult(result, payload.injectedContext?.requestId);
  }
}
```

**关键变化**：
- 返回类型：`Promise<MicroserviceResponse>`
- 响应处理：使用`MicroserviceResponseUtils.fromResult()`统一转换
- 错误处理：不再需要try-catch，Result已包含所有错误信息
