# Result模式架构优化指南

## 📋 文档概述

本文档详细描述了微服务架构中Result模式的异常处理优化方案，旨在替代传统的异常抛出机制，建立统一的错误处理和响应格式规范。

**文档版本**: v1.0
**创建日期**: 2025-01-17
**适用范围**: 所有微服务（Character、Hero、Activity、Economy、Social等）

---

## 🎯 核心目标

### 1. 问题分析

**当前架构问题**：
- Controller层MessagePattern接口缺乏统一的WebSocket请求返回接口约束
- 响应链repository->service->controller各环节遇到异常直接throw，网关和客户端无法知晓异常详情
- 错误信息在调用链传播过程中丢失上下文
- 客户端收到的错误信息不够详细，难以进行精确的错误处理

### 2. 解决方案

**Result传递链架构**：
```
Repository层 -> Service层 -> Controller层 -> 网关层 -> 客户端
Result<T>   -> Result<T>  -> MicroserviceResponse -> WebSocket响应 -> 统一错误处理
```

**核心优势**：
- ✅ 错误信息完整传递，保持调用链上下文
- ✅ 类型安全的错误处理，编译时检查
- ✅ 避免异常传播性能损耗
- ✅ 统一的微服务响应格式
- ✅ 客户端可预期的错误处理

---

## 🏗️ 架构设计

### 1. Result类型定义

```typescript
// libs/common/src/types/result.type.ts

/**
 * 成功结果类型
 */
export interface SuccessResult<T = any> {
  success: true;
  data: T;
  message?: string;
}

/**
 * 失败结果类型
 */
export interface FailureResult {
  success: false;
  code: string;
  message: string;
  details?: any;
}

/**
 * Result联合类型
 */
export type Result<T = any> = SuccessResult<T> | FailureResult;

/**
 * Result工具类
 */
export class ResultUtils {
  static success<T>(data: T, message?: string): SuccessResult<T> {
    return { success: true, data, message };
  }

  static failure(code: string, message: string, details?: any): FailureResult {
    return { success: false, code, message, details };
  }

  static isSuccess<T>(result: Result<T>): result is SuccessResult<T> {
    return result.success === true;
  }

  static isFailure(result: Result<any>): result is FailureResult {
    return result.success === false;
  }
}
```

### 2. 微服务响应接口规范

```typescript
/**
 * 微服务统一响应接口
 */
export interface MicroserviceResponse<T = any> {
  code: number;        // 0表示成功，非0表示错误
  message: string;     // 响应消息
  data: T;            // 响应数据
  timestamp?: number;  // 时间戳
  requestId?: string;  // 请求ID，用于链路追踪
}

/**
 * 微服务响应工具类
 */
export class MicroserviceResponseUtils {
  /**
   * 从Result创建微服务响应
   */
  static fromResult<T>(result: Result<T>, requestId?: string): MicroserviceResponse<T> {
    if (ResultUtils.isSuccess(result)) {
      return {
        code: 0,
        message: result.message || '操作成功',
        data: result.data,
        timestamp: Date.now(),
        requestId
      };
    } else {
      return {
        code: this.getErrorCode(result.code),
        message: result.message,
        data: null as T,
        timestamp: Date.now(),
        requestId
      };
    }
  }

  /**
   * 错误码映射
   */
  private static getErrorCode(code: string): number {
    const errorCodeMap: Record<string, number> = {
      // 通用错误 1000-1999
      'UNKNOWN_ERROR': 1000,
      'INVALID_PARAMETER': 1001,
      'PERMISSION_DENIED': 1002,

      // 资源错误 2000-2999
      'RESOURCE_NOT_FOUND': 2000,
      'RESOURCE_ALREADY_EXISTS': 2001,
      'RESOURCE_CONFLICT': 2002,

      // Character服务特定错误 5000-5999
      'CHARACTER_NOT_FOUND': 5000,
      'CHARACTER_NAME_TAKEN': 5001,
      'CHARACTER_LEVEL_MAX': 5002,

      // 数据库错误 9000-9999
      'DATABASE_ERROR': 9000,
      'DOCUMENT_VALIDATION_ERROR': 9001,
    };

    return errorCodeMap[code] || 1000;
  }
}
```

---

## 🔄 调用链详细设计

### 以character.create接口为例

#### 1. Controller层 (接口层)

**职责**: 统一响应格式，MessagePattern接口约束

```typescript
// apps/character/src/modules/character/character.controller.ts

@Controller()
export class CharacterController {

  /**
   * 创建新角色
   *
   * 🎯 Controller层职责：
   * 1. 接收WebSocket请求
   * 2. 调用Service层业务逻辑
   * 3. 将Result转换为统一的MicroserviceResponse格式
   * 4. 返回给网关层
   */
  @MessagePattern('character.create')
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(
    @Payload() payload: {
      createDto: CreateCharacterDto;
      injectedContext?: InjectedContext
    }
  ): Promise<MicroserviceResponse> {
    this.logger.log(`创建角色请求: ${JSON.stringify(payload.createDto)}`);

    // 调用Service层，获取Result
    const result = await this.characterService.createCharacter(payload.createDto);

    // 转换为统一响应格式
    return MicroserviceResponseUtils.fromResult(result, payload.injectedContext?.requestId);
  }
}
```

**关键变化**：
- 返回类型：`Promise<MicroserviceResponse>`
- 响应处理：使用`MicroserviceResponseUtils.fromResult()`统一转换
- 错误处理：不再需要try-catch，Result已包含所有错误信息

#### 2. Service层 (业务逻辑层)

**职责**: Result传递链，业务逻辑处理，错误上下文保持

```typescript
// apps/character/src/modules/character/character.service.ts

@Injectable()
export class CharacterService {

  /**
   * 创建新角色
   *
   * 🎯 Service层职责：
   * 1. 接收Repository层返回的Result
   * 2. 进行Result检查和传递
   * 3. 处理业务逻辑
   * 4. 返回Result给Controller层
   */
  async createCharacter(createDto: CreateCharacterDto): Promise<Result<CharacterDocument>> {
    // 步骤1: 检查角色名是否已存在
    const nameExistsResult = await this.characterRepository.existsByName(
      createDto.name,
      createDto.serverId
    );

    // Result检查：如果Repository调用失败，直接传递错误
    if (!ResultUtils.isSuccess(nameExistsResult)) {
      return nameExistsResult as Result<CharacterDocument>;
    }

    // 业务逻辑：检查角色名是否重复
    if (nameExistsResult.data) {
      return ResultUtils.failure(
        'CHARACTER_NAME_TAKEN',
        '角色名已存在，请选择其他名称'
      );
    }

    // 步骤2: 参数验证
    if (!createDto.characterId) {
      return ResultUtils.failure(
        'INVALID_PARAMETER',
        '角色ID必须由Auth服务提供'
      );
    }

    // 步骤3: 构建角色数据
    const characterData = {
      characterId: createDto.characterId,
      userId: createDto.userId,
      serverId: createDto.serverId,
      openId: createDto.openId,
      name: createDto.name,
      avatar: createDto.avatar || '',
      faceIcon: createDto.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
      cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
      gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
      energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
      loginInfo: {
        createTime: Date.now(),
        loginTime: Date.now(),
      },
    };

    // 步骤4: 创建角色
    const createResult = await this.characterRepository.create(characterData);

    // Result检查和传递
    if (ResultUtils.isSuccess(createResult)) {
      this.logger.log(`角色创建成功: ${createDto.characterId}, 用户: ${createDto.userId}`);
      return ResultUtils.success(createResult.data, '角色创建成功');
    } else {
      this.logger.error('创建角色失败', createResult.message);
      return createResult;
    }
  }
}
```

**关键变化**：
- 返回类型：`Promise<Result<CharacterDocument>>`
- 错误处理：使用Result检查替代try-catch
- Result传递：Repository错误直接传递，不重复创建Result
- 业务错误：使用`ResultUtils.failure()`创建业务错误

#### 3. Repository层 (数据访问层)

**职责**: 数据库操作，异常包装为Result

```typescript
// apps/character/src/common/repositories/character.repository.ts

@Injectable()
export class CharacterRepository {

  /**
   * 检查角色名是否存在
   *
   * 🎯 Repository层职责：
   * 1. 执行数据库操作
   * 2. 将可能的异常包装为Result
   * 3. 返回Result给Service层
   */
  async existsByName(
    name: string,
    serverId: string,
    excludeCharacterId?: string
  ): Promise<Result<boolean>> {
    return await ExceptionToResultUtils.wrapAsync(async () => {
      const filter: FilterQuery<CharacterDocument> = {
        name,
        serverId,
        deletedAt: { $exists: false }
      };

      if (excludeCharacterId) {
        filter.characterId = { $ne: excludeCharacterId };
      }

      const count = await this.characterModel.countDocuments(filter);
      const exists = count > 0;

      this.logger.debug(`检查角色名: ${name}, 服务器: ${serverId}, 存在: ${exists}`);
      return exists;
    });
  }

  /**
   * 创建角色
   */
  async create(createCharacterDto: CreateCharacterDto): Promise<Result<CharacterDocument>> {
    return await ExceptionToResultUtils.wrapAsync(async () => {
      const character = new this.characterModel(createCharacterDto);
      const savedCharacter = await character.save();

      this.logger.log(`角色创建成功: ${savedCharacter.characterId}`);
      return savedCharacter;
    });
  }
}
```

**关键变化**：
- 返回类型：`Promise<Result<T>>`
- 异常处理：使用`ExceptionToResultUtils.wrapAsync()`包装
- 错误映射：自动将MongoDB错误映射为业务错误码
- 日志记录：在Repository层记录操作日志

---

## 🛠️ 异常转Result工具类

### ExceptionToResultUtils

```typescript
/**
 * 异常转Result工具类
 */
export class ExceptionToResultUtils {
  /**
   * 包装异步函数，将异常转换为Result
   */
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    errorCodeMap?: Record<string, string>
  ): Promise<Result<T>> {
    try {
      const data = await fn();
      return ResultUtils.success(data);
    } catch (error) {
      return this.handleError(error, errorCodeMap);
    }
  }

  /**
   * 处理错误并转换为FailureResult
   */
  private static handleError(
    error: any,
    errorCodeMap?: Record<string, string>
  ): FailureResult {
    // 如果已经是Result类型，直接返回
    if (error && typeof error === 'object' && 'success' in error && error.success === false) {
      return error as FailureResult;
    }

    // 处理MongoDB错误
    if (error && error.name === 'MongoError') {
      return this.handleMongoError(error);
    }

    // 处理NestJS异常
    if (error && typeof error === 'object' && error.response) {
      const response = error.response;
      return ResultUtils.failure(
        response.code || error.name || 'HTTP_EXCEPTION',
        response.message || error.message || 'HTTP异常',
        {
          statusCode: error.status,
          originalError: error.name,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      );
    }

    // 使用错误码映射
    if (errorCodeMap && error.message && errorCodeMap[error.message]) {
      return ResultUtils.failure(
        errorCodeMap[error.message],
        error.message,
        { originalError: error.name }
      );
    }

    // 默认错误处理
    return ResultUtils.failure(
      error.code || error.name || 'UNKNOWN_ERROR',
      error.message || '未知错误',
      {
        originalError: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    );
  }

  /**
   * 处理MongoDB特定错误
   */
  private static handleMongoError(error: any): FailureResult {
    switch (error.code) {
      case 11000: // 重复键错误
        return ResultUtils.failure(
          'RESOURCE_ALREADY_EXISTS',
          '数据已存在',
          { duplicateKey: error.keyValue }
        );
      case 121: // 文档验证失败
        return ResultUtils.failure(
          'DOCUMENT_VALIDATION_ERROR',
          '文档验证失败',
          { validationError: error.errInfo }
        );
      default:
        return ResultUtils.failure(
          'DATABASE_ERROR',
          error.message || '数据库操作失败',
          { mongoErrorCode: error.code }
        );
    }
  }
}
```

---

## 🔄 完整响应链流程

### character.create调用链示例

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Controller as Controller层
    participant Service as Service层
    participant Repository as Repository层
    participant Database as 数据库

    Client->>Gateway: WebSocket请求 character.create
    Gateway->>Controller: MessagePattern调用

    Controller->>Service: createCharacter(createDto)

    Service->>Repository: existsByName(name, serverId)
    Repository->>Database: countDocuments查询
    Database-->>Repository: 查询结果
    Repository-->>Service: Result<boolean>

    alt 角色名已存在
        Service-->>Controller: Result<failure> "CHARACTER_NAME_TAKEN"
        Controller-->>Gateway: MicroserviceResponse {code: 5001}
        Gateway-->>Client: WebSocket响应 错误信息
    else 角色名可用
        Service->>Repository: create(characterData)
        Repository->>Database: 保存角色数据
        Database-->>Repository: 保存结果
        Repository-->>Service: Result<CharacterDocument>
        Service-->>Controller: Result<CharacterDocument>
        Controller-->>Gateway: MicroserviceResponse {code: 0, data: character}
        Gateway-->>Client: WebSocket响应 成功数据
    end
```

### 错误传播示例

**场景1: 数据库连接失败**
```
Database Error -> Repository: ExceptionToResultUtils.wrapAsync()
-> Service: Result<failure> "DATABASE_ERROR"
-> Controller: MicroserviceResponse {code: 9000}
-> Client: 明确的数据库错误信息
```

**场景2: 业务逻辑错误**
```
Service: 角色名重复检查
-> Service: ResultUtils.failure("CHARACTER_NAME_TAKEN")
-> Controller: MicroserviceResponse {code: 5001}
-> Client: 明确的业务错误信息
```

**场景3: 参数验证错误**
```
Service: 参数验证失败
-> Service: ResultUtils.failure("INVALID_PARAMETER")
-> Controller: MicroserviceResponse {code: 1001}
-> Client: 明确的参数错误信息
```

---

## 📋 实施指南

### 1. 改造优先级

**阶段1: 核心基础设施**
- [ ] 扩展Result类型定义 (`libs/common/src/types/result.type.ts`)
- [ ] 实现MicroserviceResponseUtils
- [ ] 实现ExceptionToResultUtils

**阶段2: Repository层改造**
- [ ] 更新返回类型为`Promise<Result<T>>`
- [ ] 使用ExceptionToResultUtils包装数据库操作
- [ ] 添加Result相关import

**阶段3: Service层改造**
- [ ] 更新返回类型为`Promise<Result<T>>`
- [ ] 实现Result传递链逻辑
- [ ] 替换try-catch为Result检查

**阶段4: Controller层改造**
- [ ] 更新MessagePattern返回类型为`Promise<MicroserviceResponse>`
- [ ] 使用MicroserviceResponseUtils转换响应
- [ ] 移除try-catch异常处理

### 2. 改造检查清单

**Repository层检查**：
- [ ] 所有方法返回类型为`Promise<Result<T>>`
- [ ] 使用ExceptionToResultUtils包装异步操作
- [ ] 添加必要的import语句
- [ ] 移除throw语句

**Service层检查**：
- [ ] 所有方法返回类型为`Promise<Result<T>>`
- [ ] Repository调用后进行Result检查
- [ ] 使用ResultUtils创建业务错误
- [ ] 移除try-catch块

**Controller层检查**：
- [ ] MessagePattern方法返回`Promise<MicroserviceResponse>`
- [ ] 使用MicroserviceResponseUtils.fromResult()
- [ ] 移除try-catch块
- [ ] 保持缓存装饰器等其他功能

### 3. 测试验证

**编译验证**：
```bash
npm run build:character
```

**功能测试**：
```bash
# 测试正常流程
node scripts/test-character-create.js

# 测试错误场景
node scripts/test-character-create-errors.js
```

**性能测试**：
- 对比改造前后的响应时间
- 验证Result模式的性能优势

---

## 🔧 自动化工具

### 转换工具使用

```bash
# 预览转换效果
node tools/exception-to-result-converter.js apps/character --dry-run --verbose

# 执行实际转换
node tools/exception-to-result-converter.js apps/character --verbose

# 生成转换报告
node tools/exception-to-result-converter.js apps/character --generate-report
```

### 工具配置

转换规则配置文件：`tools/conversion-config.json`

```json
{
  "conversionRules": {
    "repository": {
      "returnTypePattern": "Promise<Result<{originalType}>>",
      "wrapperMethod": "ExceptionToResultUtils.wrapAsync"
    },
    "service": {
      "returnTypePattern": "Promise<Result<{originalType}>>",
      "conversionMode": "result-chain"
    },
    "controller": {
      "returnTypePattern": "Promise<MicroserviceResponse>",
      "responsePattern": "MicroserviceResponseUtils.fromResult"
    }
  }
}
```

---

## 📊 预期收益

### 1. 开发效率提升

- **错误处理标准化**: 统一的Result模式，减少错误处理代码
- **类型安全**: 编译时检查，减少运行时错误
- **调试效率**: 完整的错误上下文，快速定位问题

### 2. 系统稳定性提升

- **错误信息完整**: 客户端获得详细的错误信息
- **性能优化**: 避免异常抛出的性能损耗
- **一致性**: 所有微服务使用统一的错误处理模式

### 3. 维护成本降低

- **代码可读性**: Result模式使错误处理逻辑更清晰
- **测试覆盖**: 更容易编写错误场景的单元测试
- **文档化**: 错误码和消息标准化，便于文档维护

---

## 📝 注意事项

### 1. 向后兼容

- 改造过程中保持API接口不变
- 客户端无需修改，只是获得更好的错误信息
- 网关层可能需要适配新的响应格式

### 2. 性能考虑

- Result对象创建的内存开销
- 避免过深的Result嵌套
- 合理使用缓存减少重复计算

### 3. 团队协作

- 统一的代码规范和最佳实践
- 充分的代码审查确保质量
- 完善的文档和培训

---

## 🔗 相关资源

- [Result类型定义](../libs/common/src/types/result.type.ts)
- [转换工具](../tools/exception-to-result-converter.js)
- [配置文件](../tools/conversion-config.json)
- [测试脚本](../scripts/)

---

**文档维护**: 请在实施过程中及时更新本文档，记录遇到的问题和解决方案。
