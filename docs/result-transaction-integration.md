# Result模式与Mongoose事务处理集成指南

## 🎯 核心问题解答

### 1. Repository层Result改造是否影响事务处理？

**答案：不会影响，但需要适配。**

- ✅ **ACID特性保持不变**：Result包装不改变事务的原子性、一致性、隔离性、持久性
- ✅ **Session传递机制正常**：ClientSession仍然可以在方法间正常传递
- ✅ **事务管理器兼容**：可以与现有的TransactionManager共存
- ⚠️ **需要错误处理适配**：事务异常需要转换为Result格式

### 2. 事务处理的底层原理

#### MongoDB事务底层机制：
1. **Session管理**：每个事务绑定到一个ClientSession
2. **写关注点**：确保写操作的持久性和一致性
3. **读关注点**：控制读操作的隔离级别
4. **两阶段提交**：prepare阶段和commit阶段
5. **冲突检测**：基于文档版本的乐观锁机制

#### Mongoose事务封装：
1. **startSession()**：创建会话
2. **startTransaction()**：开始事务
3. **commitTransaction()**：提交事务
4. **abortTransaction()**：回滚事务
5. **endSession()**：结束会话

## 🔧 集成方案设计

### 方案1：Repository层事务方法适配

```typescript
import { ClientSession } from 'mongoose';
import { Result, RepositoryResultWrapper } from '@libs/common/types/result.type';

@Injectable()
export class CharacterRepository {
  constructor(
    @InjectModel(Character.name) private characterModel: Model<CharacterDocument>
  ) {}

  // ========== 基础方法：支持可选Session ==========
  
  /**
   * 查找角色（支持事务）
   */
  async findById(
    id: string, 
    session?: ClientSession
  ): Promise<Result<CharacterDocument | null>> {
    return await RepositoryResultWrapper.wrapWithSession(async () => {
      const options = session ? { session } : {};
      return await this.characterModel.findById(id, null, options).exec();
    });
  }

  /**
   * 创建角色（支持事务）
   */
  async create(
    dto: CreateCharacterDto,
    session?: ClientSession
  ): Promise<Result<CharacterDocument>> {
    return await RepositoryResultWrapper.wrapWithSession(async () => {
      const options = session ? { session } : {};
      const character = new this.characterModel(dto);
      return await character.save(options);
    });
  }

  /**
   * 更新角色（支持事务）
   */
  async update(
    id: string,
    updateData: Partial<CharacterDocument>,
    session?: ClientSession
  ): Promise<Result<CharacterDocument>> {
    return await RepositoryResultWrapper.wrapWithSession(async () => {
      const options = session ? { session, new: true } : { new: true };
      return await this.characterModel.findByIdAndUpdate(
        id,
        { $set: updateData },
        options
      ).exec();
    });
  }

  // ========== 事务包装方法 ==========

  /**
   * 角色转会事务（完整事务操作）
   */
  async transferCharacterWithTransaction(
    characterId: string,
    newGuildId: string
  ): Promise<Result<CharacterDocument>> {
    return await TransactionResultManager.executeTransaction(async (session) => {
      // 1. 查找角色
      const characterResult = await this.findById(characterId, session);
      const errorResult = ServiceResultHandler.propagateError<CharacterDocument>(characterResult);
      if (errorResult) return errorResult;

      const character = characterResult.data;
      if (!character) {
        return ResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 执行转会
      const updateResult = await this.update(
        characterId,
        { guildId: newGuildId, transferDate: new Date() },
        session
      );

      return updateResult;
    });
  }

  /**
   * 批量操作事务
   */
  async bulkUpdateWithTransaction(
    updates: Array<{ id: string; data: Partial<CharacterDocument> }>
  ): Promise<Result<void>> {
    return await TransactionResultManager.executeTransaction(async (session) => {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { $set: update.data }
        }
      }));

      await this.characterModel.bulkWrite(bulkOps, { session });
      return ResultUtils.empty();
    });
  }
}
```

### 方案2：Service层事务编排

```typescript
@Injectable()
export class CharacterService {
  constructor(
    private readonly characterRepo: CharacterRepository,
    private readonly guildRepo: GuildRepository
  ) {}

  /**
   * 复杂业务事务：角色转会
   */
  async transferCharacter(
    characterId: string,
    fromGuildId: string,
    toGuildId: string
  ): Promise<Result<CharacterDocument>> {
    return await TransactionResultManager.executeWithRetry(async (session) => {
      // 1. 验证角色存在
      const characterResult = await this.characterRepo.findById(characterId, session);
      const characterError = ServiceResultHandler.propagateError<CharacterDocument>(characterResult);
      if (characterError) return characterError;

      const character = characterResult.data;
      if (!character) {
        return ResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 验证目标公会存在
      const guildResult = await this.guildRepo.findById(toGuildId, session);
      const guildError = ServiceResultHandler.propagateError<CharacterDocument>(guildResult);
      if (guildError) return guildError;

      if (!guildResult.data) {
        return ResultUtils.error('目标公会不存在', 'GUILD_NOT_FOUND');
      }

      // 3. 更新角色所属公会
      const updateResult = await this.characterRepo.update(
        characterId,
        { guildId: toGuildId, transferDate: new Date() },
        session
      );
      const updateError = ServiceResultHandler.propagateError<CharacterDocument>(updateResult);
      if (updateError) return updateError;

      // 4. 更新原公会成员数
      const removeResult = await this.guildRepo.removeMember(fromGuildId, characterId, session);
      const removeError = ServiceResultHandler.propagateError<CharacterDocument>(removeResult);
      if (removeError) return removeError;

      // 5. 更新新公会成员数
      const addResult = await this.guildRepo.addMember(toGuildId, characterId, session);
      const addError = ServiceResultHandler.propagateError<CharacterDocument>(addResult);
      if (addError) return addError;

      return updateResult;
    }, 3); // 最多重试3次
  }

  /**
   * 批量角色操作事务
   */
  async batchProcessCharacters(
    operations: Array<CharacterOperation>
  ): Promise<Result<void>> {
    return await TransactionResultManager.executeTransaction(async (session) => {
      for (const operation of operations) {
        let result: Result<any>;

        switch (operation.type) {
          case 'CREATE':
            result = await this.characterRepo.create(operation.data, session);
            break;
          case 'UPDATE':
            result = await this.characterRepo.update(operation.id, operation.data, session);
            break;
          case 'DELETE':
            result = await this.characterRepo.delete(operation.id, session);
            break;
          default:
            return ResultUtils.error('未知操作类型', 'UNKNOWN_OPERATION');
        }

        // 检查每个操作的结果
        const errorResult = ServiceResultHandler.propagateError<void>(result);
        if (errorResult) return errorResult;
      }

      return ResultUtils.empty();
    });
  }
}
```

### 方案3：Controller层事务调用

```typescript
@Controller('character')
export class CharacterController {
  constructor(private readonly characterService: CharacterService) {}

  @MessagePattern('character.transfer')
  async transferCharacter(@Payload() payload: {
    characterId: string;
    fromGuildId: string;
    toGuildId: string;
    injectedContext?: InjectedContext;
  }): Promise<MicroserviceResponse<CharacterDocument>> {
    const result = await this.characterService.transferCharacter(
      payload.characterId,
      payload.fromGuildId,
      payload.toGuildId
    );
    
    return MicroserviceResponseUtils.fromResult(result);
  }

  @MessagePattern('character.batchProcess')
  async batchProcessCharacters(@Payload() payload: {
    operations: Array<CharacterOperation>;
    injectedContext?: InjectedContext;
  }): Promise<MicroserviceResponse<void>> {
    const result = await this.characterService.batchProcessCharacters(payload.operations);
    
    return MicroserviceResponseUtils.fromResult(result);
  }
}
```

## 🔍 事务处理底层原理详解

### 1. MongoDB事务机制

#### 1.1 会话管理
```typescript
// 底层原理
const session = await mongoose.startSession();
// 创建一个逻辑会话，包含：
// - sessionId: 唯一标识符
// - txnNumber: 事务编号
// - readConcern: 读关注点
// - writeConcern: 写关注点
```

#### 1.2 事务状态机
```
[开始] -> startTransaction() -> [活跃]
[活跃] -> commitTransaction() -> [已提交]
[活跃] -> abortTransaction() -> [已中止]
[已提交/已中止] -> endSession() -> [结束]
```

#### 1.3 冲突检测机制
```typescript
// MongoDB使用文档版本进行冲突检测
{
  _id: ObjectId("..."),
  data: "...",
  __v: 5  // 版本号，每次更新递增
}

// 当两个事务同时修改同一文档时：
// 1. 第一个事务提交成功，版本号变为6
// 2. 第二个事务基于版本5的修改被拒绝
// 3. 第二个事务收到WriteConflict错误
```

### 2. Mongoose事务封装

#### 2.1 Session传递机制
```typescript
// Mongoose通过options传递session
const options = { session };
await Model.findById(id, null, options);
await Model.updateOne(filter, update, options);
await document.save(options);
```

#### 2.2 错误处理机制
```typescript
// Mongoose事务错误类型
try {
  await session.commitTransaction();
} catch (error) {
  if (error.code === 11000) {
    // 重复键错误
  } else if (error.code === 112) {
    // 写冲突错误
  } else if (error.code === 244) {
    // 事务冲突错误
  }
  await session.abortTransaction();
}
```

### 3. Result模式集成原理

#### 3.1 错误转换机制
```typescript
// 原始事务错误处理
try {
  const result = await operation(session);
  await session.commitTransaction();
  return result;
} catch (error) {
  await session.abortTransaction();
  throw error; // 抛出异常
}

// Result模式错误处理
try {
  const result = await operation(session);
  if (ResultUtils.isFailure(result)) {
    await session.abortTransaction();
    return result; // 返回Result
  }
  await session.commitTransaction();
  return result;
} catch (error) {
  await session.abortTransaction();
  return ResultUtils.error(error.message, 'TRANSACTION_ERROR');
}
```

#### 3.2 事务传播机制
```typescript
// Result模式下的事务传播
async parentOperation(): Promise<Result<T>> {
  return await TransactionResultManager.executeTransaction(async (session) => {
    // 子操作1
    const result1 = await this.childOperation1(session);
    const failureResult1 = ServiceResultHandler.propagateError<T>(result1);
    if (failureResult1) return failureResult1; // 如果失败，返回错误并自动回滚

    // 子操作2
    const result2 = await this.childOperation2(session);
    const failureResult2 = ServiceResultHandler.propagateError<T>(result2);
    if (failureResult2) return failureResult2; // 如果失败，返回错误并自动回滚

    // 所有操作都成功，返回最终结果
    return ResultUtils.ok(finalResult);
  });
}

// 📝 propagateError逻辑说明：
// - 如果result成功 → propagateError返回null → if条件为false → 继续执行
// - 如果result失败 → propagateError返回失败的Result → if条件为true → 返回错误，触发回滚
```

## 📋 转换清单

### Repository层转换清单
- [ ] 为所有数据库操作方法添加可选的`session?: ClientSession`参数
- [ ] 使用`RepositoryResultWrapper.wrapWithSession()`包装操作
- [ ] 创建事务包装方法（如`xxxWithTransaction`）
- [ ] 实现批量操作的事务支持

### Service层转换清单
- [ ] 使用`TransactionResultManager.executeTransaction()`执行事务
- [ ] 在事务内部使用`ServiceResultHandler.propagateError()`检查结果
- [ ] 为复杂业务流程添加重试机制
- [ ] 实现事务的错误恢复逻辑

### 注意事项
- ✅ **保持Session传递**：确保所有事务内的操作都使用同一个session
- ✅ **错误及时检查**：每个Repository调用后立即检查Result
- ✅ **避免嵌套事务**：MongoDB不支持嵌套事务
- ✅ **合理的重试策略**：只对可重试的错误进行重试
- ⚠️ **性能考虑**：事务会增加延迟，避免长时间持有事务

## 🎯 总结

Result模式与Mongoose事务处理的集成是完全可行的，主要优势：

1. **类型安全**：事务错误也成为类型系统的一部分
2. **统一处理**：事务错误和业务错误使用相同的处理模式
3. **易于测试**：Result模式使事务逻辑更容易进行单元测试
4. **向后兼容**：可以与现有的事务代码共存

通过`TransactionResultManager`和相关工具类，开发者可以轻松地将现有的事务代码迁移到Result模式，同时保持所有事务的ACID特性。
