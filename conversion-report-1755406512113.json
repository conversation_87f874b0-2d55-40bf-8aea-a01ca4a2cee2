{"timestamp": "2025-08-17T04:55:12.113Z", "stats": {"filesProcessed": 5, "repositoryFiles": 5, "serviceFiles": 0, "controllerFiles": 0, "throwsConverted": 70, "functionsUpdated": 71, "messagePatternUpdated": 0, "importsAdded": 5, "errors": []}, "conversions": [{"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByUserId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByName", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByOpenId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "softDelete", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "existsByName", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findWithPagination", "changes": ["使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByLevelRange", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "getOnlineCount", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "bulkUpdate", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "getServerStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "createInternal", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findFormation", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findActiveFormation", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "updateFormation", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "delete", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "setActiveFormation", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findWithPagination", "changes": ["使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findByFormationType", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "findFormationsWithHero", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "getCharacterFormationStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "bulkUpdate", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "copyFormation", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/formation.repository.ts", "layer": "repository", "className": "FormationRepository", "methodName": "existsByName", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "findById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "findByCharacterAndType", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "delete", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "addItem", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "removeItem", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "updateItem", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "findItemInInventory", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "findItemsByConfigId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "expandCapacity", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "sortInventory", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "cleanExpiredItems", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "getInventoryStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "bulkUpdate", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/inventory.repository.ts", "layer": "repository", "className": "InventoryRepository", "methodName": "exists", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "findItemDefinitionById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "searchItemDefinitions", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "findItemById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "updateItem", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "deleteItem", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "findItemsByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "findItemsByConfigId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "bulkUpdateItems", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/item.repository.ts", "layer": "repository", "className": "ItemRepository", "methodName": "getCharacterItemStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "findById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "findByCharacterIdAndTacticKey", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "findActiveTacticsByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "batchUpdateStatus", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "delete", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "countByCharacterId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "findUpgradeableTactics", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/tactic.repository.ts", "layer": "repository", "className": "TacticRepository", "methodName": "updateUsageStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}], "errors": []}