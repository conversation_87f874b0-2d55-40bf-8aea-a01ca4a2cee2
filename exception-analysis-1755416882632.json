{"totalFiles": 459, "filesWithThrows": 135, "totalThrows": 1223, "fileStats": [{"path": "apps\\hero\\src\\modules\\hero\\hero.service.ts", "throwCount": 67, "throws": [{"line": 46, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 79, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 94, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 133, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 161, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 172, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 181, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 192, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 204, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 226, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 237, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 250, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 261, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 277, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 288, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 304, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 315, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 325, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_BREAKTHROUGH_LIMIT,\r\n          message: ErrorMessages[ErrorCode.HERO_BREAKTHROUGH_LIMIT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 394, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 405, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 413, "text": "throw new BadRequestException({\r\n          code: ErrorCode.NO_BREAKTHROUGH_TO_REVERT,\r\n          message: ErrorMessages[ErrorCode.NO_BREAKTHROUGH_TO_REVERT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 422, "text": "throw new BadRequestException({\r\n          code: ErrorCode.ALL_BREAKTHROUGH_PERFECT,\r\n          message: ErrorMessages[ErrorCode.ALL_BREAKTHROUGH_PERFECT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 459, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 470, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 478, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_TRAINING_COOLDOWN,\r\n          message: ErrorMessages[ErrorCode.HERO_TRAINING_COOLDOWN],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 486, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_IN_TRAINING,\r\n          message: ErrorMessages[ErrorCode.HERO_IN_TRAINING],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 507, "text": "throw new BadRequestException({\r\n            code: ErrorCode.INVALID_PARAMETER,\r\n            message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\r\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 541, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 552, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 562, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_CANNOT_EVOLVE,\r\n          message: ErrorMessages[ErrorCode.HERO_CANNOT_EVOLVE],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 570, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_MAX_STAR,\r\n          message: ErrorMessages[ErrorCode.HERO_MAX_STAR],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 579, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_MATERIAL,\r\n          message: ErrorMessages[ErrorCode.INSUFFICIENT_MATERIAL],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 592, "text": "throw new BadRequestException({\r\n            code: ErrorCode.INVALID_MATERIAL_HERO,\r\n            message: ErrorMessages[ErrorCode.INVALID_MATERIAL_HERO],\r\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 683, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 984, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1006, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1344, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1361, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1372, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1392, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1403, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1410, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_IN_TREATMENT,\r\n          message: ErrorMessages[ErrorCode.HERO_IN_TREATMENT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 1445, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1456, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1464, "text": "throw new BadRequestException({\r\n          code: ErrorCode.CONTRACT_NOT_EXPIRED,\r\n          message: ErrorMessages[ErrorCode.CONTRACT_NOT_EXPIRED],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 1492, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1601, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1635, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1771, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1796, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1810, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 1857, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1875, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1940, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2558, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 2566, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_ALREADY_RETIRED,\r\n          message: ErrorMessages[ErrorCode.HERO_ALREADY_RETIRED],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 2575, "text": "throw new BadRequestException({\r\n          code: ErrorCode.HERO_MAX_CAREER,\r\n          message: ErrorMessages[ErrorCode.HERO_MAX_CAREER],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 2610, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2655, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2689, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2700, "text": "throw new NotFoundException({\r\n          code: ErrorCode.HERO_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 2731, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2784, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2881, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2946, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 2970, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\social\\src\\modules\\guild\\guild.service.ts", "throwCount": 54, "throws": [{"line": 59, "text": "throw new BadRequestException({\r\n          code: ErrorCode.ALREADY_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 68, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NAME_EXISTS,\r\n          message: ErrorMessages[ErrorCode.GUILD_NAME_EXISTS],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 124, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 145, "text": "throw new BadRequestException({\r\n          code: ErrorCode.ALREADY_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 154, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 162, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_FULL,\r\n          message: ErrorMessages[ErrorCode.GUILD_FULL],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 171, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_APPLICATION_EXISTS,\r\n          message: ErrorMessages[ErrorCode.GUILD_APPLICATION_EXISTS],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 204, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 221, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 230, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 239, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_APPLICATION_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_APPLICATION_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 251, "text": "throw new BadRequestException({\r\n            code: ErrorCode.GUILD_FULL,\r\n            message: ErrorMessages[ErrorCode.GUILD_FULL],\r\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 295, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 307, "text": "throw new NotFoundException({\r\n          code: ErrorCode.NOT_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.NOT_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 316, "text": "throw new NotFoundException({\r\n          code: ErrorCode.NOT_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.NOT_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 324, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE,\r\n          message: ErrorMessages[ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 347, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 358, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 384, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 395, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 424, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 435, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 444, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 474, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 485, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 494, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 503, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_MEMBER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_MEMBER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 511, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_CANNOT_KICK_PRESIDENT,\r\n          message: ErrorMessages[ErrorCode.GUILD_CANNOT_KICK_PRESIDENT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 519, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 541, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 572, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 580, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 597, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 609, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 617, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 625, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_MEMBER_LIMIT,\r\n          message: ErrorMessages[ErrorCode.GUILD_MEMBER_LIMIT],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 634, "text": "throw new NotFoundException({\r\n          code: ErrorCode.APPLICATION_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 681, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 693, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 701, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 710, "text": "throw new NotFoundException({\r\n          code: ErrorCode.APPLICATION_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 732, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 744, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 754, "text": "throw new BadRequestException({\r\n          code: ErrorCode.CHARACTER_NOT_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 762, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 770, "text": "throw new BadRequestException({\r\n          code: ErrorCode.GUILD_NO_PERMISSION,\r\n          message: ErrorMessages[ErrorCode.GUILD_NO_PERMISSION],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 788, "text": "throw new BadRequestException({\r\n            code: ErrorCode.VICE_PRESIDENT_LIMIT,\r\n            message: ErrorMessages[ErrorCode.VICE_PRESIDENT_LIMIT],\r\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 828, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 840, "text": "throw new NotFoundException({\r\n          code: ErrorCode.GUILD_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.GUILD_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 849, "text": "throw new BadRequestException({\r\n          code: ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER,\r\n          message: ErrorMessages[ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 857, "text": "throw new BadRequestException({\r\n          code: ErrorCode.CHARACTER_NOT_IN_GUILD,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 892, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 925, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 957, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\services\\jwt.service.ts", "throwCount": 51, "throws": [{"line": 214, "text": "throw new UnauthorizedException('令牌不能为空');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 220, "text": "throw new UnauthorizedException('令牌格式不正确');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 225, "text": "throw new UnauthorizedException('令牌已被撤销');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 248, "text": "throw new UnauthorizedException('令牌已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 250, "text": "throw new UnauthorizedException('无效的令牌');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 252, "text": "throw new UnauthorizedException('令牌尚未生效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 255, "text": "throw new UnauthorizedException('令牌验证失败');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 265, "text": "throw new UnauthorizedException('令牌缺少用户标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 269, "text": "throw new UnauthorizedException('令牌缺少唯一标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 274, "text": "throw new UnauthorizedException('令牌类型错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 280, "text": "throw new UnauthorizedException('令牌签发时间无效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 285, "text": "throw new UnauthorizedException('令牌已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 296, "text": "throw new UnauthorizedException('刷新令牌已被撤销');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 311, "text": "throw new UnauthorizedException('刷新令牌已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 313, "text": "throw new UnauthorizedException('无效的刷新令牌');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 316, "text": "throw new UnauthorizedException('刷新令牌验证失败');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 446, "text": "throw new Error(`无效的时间格式: ${timeStr}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 457, "text": "throw new Error(`不支持的时间单位: ${unit}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 510, "text": "throw new UnauthorizedException('角色令牌不能为空');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 516, "text": "throw new UnauthorizedException('角色令牌格式不正确');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 521, "text": "throw new UnauthorizedException('角色令牌已被撤销');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 547, "text": "throw new UnauthorizedException('角色令牌已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 549, "text": "throw new UnauthorizedException('无效的角色令牌');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 551, "text": "throw new UnauthorizedException('角色令牌尚未生效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 554, "text": "throw new UnauthorizedException('角色令牌验证失败');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 587, "text": "throw new BadRequestException('用户标识不能为空');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 591, "text": "throw new BadRequestException('用户名不能为空且必须为字符串');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 595, "text": "throw new BadRequestException('邮箱不能为空且必须为字符串');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 599, "text": "throw new BadRequestException('角色标识不能为空且必须为字符串');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 603, "text": "throw new BadRequestException('服务器标识不能为空且必须为字符串');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 607, "text": "throw new BadRequestException('会话标识不能为空且必须为字符串');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 611, "text": "throw new BadRequestException('角色列表必须为数组');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 615, "text": "throw new BadRequestException('权限列表必须为数组');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 619, "text": "throw new BadRequestException('令牌作用域必须为character或未指定');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 630, "text": "throw new UnauthorizedException('角色令牌缺少用户标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 634, "text": "throw new UnauthorizedException('角色令牌缺少用户名');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 638, "text": "throw new UnauthorizedException('角色令牌缺少邮箱');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 642, "text": "throw new UnauthorizedException('角色令牌缺少会话标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 647, "text": "throw new UnauthorizedException('角色令牌缺少角色标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 651, "text": "throw new UnauthorizedException('角色令牌缺少服务器标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 655, "text": "throw new UnauthorizedException('令牌作用域不正确，期望角色令牌');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 660, "text": "throw new UnauthorizedException('角色令牌角色列表格式不正确');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 664, "text": "throw new UnauthorizedException('角色令牌权限列表格式不正确');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 669, "text": "throw new UnauthorizedException('角色令牌签发时间无效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 674, "text": "throw new UnauthorizedException('角色令牌缺少唯一标识');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 684, "text": "throw new UnauthorizedException('令牌缺少作用域信息');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 688, "text": "throw new UnauthorizedException(`令牌作用域不匹配，期望: ${expectedScope}, 实际: ${payload.scope}`);", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 696, "text": "throw new UnauthorizedException('角色令牌缺少必要的角色或区服信息');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 702, "text": "throw new UnauthorizedException('账号令牌不应包含角色或区服信息');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 706, "text": "throw new UnauthorizedException(`不支持的令牌作用域: ${expectedScope}`);", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 727, "text": "throw new UnauthorizedException('无效的令牌：既不是账号令牌也不是角色令牌');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "simple", "priority": "low"}, {"path": "apps\\character\\src\\modules\\character\\character.service.ts", "throwCount": 39, "throws": [{"line": 48, "text": "throw new BadRequestException({\r\n          code: ErrorCode.CHARACTER_NAME_TAKEN,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 56, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INVALID_PARAMETER,\r\n          message: '角色ID必须由Auth服务提供',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 86, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 136, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 168, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 205, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 226, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 237, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 246, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 257, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 272, "text": "throw new BadRequestException({\r\n            code: ErrorCode.CHARACTER_NAME_TAKEN,\r\n            message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],\r\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 285, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 327, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 338, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 358, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 369, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 377, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_CURRENCY,\r\n          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 397, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 408, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 420, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_CURRENCY,\r\n          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 446, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 457, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 467, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INVALID_PARAMETER,\r\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 499, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 510, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 529, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 540, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 566, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 669, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 682, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 693, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 701, "text": "throw new BadRequestException({\r\n          code: ErrorCode.REDEEM_CODE_ALREADY_USED,\r\n          message: ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 723, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 734, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 751, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1849, "text": "throw new Error('角色不存在');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 1878, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1892, "text": "throw new Error('角色不存在');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 1905, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\user\\services\\users.service.ts", "throwCount": 27, "throws": [{"line": 36, "text": "throw new BadRequestException('密码确认不匹配');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 42, "text": "throw new ConflictException('用户名已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 48, "text": "throw new ConflictException('邮箱已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 55, "text": "throw new ConflictException('手机号已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 71, "text": "throw new BadRequestException({\r\n        message: '密码不符合安全要求',\r\n        errors: passwordValidation.errors\r\n      });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 124, "text": "throw new BadRequestException('创建用户失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 148, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 170, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 194, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 216, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 240, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 262, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 272, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 289, "text": "throw new ConflictException('邮箱已被其他用户使用');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 299, "text": "throw new ConflictException('手机号已被其他用户使用');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 307, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 331, "text": "throw new BadRequestException('新密码确认不匹配');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 336, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 346, "text": "throw new UnauthorizedException('当前密码错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 361, "text": "throw new BadRequestException({\r\n        message: '新密码不符合安全要求',\r\n        errors: passwordValidation.errors\r\n      });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 374, "text": "throw new BadRequestException('新密码不能与最近使用的密码相同');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 463, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 494, "text": "throw new UnauthorizedException('账户已被暂停');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 498, "text": "throw new UnauthorizedException('账户未激活');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 502, "text": "throw new UnauthorizedException('账户不存在');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 507, "text": "throw new UnauthorizedException(`账户已被锁定，请在${lockTime}分钟后重试`);", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 554, "text": "throw new Error('findByResetToken method not implemented');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\admin\\services\\user-management.service.ts", "throwCount": 25, "throws": [{"line": 94, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 112, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 118, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 136, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 168, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 185, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 191, "text": "throw new BadRequestException('部分角色不存在');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 218, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 236, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 268, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 286, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 323, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 336, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 365, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 378, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 403, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 431, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 452, "text": "throw new NotFoundException('会话不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 477, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 500, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 513, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 541, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 554, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 558, "text": "throw new BadRequestException('用户未被删除');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 580, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\modules\\skill\\skill.service.ts", "throwCount": 24, "throws": [{"line": 40, "text": "throw new NotFoundException({\n          code: ErrorCode.SKILL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.SKILL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 49, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 115, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 143, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 162, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 214, "text": "throw new NotFoundException({\n          code: ErrorCode.SKILL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.SKILL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 223, "text": "throw new BadRequestException({\n          code: ErrorCode.SKILL_ALREADY_LEARNED,\n          message: ErrorMessages[ErrorCode.SKILL_ALREADY_LEARNED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 232, "text": "throw new BadRequestException({\n          code: ErrorCode.HERO_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 262, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 273, "text": "throw new NotFoundException({\n          code: ErrorCode.HERO_SKILL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 281, "text": "throw new BadRequestException({\n          code: ErrorCode.SKILL_CANNOT_UPGRADE,\n          message: ErrorMessages[ErrorCode.SKILL_CANNOT_UPGRADE],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 289, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_UPGRADE_LEVEL,\n          message: ErrorMessages[ErrorCode.INVALID_UPGRADE_LEVEL],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 297, "text": "throw new BadRequestException({\n          code: ErrorCode.EXCEED_MAX_LEVEL,\n          message: ErrorMessages[ErrorCode.EXCEED_MAX_LEVEL],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 309, "text": "throw new BadRequestException({\n          code: ErrorCode.INSUFFICIENT_HEROES,\n          message: resourceCheckResult.message,\n          data: {\n            required: resourceCheckResult.required,\n            current: resourceCheckResult.current,\n          },\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 336, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 347, "text": "throw new NotFoundException({\n          code: ErrorCode.HERO_SKILL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 355, "text": "throw new BadRequestException({\n          code: ErrorCode.SKILL_IS_LOCKED,\n          message: ErrorMessages[ErrorCode.SKILL_IS_LOCKED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 379, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 390, "text": "throw new NotFoundException({\n          code: ErrorCode.HERO_SKILL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 407, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 420, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 433, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 445, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\social\\src\\modules\\friend\\friend.service.ts", "throwCount": 24, "throws": [{"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 65, "text": "throw new BadRequestException({\n          code: ErrorCode.CANNOT_ADD_SELF,\n          message: ErrorMessages[ErrorCode.CANNOT_ADD_SELF],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 79, "text": "throw new BadRequestException({\n          code: ErrorCode.FRIEND_ALREADY_EXISTS,\n          message: ErrorMessages[ErrorCode.FRIEND_ALREADY_EXISTS],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 87, "text": "throw new BadRequestException({\n          code: ErrorCode.FRIEND_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.FRIEND_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 96, "text": "throw new BadRequestException({\n          code: ErrorCode.FRIEND_REQUEST_ALREADY_SENT,\n          message: ErrorMessages[ErrorCode.FRIEND_REQUEST_ALREADY_SENT],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 135, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 151, "text": "throw new NotFoundException({\n          code: ErrorCode.FRIEND_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.FRIEND_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 161, "text": "throw new BadRequestException({\n            code: ErrorCode.FRIEND_LIMIT_REACHED,\n            message: ErrorMessages[ErrorCode.FRIEND_LIMIT_REACHED],\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 224, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 243, "text": "throw new NotFoundException({\n          code: ErrorCode.FRIEND_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.FRIEND_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 259, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 272, "text": "throw new BadRequestException({\n          code: ErrorCode.FRIEND_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.FRIEND_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 288, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 330, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 346, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 358, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 371, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 410, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 437, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 469, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 507, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 522, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 556, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 613, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\common\\repositories\\hero.repository.ts", "throwCount": 23, "throws": [{"line": 38, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 50, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 62, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 100, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 120, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 142, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 222, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 251, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 280, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 299, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 346, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 368, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 423, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 453, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 467, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 482, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 503, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 527, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 545, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 558, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 594, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 609, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\modules\\exchange\\exchange.service.ts", "throwCount": 22, "throws": [{"line": 61, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 72, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 86, "text": "throw new BadRequestException({\n          code: ErrorCode.ITEM_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 94, "text": "throw new BadRequestException({\n          code: ErrorCode.ITEM_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 123, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 134, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 145, "text": "throw new BadRequestException({\n          code: ErrorCode.ITEM_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 154, "text": "throw new BadRequestException({\n          code: ErrorCode.ITEM_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 198, "text": "throw new BadRequestException({\n          code: ErrorCode.REFRESH_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.REFRESH_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 212, "text": "throw new BadRequestException({\n          code: ErrorCode.CHIP_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 219, "text": "throw new BadRequestException({\n          code: ErrorCode.GOLD_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.GOLD_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 251, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 262, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 273, "text": "throw new BadRequestException({\n          code: ErrorCode.EXCHANGE_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 281, "text": "throw new BadRequestException({\n          code: ErrorCode.ITEM_ALREADY_BOUGHT,\n          message: ErrorMessages[ErrorCode.ITEM_ALREADY_BOUGHT],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 290, "text": "throw new BadRequestException({\n          code: ErrorCode.CHIP_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 320, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 624, "text": "throw new Error(`扣除物品失败: ${result.message || '未知错误'}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 630, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 648, "text": "throw new Error(`添加物品失败: ${result.message || '未知错误'}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 654, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\task\\task.service.ts", "throwCount": 21, "throws": [{"line": 71, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 84, "text": "throw new BadRequestException({\n          code: ErrorCode.TASK_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.TASK_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 107, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 152, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 165, "text": "throw new BadRequestException({\n          code: ErrorCode.TASK_NOT_COMPLETED,\n          message: ErrorMessages[ErrorCode.TASK_NOT_COMPLETED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 212, "text": "throw new BadRequestException({\n            code: ErrorCode.ACTIVITY_NOT_FOUND,\n            message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 231, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 251, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 269, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 281, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 308, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 320, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 425, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 441, "text": "throw new NotFoundException({\n          code: ErrorCode.TASK_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.TASK_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 469, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 488, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 503, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 785, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 877, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 918, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\modules\\relay\\relay.service.ts", "throwCount": 21, "throws": [{"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 57, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_ALREADY_JOINED,\n          message: ErrorMessages[ErrorCode.RELAY_ALREADY_JOINED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 65, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_EXPIRED,\n          message: ErrorMessages[ErrorCode.RELAY_EXPIRED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 76, "text": "throw new BadRequestException({\n          code: ErrorCode.DIAMOND_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.DIAMOND_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 109, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 120, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 130, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_NOT_JOINED,\n          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 138, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_EXPIRED,\n          message: ErrorMessages[ErrorCode.RELAY_EXPIRED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 147, "text": "throw new BadRequestException({\n          code: ErrorCode.EXCHANGE_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 155, "text": "throw new BadRequestException({\n          code: ErrorCode.INTEGRAL_NOT_ENOUGH,\n          message: ErrorMessages[ErrorCode.INTEGRAL_NOT_ENOUGH],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 182, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 193, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 203, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_NOT_JOINED,\n          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 211, "text": "throw new BadRequestException({\n          code: ErrorCode.AWARD_NOT_AVAILABLE,\n          message: ErrorMessages[ErrorCode.AWARD_NOT_AVAILABLE],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 219, "text": "throw new NotFoundException({\n          code: ErrorCode.AWARD_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.AWARD_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 231, "text": "throw new BadRequestException({\n          code: ErrorCode.AWARD_RECEIVE_FAILED,\n          message: ErrorMessages[ErrorCode.AWARD_RECEIVE_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 249, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 263, "text": "throw new BadRequestException({\n          code: ErrorCode.RELAY_NOT_JOINED,\n          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 271, "text": "throw new BadRequestException({\n          code: ErrorCode.NO_AWARDS_AVAILABLE,\n          message: ErrorMessages[ErrorCode.NO_AWARDS_AVAILABLE],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 308, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 338, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\economy\\src\\modules\\shop\\shop.service.ts", "throwCount": 21, "throws": [{"line": 38, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 52, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CONFIG_NOT_FOUND,\r\n          message: `商品配置不存在: ${purchaseDto.goodsId}`,\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 61, "text": "throw new BadRequestException({\r\n          code: ErrorCode.TIME_LIMIT_EXCEEDED,\r\n          message: '商品不在销售时间内',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 70, "text": "throw new BadRequestException({\r\n          code: ErrorCode.LEVEL_REQUIREMENT_NOT_MET,\r\n          message: 'VIP等级或玩家等级不足',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 79, "text": "throw new BadRequestException({\r\n          code: ErrorCode.PURCHASE_LIMIT_EXCEEDED,\r\n          message: '超过购买限制',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 89, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_CURRENCY,\r\n          message: errorMessage,\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 104, "text": "throw new BadRequestException({\r\n          code: ErrorCode.DELIVERY_FAILED,\r\n          message: '发货失败',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 136, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 156, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 170, "text": "throw new BadRequestException({\r\n          code: ErrorCode.PAYMENT_VERIFICATION_FAILED,\r\n          message: '支付验证失败',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 179, "text": "throw new NotFoundException({\r\n          code: ErrorCode.CONFIG_NOT_FOUND,\r\n          message: `月卡配置不存在: ${buyDto.cardType}`,\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 187, "text": "throw new BadRequestException({\r\n          code: ErrorCode.INVALID_PARAMETER,\r\n          message: '购买天数与配置不匹配',\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 228, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 239, "text": "throw new NotFoundException({\r\n          code: ErrorCode.SHOP_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.SHOP_NOT_FOUND],\r\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 246, "text": "throw new BadRequestException({\r\n          code: ErrorCode.MONTH_CARD_REWARD_CLAIMED,\r\n          message: ErrorMessages[ErrorCode.MONTH_CARD_REWARD_CLAIMED],\r\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 267, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 280, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 292, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 304, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 331, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1098, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\social\\src\\common\\repositories\\guild.repository.ts", "throwCount": 21, "throws": [{"line": 31, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 69, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 94, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 115, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 157, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 185, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 203, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 222, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 241, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 260, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 279, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 304, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 332, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 363, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 383, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 395, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 417, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 439, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\guide.repository.ts", "throwCount": 19, "throws": [{"line": 31, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 99, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 118, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 140, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 159, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 178, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 197, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 209, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 228, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 245, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 316, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 345, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 366, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 383, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 397, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 414, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\modules\\mail\\mail.service.ts", "throwCount": 19, "throws": [{"line": 75, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 91, "text": "throw new NotFoundException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 99, "text": "throw new BadRequestException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 120, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 132, "text": "throw new NotFoundException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 140, "text": "throw new BadRequestException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 148, "text": "throw new BadRequestException({\n          code: ErrorCode.MAIL_NO_ATTACHMENT,\n          message: ErrorMessages[ErrorCode.MAIL_NO_ATTACHMENT],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 185, "text": "throw new NotFoundException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 193, "text": "throw new BadRequestException({\n          code: ErrorCode.MAIL_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.MAIL_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 202, "text": "throw new BadRequestException({\n          code: ErrorCode.MAIL_DELETE_FAILED,\n          message: ErrorMessages[ErrorCode.MAIL_DELETE_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 218, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 270, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 314, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 328, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 355, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 441, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 470, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 506, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\event.repository.ts", "throwCount": 18, "throws": [{"line": 31, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 107, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 133, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 155, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 177, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 198, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 215, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 227, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 275, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 305, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 326, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 343, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 357, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 371, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 395, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\common\\repositories\\formation.repository.ts", "throwCount": 18, "throws": [{"line": 34, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 50, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 86, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 99, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 127, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 142, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 165, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 186, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 199, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 226, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 289, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 315, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 338, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 396, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 418, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 429, "text": "throw new Error('源阵容不存在');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 454, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 476, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\common\\repositories\\inventory.repository.ts", "throwCount": 17, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 58, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 73, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 94, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 114, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 127, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 150, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 199, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 220, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 241, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 266, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 328, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 373, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 423, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 445, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 458, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\sign.repository.ts", "throwCount": 16, "throws": [{"line": 31, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 108, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 130, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 152, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 171, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 188, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 200, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 252, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 268, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 289, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 306, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 321, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 339, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\common\\repositories\\mail.repository.ts", "throwCount": 16, "throws": [{"line": 31, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 43, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 71, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 89, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 104, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 129, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 144, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 159, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 176, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 194, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 209, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 221, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 240, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 255, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 270, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 291, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\user\\repositories\\user.repository.ts", "throwCount": 16, "throws": [{"line": 35, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 53, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 74, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 95, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 110, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 134, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 150, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 170, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 191, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 204, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 320, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 337, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 390, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 407, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 424, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 441, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\energy\\energy.service.ts", "throwCount": 15, "throws": [{"line": 46, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 60, "text": "throw new BadRequestException({\n          code: ErrorCode.ENERGY_TIME_INVALID,\n          message: ErrorMessages[ErrorCode.ENERGY_TIME_INVALID],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 68, "text": "throw new BadRequestException({\n          code: ErrorCode.ENERGY_ALREADY_TAKEN,\n          message: ErrorMessages[ErrorCode.ENERGY_ALREADY_TAKEN],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 80, "text": "throw new BadRequestException({\n          code: ErrorCode.ENERGY_TAKE_FAILED,\n          message: ErrorMessages[ErrorCode.ENERGY_TAKE_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 99, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 112, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 133, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 146, "text": "throw new BadRequestException({\n          code: ErrorCode.INVALID_PARAMETER,\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 154, "text": "throw new BadRequestException({\n          code: ErrorCode.REWARD_NOT_AVAILABLE,\n          message: ErrorMessages[ErrorCode.REWARD_NOT_AVAILABLE],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 163, "text": "throw new BadRequestException({\n          code: ErrorCode.REWARD_CLAIM_FAILED,\n          message: ErrorMessages[ErrorCode.REWARD_CLAIM_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 198, "text": "throw new BadRequestException({\n          code: ErrorCode.GIFT_BUY_LIMIT_REACHED,\n          message: ErrorMessages[ErrorCode.GIFT_BUY_LIMIT_REACHED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 208, "text": "throw new BadRequestException({\n          code: ErrorCode.GIFT_ALREADY_BOUGHT_TODAY,\n          message: ErrorMessages[ErrorCode.GIFT_ALREADY_BOUGHT_TODAY],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 233, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 276, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\gateway\\src\\infra\\auth\\guards\\ws-auth.guard.ts", "throwCount": 15, "throws": [{"line": 55, "text": "throw new WsException('Authentication required');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 86, "text": "throw new WsException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${validationResult.tokenScope}`);", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 101, "text": "throw new WsException('Insufficient permissions');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 111, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 113, "text": "throw new WsException('Authentication failed');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 170, "text": "throw new WsException('Invalid token format');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 177, "text": "throw new WsException('Invalid token format');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 191, "text": "throw new WsException(`Unsupported token scope: ${decoded.scope}`);", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 197, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 199, "text": "throw new WsException('Token validation failed');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 216, "text": "throw new WsException(result.error || '账号Token验证失败');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 222, "text": "throw new WsException('Expected account token');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 246, "text": "throw new WsException(result.error || '角色Token验证失败');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 252, "text": "throw new WsException('Expected character token');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 258, "text": "throw new WsException('Character token must contain character and server information');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\activity\\src\\modules\\event\\event.service.ts", "throwCount": 14, "throws": [{"line": 94, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 144, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 157, "text": "throw new NotFoundException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 168, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 192, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 205, "text": "throw new NotFoundException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 239, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 252, "text": "throw new NotFoundException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 260, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 268, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 277, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 307, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 319, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 339, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\guide\\guide.service.ts", "throwCount": 14, "throws": [{"line": 60, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 101, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 115, "text": "throw new NotFoundException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 205, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 233, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 257, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 277, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 297, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 573, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 588, "text": "throw new BadRequestException({\n          code: ErrorCode.GUIDE_NOT_TRIGGERED,\n          message: ErrorMessages[ErrorCode.GUIDE_NOT_TRIGGERED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 604, "text": "throw new NotFoundException({\n          code: ErrorCode.GUIDE_CONFIG_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.GUIDE_CONFIG_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 626, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 658, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 684, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\modules\\scout\\scout.service.ts", "throwCount": 14, "throws": [{"line": 303, "text": "throw new BadRequestException('无效的球探类型');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 316, "text": "throw new BadRequestException(`搜索冷却中，剩余时间: ${Math.ceil(remainingTime / 60000)}分钟`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 325, "text": "throw new BadRequestException({\n          code: -1,\n          message: '金币不足',\n          data: {\n            required: searchCost.gold,\n            current: goldCheckResult.currentGold,\n          },\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 341, "text": "throw new BadRequestException('扣除金币失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 367, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 387, "text": "throw new Error(result.message || '获取球探数据失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 393, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 412, "text": "throw new Error(result.message || '更新球探数据失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 418, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 921, "text": "throw new Error(`添加道具失败: ${result.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 927, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1035, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1206, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 1264, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\sign\\sign.service.ts", "throwCount": 13, "throws": [{"line": 81, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 93, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 101, "text": "throw new BadRequestException({\n          code: ErrorCode.DAY_ALREADY_SIGNED,\n          message: ErrorMessages[ErrorCode.DAY_ALREADY_SIGNED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 109, "text": "throw new BadRequestException({\n          code: ErrorCode.DAY_ALREADY_SIGNED,\n          message: ErrorMessages[ErrorCode.DAY_ALREADY_SIGNED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 136, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 148, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 156, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 165, "text": "throw new BadRequestException({\n          code: ErrorCode.DAY_ALREADY_SIGNED,\n          message: ErrorMessages[ErrorCode.DAY_ALREADY_SIGNED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 207, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 218, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 227, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 235, "text": "throw new BadRequestException({\n          code: ErrorCode.ACTIVITY_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 259, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\character\\src\\common\\repositories\\character.repository.ts", "throwCount": 13, "throws": [{"line": 34, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 46, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 69, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 92, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 108, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 128, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 150, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 218, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 248, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 270, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 292, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 344, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\rbac\\services\\roles.service.ts", "throwCount": 13, "throws": [{"line": 37, "text": "throw new ConflictException('角色名称已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 67, "text": "throw new BadRequestException('创建角色失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 84, "text": "throw new NotFoundException('角色不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 96, "text": "throw new NotFoundException('角色不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 126, "text": "throw new BadRequestException('不能修改系统角色的系统标识');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 134, "text": "throw new ConflictException('角色名称已被其他角色使用');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 163, "text": "throw new NotFoundException('角色不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 170, "text": "throw new BadRequestException('更新角色失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 184, "text": "throw new BadRequestException('不能删除系统角色');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 189, "text": "throw new BadRequestException('不能删除正在使用的角色');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 311, "text": "throw new BadRequestException(`权限不存在: ${permissionName}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 331, "text": "throw new BadRequestException(`以下角色不存在: ${invalidRoles.join(', ')}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 372, "text": "throw new BadRequestException('检测到循环继承关系');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "simple", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\honor\\honor.service.ts", "throwCount": 12, "throws": [{"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 54, "text": "throw new NotFoundException({\n          code: ErrorCode.HONOR_TASK_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HONOR_TASK_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 62, "text": "throw new BadRequestException({\n          code: ErrorCode.HONOR_TASK_NOT_COMPLETED,\n          message: ErrorMessages[ErrorCode.HONOR_TASK_NOT_COMPLETED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 71, "text": "throw new BadRequestException({\n          code: ErrorCode.HONOR_REWARD_CLAIM_FAILED,\n          message: ErrorMessages[ErrorCode.HONOR_REWARD_CLAIM_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 98, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 134, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 148, "text": "throw new BadRequestException({\n          code: ErrorCode.HONOR_TASK_ALREADY_EXISTS,\n          message: ErrorMessages[ErrorCode.HONOR_TASK_ALREADY_EXISTS],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 179, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 192, "text": "throw new BadRequestException({\n          code: ErrorCode.HONOR_LEVEL_NOT_REACHED,\n          message: ErrorMessages[ErrorCode.HONOR_LEVEL_NOT_REACHED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 201, "text": "throw new NotFoundException({\n          code: ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 220, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 262, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\activity\\src\\modules\\sign\\sign.service.new.ts", "throwCount": 12, "throws": [{"line": 81, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 95, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_RECORD_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.SIGN_RECORD_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 103, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_ALREADY_SIGNED,\n          message: ErrorMessages[ErrorCode.SIGN_ALREADY_SIGNED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 112, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_FAILED,\n          message: ErrorMessages[ErrorCode.SIGN_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 133, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 147, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_RECORD_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.SIGN_RECORD_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 155, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_INVALID_MAKEUP_DAY,\n          message: ErrorMessages[ErrorCode.SIGN_INVALID_MAKEUP_DAY],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 169, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_MAKEUP_FAILED,\n          message: ErrorMessages[ErrorCode.SIGN_MAKEUP_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 189, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 203, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_RECORD_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.SIGN_RECORD_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 212, "text": "throw new BadRequestException({\n          code: ErrorCode.SIGN_SEVEN_DAY_REWARD_FAILED,\n          message: ErrorMessages[ErrorCode.SIGN_SEVEN_DAY_REWARD_FAILED],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 228, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "medium", "priority": "low"}, {"path": "apps\\character\\src\\common\\repositories\\item.repository.ts", "throwCount": 12, "throws": [{"line": 28, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 61, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 82, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 112, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 132, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 145, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 167, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 179, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 208, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 220, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 242, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 291, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\modules\\training\\training.service.ts", "throwCount": 12, "throws": [{"line": 29, "text": "throw new NotFoundException({\n        code: ErrorCode.HERO_NOT_FOUND,\n        message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\n      });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 82, "text": "throw new NotFoundException({\n          code: ErrorCode.HERO_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 100, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 112, "text": "throw new NotFoundException({\n          code: ErrorCode.HERO_NOT_FOUND,\n          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\n        });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 120, "text": "throw new BadRequestException({\n          code: ErrorCode.HERO_TRAINING_COOLDOWN,\n          message: ErrorMessages[ErrorCode.HERO_TRAINING_COOLDOWN],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 128, "text": "throw new BadRequestException({\n          code: ErrorCode.HERO_IN_TRAINING,\n          message: ErrorMessages[ErrorCode.HERO_IN_TRAINING],\n        });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 149, "text": "throw new BadRequestException({\n            code: ErrorCode.INVALID_PARAMETER,\n            message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\n          });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 196, "text": "throw new NotFoundException({\n        code: ErrorCode.HERO_NOT_FOUND,\n        message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\n      });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 208, "text": "throw new BadRequestException('球员特训数据不存在');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 213, "text": "throw new BadRequestException('无效的特训类型');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 219, "text": "throw new BadRequestException('没有对应的特训数据');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "simple", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\services\\character-session.service.ts", "throwCount": 12, "throws": [{"line": 60, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 146, "text": "throw new NotFoundException('会话不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 150, "text": "throw new BadRequestException('会话已过期，无法延长');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 164, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 191, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 203, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 226, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 251, "text": "throw new BadRequestException('用户ID不能为空');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 255, "text": "throw new BadRequestException('角色ID不能为空');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 259, "text": "throw new BadRequestException('服务器ID不能为空');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 263, "text": "throw new BadRequestException('服务器名称不能为空');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 267, "text": "throw new BadRequestException('过期时间必须在未来');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "medium", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\security\\pipes\\parse-object-id.pipe.ts", "throwCount": 12, "throws": [{"line": 47, "text": "throw new BadRequestException(this.options.errorMessage);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 53, "text": "throw new BadRequestException(this.options.errorMessage);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 140, "text": "throw new BadRequestException(this.options.errorMessage);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 146, "text": "throw new BadRequestException(`ObjectId数组长度不能超过${this.options.maxLength}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 157, "text": "throw new BadRequestException(`数组索引${i}处的ObjectId格式无效`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 251, "text": "throw new BadRequestException(`查询参数${field}的ObjectId格式无效`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 261, "text": "throw new BadRequestException(`查询参数${field}必须是数组`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 265, "text": "throw new BadRequestException(`查询参数${field}数组长度不能超过50`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 271, "text": "throw new BadRequestException(`查询参数${field}数组索引${index}的ObjectId格式无效`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 316, "text": "throw new BadRequestException('此管道只能用于路径参数');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 379, "text": "throw new BadRequestException(`请求体${field}数组索引${index}的ObjectId格式无效`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 387, "text": "throw new BadRequestException(`请求体${field}的ObjectId格式无效`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\infra\\auth\\guards\\auth.guard.ts", "throwCount": 12, "throws": [{"line": 36, "text": "throw new UnauthorizedException('Authentication token is required');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 52, "text": "throw new UnauthorizedException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${validationResult.tokenScope}`);", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 57, "text": "throw new UnauthorizedException(error.message || 'Invalid authentication token');", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 98, "text": "throw new UnauthorizedException('Invalid token format');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 108, "text": "throw new UnauthorizedException(`Unsupported token scope: ${decoded.scope}`);", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 112, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 114, "text": "throw new UnauthorizedException('Token validation failed');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 128, "text": "throw new UnauthorizedException(result.error || '账号Token验证失败');", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 133, "text": "throw new UnauthorizedException('Expected account token');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 153, "text": "throw new UnauthorizedException(result.error || '角色Token验证失败');", "pattern": "unknown", "exceptionType": "UnauthorizedException", "canAutoConvert": false}, {"line": 158, "text": "throw new UnauthorizedException('Expected character token');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 163, "text": "throw new UnauthorizedException('Character token must contain character and server information');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "medium", "priority": "incompatible"}, {"path": "apps\\character\\src\\common\\repositories\\tactic.repository.ts", "throwCount": 11, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 47, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 59, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 71, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 87, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 122, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 137, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 150, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 162, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 179, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 204, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\common\\repositories\\relay.repository.ts", "throwCount": 11, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 58, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 102, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 119, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 135, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 151, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 199, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 218, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 238, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 266, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\common\\repositories\\shop.repository.ts", "throwCount": 11, "throws": [{"line": 40, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 52, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 64, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 89, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 118, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 138, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 151, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 192, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 237, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 309, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 359, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\common\\repositories\\skill.repository.ts", "throwCount": 11, "throws": [{"line": 40, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 52, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 76, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 96, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 109, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 122, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 141, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 161, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 221, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 263, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\business.repository.ts", "throwCount": 11, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 57, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 75, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 107, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 124, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 141, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 153, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 166, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 178, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 212, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\trophy.repository.ts", "throwCount": 11, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 57, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 75, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 107, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 124, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 159, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 172, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 184, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 218, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 245, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\common\\repositories\\chat.repository.ts", "throwCount": 11, "throws": [{"line": 37, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 62, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 95, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 117, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 145, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 198, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 213, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 225, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 245, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 262, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\admin\\services\\admin.service.ts", "throwCount": 11, "throws": [{"line": 114, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 129, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 143, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 157, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 209, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 223, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 237, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 263, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 301, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 317, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\controllers\\character-auth.controller.ts", "throwCount": 11, "throws": [{"line": 170, "text": "throw new Error('缺少角色认证Token');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 183, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 222, "text": "throw new Error('缺少角色认证Token');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 234, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 267, "text": "throw new Error('缺少角色认证Token');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 284, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 317, "text": "throw new Error('缺少角色认证Token');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 330, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 394, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 444, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 466, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\services\\auth.service.ts", "throwCount": 11, "throws": [{"line": 102, "text": "throw new UnauthorizedException('用户名或密码错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 108, "text": "throw new UnauthorizedException('需要提供MFA验证码');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 116, "text": "throw new UnauthorizedException('MFA验证码错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 200, "text": "throw new UnauthorizedException('会话无效或已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 282, "text": "throw new UnauthorizedException('令牌格式无效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 288, "text": "throw new UnauthorizedException('令牌格式不正确');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 296, "text": "throw new UnauthorizedException('令牌类型错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 346, "text": "throw new UnauthorizedException('会话已失效');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 350, "text": "throw new UnauthorizedException('会话已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 354, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 478, "text": "throw new BadRequestException('密码重置功能暂未实现');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "simple", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\services\\character-auth.service.ts", "throwCount": 11, "throws": [{"line": 51, "text": "throw new NotFoundException('用户不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 127, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 142, "text": "throw new UnauthorizedException('会话已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 151, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 277, "text": "throw new BadRequestException(`终止用户会话失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 302, "text": "throw new UnauthorizedException('角色不存在或不属于当前用户');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 337, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 352, "text": "throw new UnauthorizedException('会话已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 390, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 405, "text": "throw new UnauthorizedException('会话已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 420, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\character\\services\\character.service.ts", "throwCount": 11, "throws": [{"line": 52, "text": "throw new ConflictException(serverValidation.message);", "pattern": "unknown", "exceptionType": "ConflictException", "canAutoConvert": false}, {"line": 58, "text": "throw new ConflictException(nameFormatValidation.message);", "pattern": "unknown", "exceptionType": "ConflictException", "canAutoConvert": false}, {"line": 64, "text": "throw new ConflictException(`用户在区服${createDto.serverId}已存在角色`);", "pattern": "unknown", "exceptionType": "ConflictException", "canAutoConvert": false}, {"line": 73, "text": "throw new ConflictException(`角色名称\"${createDto.characterName}\"已存在`);", "pattern": "unknown", "exceptionType": "ConflictException", "canAutoConvert": false}, {"line": 116, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 173, "text": "throw new NotFoundException(`角色 ${characterId} 不存在`);", "pattern": "unknown", "exceptionType": "NotFoundException", "canAutoConvert": false}, {"line": 195, "text": "throw new UnauthorizedException('角色不存在或不属于当前用户');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 208, "text": "throw new NotFoundException(`角色 ${characterId} 不存在`);", "pattern": "unknown", "exceptionType": "NotFoundException", "canAutoConvert": false}, {"line": 251, "text": "throw new NotFoundException(`角色 ${characterId} 不存在`);", "pattern": "unknown", "exceptionType": "NotFoundException", "canAutoConvert": false}, {"line": 257, "text": "throw new ConflictException(canDelete.reason);", "pattern": "unknown", "exceptionType": "ConflictException", "canAutoConvert": false}, {"line": 336, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\rbac\\services\\permissions.service.ts", "throwCount": 11, "throws": [{"line": 34, "text": "throw new ConflictException('权限已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 66, "text": "throw new BadRequestException('创建权限失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 83, "text": "throw new NotFoundException('权限不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 95, "text": "throw new NotFoundException('权限不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 128, "text": "throw new BadRequestException('不能修改系统权限的系统标识');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 143, "text": "throw new ConflictException('资源和操作组合已存在');", "pattern": "simple", "exceptionType": "ConflictException", "canAutoConvert": true}, {"line": 166, "text": "throw new NotFoundException('权限更新失败');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 170, "text": "throw new NotFoundException('权限不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 177, "text": "throw new BadRequestException('更新权限失败');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 191, "text": "throw new BadRequestException('不能删除系统权限');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 249, "text": "throw new BadRequestException(`以下权限不存在: ${invalidPermissions.join(', ')}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}], "complexity": "simple", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\ranking.repository.ts", "throwCount": 10, "throws": [{"line": 33, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 56, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 78, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 115, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 131, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 168, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 193, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 267, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 288, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 300, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\tournament.repository.ts", "throwCount": 10, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 57, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 75, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 128, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 172, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 185, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 197, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 228, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 284, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\common\\repositories\\friend.repository.ts", "throwCount": 10, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 63, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 83, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 135, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 159, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 223, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 261, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 313, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 335, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\honor.repository.ts", "throwCount": 9, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 51, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 73, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 95, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 112, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 126, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 145, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 180, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 200, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\task.repository.ts", "throwCount": 9, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 63, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 83, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 116, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 151, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 236, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 277, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 304, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\modules\\inventory\\inventory.service.ts", "throwCount": 9, "throws": [{"line": 68, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 184, "text": "throw new NotFoundException('背包不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 190, "text": "throw new NotFoundException('物品不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 751, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 771, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 791, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 811, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 831, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 851, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\battle.repository.ts", "throwCount": 9, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 42, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 60, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 73, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 95, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 111, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 132, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 163, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 175, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\security\\pipes\\validation.pipe.ts", "throwCount": 9, "throws": [{"line": 106, "text": "throw this.options.exceptionFactory(errors);", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 109, "text": "throw this.createValidationException(errors, metadata);", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 278, "text": "throw new BadRequestException({\r\n        message: '用户注册验证失败',\r\n        errors: errors.map(error => ({\r\n          field: 'general',\r\n          value: null,\r\n          constraints: { custom: error },\r\n        })),\r\n      });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 306, "text": "throw new BadRequestException({\r\n        message: '密码修改验证失败',\r\n        errors: errors.map(error => ({\r\n          field: 'general',\r\n          value: null,\r\n          constraints: { custom: error },\r\n        })),\r\n      });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 335, "text": "throw new BadRequestException({\r\n        message: '角色分配验证失败',\r\n        errors: errors.map(error => ({\r\n          field: 'roles',\r\n          value: value.roles,\r\n          constraints: { custom: error },\r\n        })),\r\n      });", "pattern": "complex", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 412, "text": "throw new BadRequestException(`检测到潜在的安全威胁: ${type}`);", "pattern": "unknown", "exceptionType": "BadRequestException", "canAutoConvert": false}, {"line": 434, "text": "throw new BadRequestException('请求数据过大');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 450, "text": "throw new BadRequestException('数组长度超出限制');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 464, "text": "throw new BadRequestException('字符串长度超出限制');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\modules\\websocket\\gateways\\websocket.gateway.ts", "throwCount": 9, "throws": [{"line": 551, "text": "throw new Error(`Invalid command format: '${command}'. Expected format: 'service.action' or 'service.module.action'`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 560, "text": "throw new Error(`Invalid service name: '${service}'. Valid services: ${validServices.join(', ')}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 587, "text": "throw new Error(routeResult.error || 'Message routing failed');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 596, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 658, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 755, "text": "throw new Error('Character token required for game connection');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 764, "text": "throw new Error('Character token required for game connection');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 773, "text": "throw new Error('Invalid character token: missing character or server information');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 805, "text": "throw new Error('Invalid character token');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\activity\\src\\common\\repositories\\energy.repository.ts", "throwCount": 8, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 52, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 74, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 96, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 113, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 130, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 150, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 181, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\health\\health.service.ts", "throwCount": 8, "throws": [{"line": 45, "text": "throw new Error('Redis客户端未初始化');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 81, "text": "throw new Error('JWT密钥配置无效');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 111, "text": "throw new Error('缓存服务不可用');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 124, "text": "throw new Error('缓存读写测试失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 209, "text": "throw new Error('MongoDB连接异常');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 216, "text": "throw new Error('Redis连接异常');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 282, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 333, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\common\\repositories\\exchange.repository.ts", "throwCount": 8, "throws": [{"line": 27, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 58, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 102, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 119, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 158, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 175, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 195, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\common\\repositories\\league.repository.ts", "throwCount": 7, "throws": [{"line": 26, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 39, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 57, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 75, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 88, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 100, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 125, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\modules\\chat\\chat.service.ts", "throwCount": 7, "throws": [{"line": 30, "text": "throw new BadRequestException('私聊消息必须指定接收者');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 84, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 130, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 177, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 235, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 250, "text": "throw new BadRequestException('频道不存在或用户不在频道中');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 264, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\security\\services\\crypto.service.ts", "throwCount": 7, "throws": [{"line": 84, "text": "throw new Error('数据加密失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 111, "text": "throw new Error('数据解密失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 123, "text": "throw new Error('密码哈希失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 214, "text": "throw new Error('数字签名失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 252, "text": "throw new Error('密钥对生成失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 276, "text": "throw new Error('椭圆曲线密钥对生成失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 340, "text": "throw new Error('至少需要选择一种字符类型');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\announcement.service.ts", "throwCount": 7, "throws": [{"line": 109, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 122, "text": "throw new Error(`Announcement not found: ${announcementId}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 169, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 183, "text": "throw new Error('Announcement start time is in the future');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 186, "text": "throw new Error('Announcement has expired');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 203, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 287, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\common\\utils\\index.ts", "throwCount": 6, "throws": [{"line": 49, "text": "throw lastError;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 64, "text": "throw lastError!;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 229, "text": "throw new Error(`Environment variable ${key} is required`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 239, "text": "throw new Error(`Environment variable ${key} is required`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 246, "text": "throw new Error(`Environment variable ${key} must be a number`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 257, "text": "throw new Error(`Environment variable ${key} is required`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\modules\\career\\career.service.ts", "throwCount": 6, "throws": [{"line": 30, "text": "throw new NotFoundException('球员不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 34, "text": "throw new BadRequestException('天数必须大于0');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 164, "text": "throw new NotFoundException('球员不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 214, "text": "throw new NotFoundException('球员不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 218, "text": "throw new BadRequestException('球员不属于该角色');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 319, "text": "throw new NotFoundException('球员不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}], "complexity": "simple", "priority": "medium"}, {"path": "apps\\auth\\src\\modules\\admin\\services\\system.service.ts", "throwCount": 6, "throws": [{"line": 79, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 155, "text": "throw new Error(`不支持的维护操作: ${actionDto.action}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 167, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 201, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 232, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 289, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\character\\controllers\\character.controller.ts", "throwCount": 6, "throws": [{"line": 71, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 102, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 132, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 166, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 205, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 245, "text": "throw new Error('无法获取用户ID，请重新登录');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\event-notification.service.ts", "throwCount": 6, "throws": [{"line": 113, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 126, "text": "throw new Error(`Event notification not found: ${notificationId}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 177, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 191, "text": "throw new Error('Event has already ended');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 208, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 297, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\admin\\services\\statistics.service.ts", "throwCount": 5, "throws": [{"line": 68, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 117, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 171, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 206, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 244, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\api-key\\services\\api-key.service.ts", "throwCount": 5, "throws": [{"line": 22, "text": "throw new UnauthorizedException('API Key不能为空');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 28, "text": "throw new UnauthorizedException('无效的API Key');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 35, "text": "throw new UnauthorizedException('API Key已过期');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 40, "text": "throw new UnauthorizedException('API Key已被禁用');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 49, "text": "throw new UnauthorizedException('IP地址不在白名单中');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "simple", "priority": "high"}, {"path": "apps\\auth\\src\\modules\\security\\services\\encryption.service.ts", "throwCount": 5, "throws": [{"line": 49, "text": "throw new Error('加密密钥未配置');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 79, "text": "throw new Error('数据加密失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 88, "text": "throw new Error('解密密钥未配置');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 112, "text": "throw new Error('数据解密失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 267, "text": "throw new Error(`不支持的密钥派生算法: ${this.keyDerivation}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\message-persistence.service.ts", "throwCount": 5, "throws": [{"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 89, "text": "throw new Error(`Message not found: ${messageId}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 110, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 136, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 228, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\message-publisher.service.ts", "throwCount": 5, "throws": [{"line": 62, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 154, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 201, "text": "throw new Error('Invalid message format: missing required fields');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 205, "text": "throw new Error('Invalid message format: missing target configuration');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 209, "text": "throw new Error('Invalid message format: publishAt must be before expireAt');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\health\\controllers\\microservice-health.controller.ts", "throwCount": 5, "throws": [{"line": 78, "text": "throw new HttpException(\r\n        {\r\n          status: 'error',\r\n          timestamp: new Date().toISOString(),\r\n          error: error.message || '微服务健康检查失败',\r\n        },\r\n        HttpStatus.SERVICE_UNAVAILABLE,\r\n      );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 140, "text": "throw new HttpException(\r\n          {\r\n            status: 'error',\r\n            timestamp: new Date().toISOString(),\r\n            service: serviceName,\r\n            error: '微服务不存在或未配置',\r\n          },\r\n          HttpStatus.NOT_FOUND,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 180, "text": "throw new HttpException(detailedStatus, HttpStatus.SERVICE_UNAVAILABLE);", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 186, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 189, "text": "throw new HttpException(\r\n        {\r\n          status: 'error',\r\n          timestamp: new Date().toISOString(),\r\n          service: serviceName,\r\n          error: error.message || '微服务健康检查失败',\r\n        },\r\n        HttpStatus.SERVICE_UNAVAILABLE,\r\n      );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\websocket\\guards\\ws-auth.guard.ts", "throwCount": 5, "throws": [{"line": 46, "text": "throw new WsException('Not authenticated');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 52, "text": "throw new WsException('Character context required');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 67, "text": "throw new WsException('Insufficient permissions');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 78, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 80, "text": "throw new WsException('Authentication failed');", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\economy\\src\\modules\\trade\\trade.service.ts", "throwCount": 4, "throws": [{"line": 545, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 565, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 585, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 605, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\services\\mfa.service.ts", "throwCount": 4, "throws": [{"line": 98, "text": "throw new BadRequestException('无效的验证码');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 143, "text": "throw new UnauthorizedException('密码错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 151, "text": "throw new BadRequestException('无效的验证码');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}, {"line": 264, "text": "throw new BadRequestException('短信功能未启用');", "pattern": "simple", "exceptionType": "BadRequestException", "canAutoConvert": true}], "complexity": "simple", "priority": "high"}, {"path": "apps\\gateway\\src\\infra\\auth\\services\\character-auth.service.ts", "throwCount": 4, "throws": [{"line": 40, "text": "throw new Error(tokenResult.error || '角色Token生成失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 47, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 116, "text": "throw new Error(result.error || '角色登出失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 122, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\routing\\controllers\\proxy.controller.ts", "throwCount": 4, "throws": [{"line": 61, "text": "throw new HttpException(\r\n          {\r\n            statusCode: routeResult.statusCode || HttpStatus.NOT_FOUND,\r\n            message: routeResult.error || 'Unknown service',\r\n            error: 'Not Found',\r\n            path: fullPath,\r\n            method: req.method,\r\n            timestamp: new Date().toISOString(),\r\n          },\r\n          routeResult.statusCode || HttpStatus.NOT_FOUND,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 79, "text": "throw new HttpException(\r\n          {\r\n            statusCode: routeResult.statusCode || HttpStatus.SERVICE_UNAVAILABLE,\r\n            message: routeResult.error || 'Service unavailable',\r\n            error: 'Service Unavailable',\r\n            path: fullPath,\r\n            method: req.method,\r\n            timestamp: new Date().toISOString(),\r\n          },\r\n          routeResult.statusCode || HttpStatus.SERVICE_UNAVAILABLE,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 98, "text": "throw new HttpException(\r\n          {\r\n            statusCode: HttpStatus.NOT_FOUND,\r\n            message: 'API endpoint not found',\r\n            error: 'Not Found',\r\n            path: fullPath,\r\n            method: req.method,\r\n            timestamp: new Date().toISOString(),\r\n          },\r\n          HttpStatus.NOT_FOUND,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 114, "text": "throw new HttpException(\r\n          {\r\n            statusCode: HttpStatus.NOT_FOUND,\r\n            message: 'Service not found',\r\n            error: 'Not Found',\r\n            path: fullPath,\r\n            method: req.method,\r\n            timestamp: new Date().toISOString(),\r\n          },\r\n          HttpStatus.NOT_FOUND,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\server-discovery\\controllers\\server-discovery.controller.ts", "throwCount": 4, "throws": [{"line": 123, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 195, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 278, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 345, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\websocket\\services\\message-router.service.ts", "throwCount": 4, "throws": [{"line": 152, "text": "throw new Error(`Invalid service name: ${service}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 178, "text": "throw new Error(`Invalid service name: ${actualService}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 212, "text": "throw new Error(`Invalid service name: ${actualService}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 246, "text": "throw new Error(`Invalid service name: ${actualService}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\common\\decorators\\secure-repository.decorator.ts", "throwCount": 3, "throws": [{"line": 40, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 61, "text": "throw new Error(`缺少必需字段: ${field}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 147, "text": "throw new Error('检测到潜在的安全威胁');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\api-key\\controllers\\api-key.controller.ts", "throwCount": 3, "throws": [{"line": 70, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 91, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 113, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\message-scheduler.service.ts", "throwCount": 3, "throws": [{"line": 173, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 201, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 225, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\health\\controllers\\standard-health.controller.ts", "throwCount": 3, "throws": [{"line": 109, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 159, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 225, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\health\\indicators\\microservice-health.indicator.ts", "throwCount": 3, "throws": [{"line": 122, "text": "throw new Error('No service instance available');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 338, "text": "throw new HealthCheckError('Some microservices are unhealthy', result);", "pattern": "unknown", "exceptionType": "HealthCheckError", "canAutoConvert": false}, {"line": 348, "text": "throw new HealthCheckError('Microservice health check failed', result);", "pattern": "unknown", "exceptionType": "HealthCheckError", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\load-balancing\\services\\load-balancing.service.ts", "throwCount": 3, "throws": [{"line": 334, "text": "throw new Error(`Service unavailable: Connection refused to ${url}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 336, "text": "throw new Error(`Service unavailable: Timeout connecting to ${url}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 341, "text": "throw new Error(`Health check failed: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\main.ts", "throwCount": 2, "throws": [{"line": 124, "text": "throw new Error(`网关实例ID ${instanceId} 超出最大限制 ${maxInstances}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 133, "text": "throw new Error(`网关实例${instanceId}端口${port}不可用，请检查端口占用情况`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\modules\\formation\\formation.service.ts", "throwCount": 2, "throws": [{"line": 78, "text": "throw new NotFoundException(`角色阵容数据不存在: ${characterId}`);", "pattern": "unknown", "exceptionType": "NotFoundException", "canAutoConvert": false}, {"line": 1232, "text": "throw new Error('无法获取任何球员信息');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\modules\\lottery\\lottery.service.ts", "throwCount": 2, "throws": [{"line": 147, "text": "throw new Error('抽奖配置获取失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 157, "text": "throw new Error('抽奖权重配置错误');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\modules\\cultivation\\cultivation.service.ts", "throwCount": 2, "throws": [{"line": 41, "text": "throw new NotFoundException({\r\n        code: ErrorCode.HERO_NOT_FOUND,\r\n        message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],\r\n      });", "pattern": "complex", "exceptionType": "NotFoundException", "canAutoConvert": true}, {"line": 50, "text": "throw new NotFoundException('球员配置不存在');", "pattern": "simple", "exceptionType": "NotFoundException", "canAutoConvert": true}], "complexity": "simple", "priority": "high"}, {"path": "apps\\hero\\src\\modules\\ground\\ground.service.ts", "throwCount": 2, "throws": [{"line": 630, "text": "throw new Error(`更新场地数据失败: ${result?.message || '未知错误'}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 636, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\modules\\battle\\battle-engine.ts", "throwCount": 2, "throws": [{"line": 38, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 60, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\guards\\character-auth.guard.ts", "throwCount": 2, "throws": [{"line": 21, "text": "throw new UnauthorizedException('缺少角色认证Token');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}, {"line": 38, "text": "throw new UnauthorizedException('角色认证失败');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "simple", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\rbac\\guards\\permissions.guard.ts", "throwCount": 2, "throws": [{"line": 34, "text": "throw new ForbiddenException('用户未认证');", "pattern": "simple", "exceptionType": "ForbiddenException", "canAutoConvert": true}, {"line": 42, "text": "throw new ForbiddenException(\r\n          `缺少权限: ${permission.resource}:${permission.action}`\r\n        );", "pattern": "unknown", "exceptionType": "ForbiddenException", "canAutoConvert": false}], "complexity": "medium", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\security\\filters\\validation-exception.filter.ts", "throwCount": 2, "throws": [{"line": 54, "text": "throw exception;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 302, "text": "throw exception;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\security\\guards\\throttler-behind-proxy.guard.ts", "throwCount": 2, "throws": [{"line": 92, "text": "throw new ThrottlerException('请求过于频繁，请稍后重试');", "pattern": "unknown", "exceptionType": "ThrottlerException", "canAutoConvert": false}, {"line": 314, "text": "throw new Error('Pipeline execution failed');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\security\\interceptors\\logging.interceptor.ts", "throwCount": 2, "throws": [{"line": 131, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 417, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\security\\services\\utils.service.ts", "throwCount": 2, "throws": [{"line": 348, "text": "throw lastError;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 356, "text": "throw lastError!;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\services\\global-broadcast.service.ts", "throwCount": 2, "throws": [{"line": 56, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 167, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\health\\indicators\\redis-health.indicator.ts", "throwCount": 2, "throws": [{"line": 24, "text": "throw new Error('<PERSON><PERSON> ping failed');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}, {"line": 40, "text": "throw new HealthCheckError('<PERSON><PERSON> check failed', result);", "pattern": "unknown", "exceptionType": "HealthCheckError", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\monitoring\\services\\tracing.service.ts", "throwCount": 2, "throws": [{"line": 261, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 281, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\rate-limiting\\guards\\rate-limit.guard.ts", "throwCount": 2, "throws": [{"line": 46, "text": "throw new HttpException(\r\n          {\r\n            statusCode: HttpStatus.TOO_MANY_REQUESTS,\r\n            message: 'Too many requests',\r\n            retryAfter: result.retryAfter,\r\n          },\r\n          HttpStatus.TOO_MANY_REQUESTS,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}, {"line": 65, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\modules\\server-discovery\\services\\server-list.service.ts", "throwCount": 2, "throws": [{"line": 119, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}, {"line": 160, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\websocket\\guards\\ws-rate-limit.guard.ts", "throwCount": 2, "throws": [{"line": 72, "text": "throw new WsException(message);", "pattern": "unknown", "exceptionType": "WsException", "canAutoConvert": false}, {"line": 78, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\activity\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 75, "text": "throw new Error(`Activity服务配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 91, "text": "throw new Error(`应用配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\config\\auth.config.ts", "throwCount": 1, "throws": [{"line": 339, "text": "throw new Error(`认证配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\config\\database.config.ts", "throwCount": 1, "throws": [{"line": 155, "text": "throw new Error(`数据库配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\config\\redis.config.ts", "throwCount": 1, "throws": [{"line": 168, "text": "throw new Error(`Redis配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\config\\security.config.ts", "throwCount": 1, "throws": [{"line": 450, "text": "throw new Error(`安全配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\character\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 75, "text": "throw new Error(`Character服务配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\economy\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 75, "text": "throw new Error(`Economy服务配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\hero\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 75, "text": "throw new Error(`Hero服务配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\social\\src\\config\\app.config.ts", "throwCount": 1, "throws": [{"line": 75, "text": "throw new Error(`Social服务配置验证失败: ${error.message}`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\common\\guards\\internal-api.guard.ts", "throwCount": 1, "throws": [{"line": 38, "text": "throw new ForbiddenException({\n        code: 403,\n        message: `内部API ${pattern} 禁止客户端访问`,\n        timestamp: new Date().toISOString(),\n        environment: this.securityConfig.environment,\n        pattern,\n        userId\n      });", "pattern": "complex", "exceptionType": "ForbiddenException", "canAutoConvert": true}], "complexity": "simple", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\common\\interceptors\\logging.interceptor.ts", "throwCount": 1, "throws": [{"line": 46, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\common\\interceptors\\request-logging.interceptor.ts", "throwCount": 1, "throws": [{"line": 83, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\match\\src\\modules\\battle\\battle.service.ts", "throwCount": 1, "throws": [{"line": 344, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\match\\src\\modules\\business\\business.service.ts", "throwCount": 1, "throws": [{"line": 744, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\auth\\strategies\\jwt.strategy.ts", "throwCount": 1, "throws": [{"line": 37, "text": "throw new UnauthorizedException('令牌验证失败');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "simple", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\auth\\strategies\\local.strategy.ts", "throwCount": 1, "throws": [{"line": 24, "text": "throw new UnauthorizedException('用户名或密码错误');", "pattern": "simple", "exceptionType": "UnauthorizedException", "canAutoConvert": true}], "complexity": "simple", "priority": "incompatible"}, {"path": "apps\\auth\\src\\modules\\character\\repositories\\auth-character.repository.ts", "throwCount": 1, "throws": [{"line": 93, "text": "throw new Error(`Character ${characterId} not found after update`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\character\\repositories\\user-character-mapping.repository.ts", "throwCount": 1, "throws": [{"line": 73, "text": "throw new Error(`Mapping ${id} not found after update`);", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\auth\\src\\modules\\security\\services\\password.service.ts", "throwCount": 1, "throws": [{"line": 164, "text": "throw new Error('密码处理失败');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\queue\\dead-letter-queue.service.ts", "throwCount": 1, "throws": [{"line": 64, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\global-messaging\\queue\\queue-manager.service.ts", "throwCount": 1, "throws": [{"line": 55, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\rate-limiting\\middleware\\rate-limit.middleware.ts", "throwCount": 1, "throws": [{"line": 65, "text": "throw new HttpException(\r\n          {\r\n            statusCode: HttpStatus.TOO_MANY_REQUESTS,\r\n            message: `Rate limit exceeded for ${blockedCheck.type}`,\r\n            error: 'Too Many Requests',\r\n            retryAfter: blockedCheck.result.retryAfter,\r\n            limit: blockedCheck.result.limit,\r\n            remaining: blockedCheck.result.remaining,\r\n            resetTime: blockedCheck.result.resetTime,\r\n          },\r\n          HttpStatus.TOO_MANY_REQUESTS,\r\n        );", "pattern": "unknown", "exceptionType": "HttpException", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\modules\\routing\\interceptors\\proxy-request.interceptor.ts", "throwCount": 1, "throws": [{"line": 76, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "incompatible"}, {"path": "apps\\gateway\\src\\modules\\routing\\services\\proxy.service.ts", "throwCount": 1, "throws": [{"line": 51, "text": "throw new Error('Invalid enhanced route result');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\server-discovery\\services\\server-recommendation.service.ts", "throwCount": 1, "throws": [{"line": 97, "text": "throw error;", "pattern": "unknown", "exceptionType": "unknown", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}, {"path": "apps\\gateway\\src\\modules\\websocket\\services\\session.service.ts", "throwCount": 1, "throws": [{"line": 62, "text": "throw new Error('Game context (characterId and serverId) is required for game WebSocket connections');", "pattern": "unknown", "exceptionType": "Error", "canAutoConvert": false}], "complexity": "complex", "priority": "low"}], "throwPatterns": {"simple": 163, "complex": 223, "unknown": 837}}