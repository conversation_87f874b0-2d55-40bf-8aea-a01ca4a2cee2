const ASTAPIScanner = require('./src/utils/ast-api-scanner');
const glob = require('glob');
const path = require('path');

async function testPathSearch() {
  console.log('🔍 测试路径搜索...');

  const scanner = new ASTAPIScanner({
    projectRoot: '..',
    outputDir: './src/actions'
  });

  // 测试搜索路径
  const searchPaths = [
    `apps/*/src/**/*.dto.ts`,
    `apps/*/src/common/dto/*.ts`,
    `apps/*/src/modules/*/dto/*.ts`,
    `libs/*/src/**/*.dto.ts`,
    `libs/game-types/src/**/*.ts`,
    `libs/*/src/dtos/*.ts`
  ];

  console.log('\n📂 测试搜索路径:');
  for (const pattern of searchPaths) {
    // 修复Windows路径问题：使用正斜杠进行glob搜索
    const fullPattern = path.join('..', pattern).replace(/\\/g, '/');
    console.log(`\n🔍 模式: ${pattern}`);
    console.log(`📁 完整路径: ${fullPattern}`);

    const files = glob.sync(fullPattern);
    console.log(`📄 找到 ${files.length} 个文件`);

    // 查找包含UpdateCharacterDto的文件
    for (const filePath of files) {
      if (filePath.includes('character.dto.ts')) {
        console.log(`✅ 找到character.dto.ts: ${filePath}`);

        // 测试直接调用searchDTOInPattern
        console.log(`🔍 测试searchDTOInPattern...`);
        const result = await scanner.searchDTOInPattern('UpdateCharacterDto', pattern);
        if (result) {
          console.log(`✅ 成功解析UpdateCharacterDto:`, JSON.stringify(result, null, 2));
          return; // 找到就退出
        } else {
          console.log(`❌ 未能解析UpdateCharacterDto`);
        }
      }
    }
  }
}

testPathSearch().catch(console.error);
