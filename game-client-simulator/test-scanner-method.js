const ASTAPIScanner = require('./src/utils/ast-api-scanner');

async function testScannerMethod() {
  console.log('🔍 测试AST扫描器的searchDTOInPattern方法...');

  const scanner = new ASTAPIScanner({
    projectRoot: '..',
    outputDir: './src/actions'
  });

  const pattern = 'apps/*/src/common/dto/*.ts';
  console.log(`📂 测试模式: ${pattern}`);

  try {
    const result = await scanner.searchDTOInPattern('UpdateCharacterDto', pattern);
    if (result) {
      console.log('✅ 成功找到并解析UpdateCharacterDto:');
      console.log(JSON.stringify(result, null, 2));
    } else {
      console.log('❌ 未找到UpdateCharacterDto');
    }
  } catch (error) {
    console.error('❌ 搜索过程中出错:', error.message);
    console.error(error.stack);
  }
}

testScannerMethod().catch(console.error);
