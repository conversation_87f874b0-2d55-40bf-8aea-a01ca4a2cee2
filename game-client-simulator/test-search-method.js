const ASTAPIScanner = require('./src/utils/ast-api-scanner');
const glob = require('glob');
const path = require('path');
const { Project } = require('ts-morph');

async function testSearchMethod() {
  console.log('🔍 测试searchDTOInPattern方法...');

  const scanner = new ASTAPIScanner({
    projectRoot: '..',
    outputDir: './src/actions'
  });

  // 手动实现searchDTOInPattern的逻辑来调试
  const pattern = 'apps/*/src/common/dto/*.ts';
  const typeName = 'UpdateCharacterDto';

  console.log(`📂 测试模式: ${pattern}`);
  console.log(`🔍 搜索类型: ${typeName}`);

  // 修复Windows路径问题：使用正斜杠进行glob搜索
  const fullPattern = path.join('..', pattern).replace(/\\/g, '/');
  console.log(`📁 完整路径: ${fullPattern}`);

  const files = glob.sync(fullPattern);
  console.log(`📄 找到 ${files.length} 个文件`);

  // 创建TypeScript项目
  const project = new Project({
    compilerOptions: {
      target: 'ES2020',
      module: 'CommonJS',
      strict: false,
      skipLibCheck: true,
      allowJs: true,
    },
  });

  for (const filePath of files) {
    console.log(`\n🔍 检查文件: ${filePath}`);

    if (filePath.includes('character.dto.ts')) {
      console.log('✅ 这是character.dto.ts文件');

      try {
        const sourceFile = project.addSourceFileAtPath(filePath);
        console.log('✅ 文件加载成功');

        const typeDefinition = sourceFile.getInterface(typeName) || sourceFile.getClass(typeName);

        if (typeDefinition) {
          console.log(`✅ 找到类型定义: ${typeName}`);

          // 手动解析属性
          const properties = typeDefinition.getProperties ? typeDefinition.getProperties() : [];
          console.log(`📋 属性数量: ${properties.length}`);

          const params = {};
          for (const prop of properties) {
            const propName = prop.getName();
            const isOptional = prop.hasQuestionToken();

            // 获取类型信息
            let propTypeText = '';
            const typeNode = prop.getTypeNode();
            if (typeNode) {
              propTypeText = typeNode.getText();
            } else {
              const propType = prop.getType();
              propTypeText = propType.getText();
            }

            console.log(`  - ${propName}${isOptional ? '?' : ''}: ${propTypeText}`);

            params[propName] = {
              type: propTypeText,
              required: !isOptional,
              description: `${propName}参数`
            };
          }

          console.log('\n✅ 解析成功!');
          console.log(JSON.stringify(params, null, 2));
          return;

        } else {
          console.log(`❌ 未找到类型定义: ${typeName}`);

          // 列出所有类
          const classes = sourceFile.getClasses();
          console.log('📋 文件中的所有类:');
          for (const cls of classes) {
            console.log(`  - ${cls.getName()}`);
          }
        }
      } catch (error) {
        console.error(`❌ 处理文件失败: ${error.message}`);
      }
    }
  }

  console.log('❌ 未找到UpdateCharacterDto');
}

testSearchMethod().catch(console.error);
