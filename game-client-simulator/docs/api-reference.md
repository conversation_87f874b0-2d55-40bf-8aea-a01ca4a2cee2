# API接口文档

> 使用TypeScript AST自动生成于 2025-08-16T13:03:08.683Z

## 概览

- 微服务数量: 8
- API接口数量: 316
- 分类数量: 7

## ACTIVITY 分类

### energy.getInfo

- **描述**: 获取精力信息
- **微服务**: activity
- **模块**: energy
- **方法**: getEnergyInfo

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### energy.takeDaily

- **描述**: 领取每日精力
- **微服务**: activity
- **模块**: energy
- **方法**: takeEveryDayEnergy

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `timeSlot` (object) 必需 - timeSlot参数

### energy.consume

- **描述**: 消耗精力
- **微服务**: activity
- **模块**: energy
- **方法**: consumeEnergy

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `amount` (number) 必需 - amount参数

### energy.claimReward

- **描述**: 领取精力消耗奖励
- **微服务**: activity
- **模块**: energy
- **方法**: claimConsumeReward

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `rewardIndex` (number) 必需 - rewardIndex参数

### energy.buyFormationGift

- **描述**: 购买限时阵型重置礼包
- **微服务**: activity
- **模块**: energy
- **方法**: buyTeamFormationGift

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### energy.getStats

- **描述**: 获取精力统计信息（管理接口）
- **微服务**: activity
- **模块**: energy
- **方法**: getEnergyStats

**参数**:

- `adminToken` (string) 可选 - adminToken参数

### event.getList

- **描述**: 获取活动列表
- **微服务**: activity
- **模块**: event
- **方法**: getEventList

**参数**:

- `characterId` (string) 必需 - characterId参数

### event.join

- **描述**: 参与活动
- **微服务**: activity
- **模块**: event
- **方法**: joinEvent

**参数**:

- `characterId` (string) 必需 - characterId参数
- `eventId` (number) 必需 - eventId参数

### event.claimReward

- **描述**: 领取活动奖励
- **微服务**: activity
- **模块**: event
- **方法**: claimEventReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `eventId` (number) 必需 - eventId参数
- `rewardId` (number) 必需 - rewardId参数

### event.getProgress

- **描述**: 获取活动进度
- **微服务**: activity
- **模块**: event
- **方法**: getEventProgress

**参数**:

- `characterId` (string) 必需 - characterId参数
- `eventId` (number) 必需 - eventId参数

### event.buyBestFootball

- **描述**: 最佳11人抽奖
- **微服务**: activity
- **模块**: event
- **方法**: buyBestFootball

**参数**:

- `characterId` (string) 必需 - characterId参数
- `index` (number) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### event.buyTurntable

- **描述**: 老虎机抽奖
- **微服务**: activity
- **模块**: event
- **方法**: buyTurntable

**参数**:

- `characterId` (string) 必需 - characterId参数
- `frequencyType` (number) 必需 - frequencyType参数

### event.buySlots

- **描述**: 拉霸抽奖
- **微服务**: activity
- **模块**: event
- **方法**: buySlots

**参数**:

- `characterId` (string) 必需 - characterId参数
- `frequencyType` (number) 必需 - frequencyType参数
- `securityMoney` (number) 必需 - securityMoney参数
- `serverId` (string) 可选 - serverId参数

### event.weekDayEncore

- **描述**: 周末返场抽奖 基于old项目: Act.prototype.weekDayEncore
- **微服务**: activity
- **模块**: event
- **方法**: weekDayEncore

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### guide.getStatus

- **描述**: 获取新手引导状态
- **微服务**: activity
- **模块**: guide
- **方法**: getGuideStatus

**参数**:

- `characterId` (string) 必需 - characterId参数

### guide.completeStep

- **描述**: 完成引导步骤
- **微服务**: activity
- **模块**: guide
- **方法**: completeGuideStep

**参数**:

- `characterId` (string) 必需 - characterId参数
- `stepId` (number) 必需 - stepId参数

### guide.skip

- **描述**: 跳过引导
- **微服务**: activity
- **模块**: guide
- **方法**: skipGuide

**参数**:

- `characterId` (string) 必需 - characterId参数

### guide.reset

- **描述**: 重置引导
- **微服务**: activity
- **模块**: guide
- **方法**: resetGuide

**参数**:

- `characterId` (string) 必需 - characterId参数

### honor.getInfo

- **描述**: 获取荣誉墙信息
- **微服务**: activity
- **模块**: honor
- **方法**: getHonorInfo

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### honor.claimTaskReward

- **描述**: 领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能
- **微服务**: activity
- **模块**: honor
- **方法**: getHonorTaskReward

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `taskId` (number) 必需 - taskId参数

### honor.updateProgress

- **描述**: 更新荣誉任务进度
- **微服务**: activity
- **模块**: honor
- **方法**: updateHonorTaskProgress

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `taskId` (number) 必需 - taskId参数
- `progress` (number) 必需 - progress参数

### honor.addTask

- **描述**: 添加荣誉任务
- **微服务**: activity
- **模块**: honor
- **方法**: addHonorTask

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `taskConfig` (any) 必需 - taskConfig参数

### honor.claimLevelReward

- **描述**: 获取荣誉等级奖励
- **微服务**: activity
- **模块**: honor
- **方法**: getHonorLevelReward

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `level` (number) 必需 - level参数

### honor.trigger

- **描述**: 触发荣誉任务检查
- **微服务**: activity
- **模块**: honor
- **方法**: triggerHonorTask

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `triggerType` (number) 必需 - triggerType参数
- `param` (any) 可选 - param参数

### honor.getRanking

- **描述**: 获取荣誉排行榜
- **微服务**: activity
- **模块**: honor
- **方法**: getHonorRanking

**参数**:

- `limit` (number) 可选 - limit参数

### sign.daily

- **描述**: 每日签到
- **微服务**: activity
- **模块**: sign
- **方法**: dailySign

**参数**:

- `characterId` (string) 必需 - characterId参数
- `openServerTime` (number) 可选 - openServerTime参数

### sign.getStatus

- **描述**: 获取签到状态
- **微服务**: activity
- **模块**: sign
- **方法**: getSignStatus

**参数**:

- `characterId` (string) 必需 - characterId参数
- `openServerTime` (number) 可选 - openServerTime参数

### sign.sevenDayReward

- **描述**: 七日签到奖励
- **微服务**: activity
- **模块**: sign
- **方法**: sevenDaySignReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `day` (number) 必需 - day参数

### sign.makeUp

- **描述**: 补签
- **微服务**: activity
- **模块**: sign
- **方法**: makeUpSign

**参数**:

- `characterId` (string) 必需 - characterId参数
- `targetDay` (number) 必需 - targetDay参数

### task.getList

- **描述**: 获取任务列表
- **微服务**: activity
- **模块**: task
- **方法**: getTaskList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `query` (object) 可选 - query参数

### task.updateProgress

- **描述**: 更新任务进度
- **微服务**: activity
- **模块**: task
- **方法**: updateTaskProgress

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `updateDto` (object) 必需 - updateDto参数

### task.batchUpdateProgress

- **描述**: 批量更新任务进度
- **微服务**: activity
- **模块**: task
- **方法**: batchUpdateTaskProgress

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `batchDto` (object) 必需 - batchDto参数

### task.claimReward

- **描述**: 领取任务奖励
- **微服务**: activity
- **模块**: task
- **方法**: claimTaskReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `claimDto` (object) 必需 - claimDto参数

### task.refresh

- **描述**: 刷新任务
- **微服务**: activity
- **模块**: task
- **方法**: refreshTasks

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `refreshDto` (object) 必需 - refreshDto参数

### task.add

- **描述**: 添加新任务
- **微服务**: activity
- **模块**: task
- **方法**: addTask

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `addDto` (object) 必需 - addDto参数

### task.getStats

- **描述**: 获取任务统计
- **微服务**: activity
- **模块**: task
- **方法**: getTaskStats

**参数**:

- `serverId` (string) 必需 - serverId参数
- `query` (object) 必需 - query参数

### task.getLeaderboard

- **描述**: 获取任务排行榜
- **微服务**: activity
- **模块**: task
- **方法**: getTaskLeaderboard

**参数**:

- `serverId` (string) 必需 - serverId参数
- `taskType` (object) 可选 - taskType参数
- `limit` (number) 可选 - limit参数

### task.batchRefresh

- **描述**: 批量刷新任务（定时任务用）
- **微服务**: activity
- **模块**: task
- **方法**: batchRefreshTasks

**参数**:

- `taskType` (object) 必需 - taskType参数

### task.cleanExpired

- **描述**: 清理过期任务
- **微服务**: activity
- **模块**: task
- **方法**: cleanExpiredTasks

### task.getDailyTasks

- **描述**: 获取每日任务
- **微服务**: activity
- **模块**: task
- **方法**: getDailyTasks

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### task.getNewbieTasks

- **描述**: 获取新手任务
- **微服务**: activity
- **模块**: task
- **方法**: getNewbieTasks

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### task.trigger

- **描述**: 触发任务进度更新 对应old项目中最核心的triggerTask方法
- **微服务**: activity
- **模块**: task
- **方法**: triggerTask

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `triggerType` (number) 必需 - triggerType参数
- `arg1` (any) 可选 - arg1参数
- `arg2` (any) 可选 - arg2参数
- `arg3` (any) 可选 - arg3参数
- `arg4` (any) 可选 - arg4参数

### task.delete

- **描述**: 删除任务
- **微服务**: activity
- **模块**: task
- **方法**: deleteTask

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `taskType` (object) 必需 - taskType参数
- `taskId` (number) 必需 - taskId参数

### task.checkFull

- **描述**: 检查任务列表是否已满
- **微服务**: activity
- **模块**: task
- **方法**: checkTaskListFull

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `taskType` (object) 必需 - taskType参数

### task.checkExists

- **描述**: 检查任务是否存在
- **微服务**: activity
- **模块**: task
- **方法**: checkTaskExists

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `taskId` (number) 必需 - taskId参数

### task.updateActivityProgress

- **描述**: 更新活动任务进度 对应old项目act.js中的updateTaskProgress方法
- **微服务**: activity
- **模块**: task
- **方法**: updateActivityTaskProgress

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `activityId` (string) 必需 - activityId参数
- `taskId` (number) 必需 - taskId参数
- `progress` (number) 必需 - progress参数

### task.updateGoldCoachProgress

- **描述**: 更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法
- **微服务**: activity
- **模块**: task
- **方法**: updateGoldCoachTaskProgress

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `coachAction` (string) 必需 - coachAction参数
- `param` (any) 可选 - param参数

### task.batchUpdateActivity

- **描述**: 批量更新活动任务
- **微服务**: activity
- **模块**: task
- **方法**: batchUpdateActivityTasks

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `updates` (array) 必需 - updates参数

## AUTH 分类

### validateApiKey

- **描述**: 验证API Key - 微服务调用
- **微服务**: auth
- **模块**: api-key
- **方法**: validateApiKeyMicroservice

**参数**:

- `apiKey` (string) 必需 - apiKey参数
- `clientIp` (string) 可选 - clientIp参数
- `userAgent` (string) 可选 - userAgent参数

### generateApiKey

- **描述**: 生成API Key - 微服务调用
- **微服务**: auth
- **模块**: api-key
- **方法**: generateApiKeyMicroservice

**参数**:

- `userId` (string) 必需 - userId参数
- `name` (string) 必需 - name参数
- `permissions` (array) 可选 - permissions参数
- `expiresAt` (object) 可选 - expiresAt参数
- `ipWhitelist` (array) 可选 - ipWhitelist参数

### revokeApiKey

- **描述**: 撤销API Key - 微服务调用
- **微服务**: auth
- **模块**: api-key
- **方法**: revokeApiKeyMicroservice

**参数**:

- `apiKey` (string) 必需 - apiKey参数

### listApiKeys

- **描述**: 列出用户的API Keys - 微服务调用
- **微服务**: auth
- **模块**: api-key
- **方法**: listApiKeysMicroservice

**参数**:

- `userId` (string) 必需 - userId参数

### character-auth.generateCharacterToken

- **描述**: 生成角色Token - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: generateCharacterTokenMicroservice

**参数**:

- `userId` (string) 必需 - userId参数
- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `characterName` (string) 可选 - characterName参数
- `sessionData` (any) 可选 - sessionData参数

### character-auth.characterLogout

- **描述**: 角色登出 - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: characterLogoutMicroservice

**参数**:

- `characterToken` (string) 必需 - characterToken参数

### character-auth.refreshCharacterToken

- **描述**: 刷新角色Token - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: refreshCharacterTokenMicroservice

**参数**:

- `characterToken` (string) 必需 - characterToken参数

### character-auth.getCurrentSession

- **描述**: 获取当前会话信息 - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: getCurrentSessionMicroservice

**参数**:

- `characterToken` (string) 必需 - characterToken参数

### character-auth.verifyCharacterToken

- **描述**: 验证角色Token - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: verifyCharacterTokenMicroservice

**参数**:

- `characterToken` (string) 必需 - characterToken参数

### character-auth.terminateUserCharacterSessions

- **描述**: 批量终止用户角色会话 - 微服务调用
- **微服务**: auth
- **模块**: auth
- **方法**: terminateUserCharacterSessionsMicroservice

**参数**:

- `userId` (string) 必需 - userId参数

### cache-expression-test

- **描述**: 缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作
- **微服务**: auth
- **模块**: health
- **方法**: testCacheExpressionWS

**参数**:

- `testKey` (string) 必需 - testKey参数

### manual-cache-test

- **描述**: 手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作
- **微服务**: auth
- **模块**: health
- **方法**: testManualCacheWS

**参数**:

- `testKey` (string) 必需 - testKey参数

### manual-cache-update

- **描述**: 手动缓存更新测试 - 微服务调用
- **微服务**: auth
- **模块**: health
- **方法**: updateManualCacheWS

**参数**:

- `testKey` (string) 必需 - testKey参数
- `updateData` (any) 必需 - updateData参数

### manual-cache-clear

- **描述**: 手动缓存清除测试 - 微服务调用
- **微服务**: auth
- **模块**: health
- **方法**: clearManualCacheWS

**参数**:

- `testKey` (string) 必需 - testKey参数

### user.changePassword

- **描述**: 修改密码 - 微服务调用
- **微服务**: auth
- **模块**: user
- **方法**: changePasswordMicroservice

**参数**:

- `userId` (string) 必需 - userId参数
- `oldPassword` (string) 必需 - oldPassword参数
- `newPassword` (string) 必需 - newPassword参数
- `confirmNewPassword` (string) 必需 - confirmNewPassword参数

### user.getUserProfile

- **描述**: 获取用户资料 - 微服务调用
- **微服务**: auth
- **模块**: user
- **方法**: getUserProfileMicroservice

**参数**:

- `userId` (string) 必需 - userId参数

## CHARACTER 分类

### character.create

- **描述**: 创建新角色
- **微服务**: character
- **模块**: character
- **方法**: createCharacter

**参数**:

- `createDto` (object) 必需 - createDto参数

### character.login

- **描述**: 角色登录
- **微服务**: character
- **模块**: character
- **方法**: loginCharacter

**参数**:

- `loginDto` (object) 必需 - loginDto参数

### character.logout

- **描述**: 角色登出
- **微服务**: character
- **模块**: character
- **方法**: logoutCharacter

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### character.getInfo

- **描述**: 获取角色信息
- **微服务**: character
- **模块**: character
- **方法**: getCharacterInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### character.update

- **描述**: 更新角色信息
- **微服务**: character
- **模块**: character
- **方法**: updateCharacter

**参数**:

- `characterId` (string) 必需 - characterId参数
- `updateDto` (object) 必需 - updateDto参数
- `serverId` (string) 可选 - serverId参数

### character.getList

- **描述**: 获取角色列表
- **微服务**: character
- **模块**: character
- **方法**: getCharacterList

**参数**:

- `getCharacterListDto` (object) 必需 - getCharacterListDto参数

### character.energy.buy

- **描述**: 购买体力
- **微服务**: character
- **模块**: character
- **方法**: buyEnergy

**参数**:

- `characterId` (string) 必需 - characterId参数
- `buyDto` (object) 必需 - buyDto参数
- `serverId` (string) 可选 - serverId参数

### character.levelup

- **描述**: 角色升级
- **微服务**: character
- **模块**: character
- **方法**: levelUp

**参数**:

- `characterId` (string) 必需 - characterId参数
- `levelUpDto` (object) 必需 - levelUpDto参数
- `serverId` (string) 可选 - serverId参数

### character.progress.completeStep

- **描述**: 完成创角步骤
- **微服务**: character
- **模块**: character
- **方法**: completeCreateStep

**参数**:

- `characterId` (string) 必需 - characterId参数
- `step` (number) 必需 - step参数
- `serverId` (string) 可选 - serverId参数

### character.progress.finishGuide

- **描述**: 完成新手引导
- **微服务**: character
- **模块**: character
- **方法**: finishGuide

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### character.setBelief

- **描述**: 设置角色信仰
- **微服务**: character
- **模块**: character
- **方法**: setCharacterBelief

**参数**:

- `characterId` (string) 必需 - characterId参数
- `beliefId` (number) 必需 - beliefId参数

### character.useRedeemCode

- **描述**: 使用兑换码
- **微服务**: character
- **模块**: character
- **方法**: useRedeemCode

**参数**:

- `characterId` (string) 必需 - characterId参数
- `group` (string) 必需 - group参数
- `codeId` (string) 必需 - codeId参数

### character.updateBuff

- **描述**: 更新持续buff
- **微服务**: character
- **模块**: character
- **方法**: updateContinuedBuff

**参数**:

- `characterId` (string) 必需 - characterId参数
- `buffDuration` (number) 必需 - buffDuration参数

### character.searchByName

- **描述**: 根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索
- **微服务**: character
- **模块**: character
- **方法**: searchCharacterByName

**参数**:

- `name` (string) 必需 - name参数
- `serverId` (string) 可选 - serverId参数

### character.getPersonInfo

- **描述**: 获取个人信息 对应old项目: game.player.getPersonInfo
- **微服务**: character
- **模块**: character
- **方法**: getPersonInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### character.createRole

- **描述**: 创建角色 对应old项目: game.player.createRole
- **微服务**: character
- **模块**: character
- **方法**: createRole

**参数**:

- `characterId` (string) 必需 - characterId参数
- `qualified` (number) 必需 - qualified参数
- `name` (string) 必需 - name参数
- `faceIcon` (number) 必需 - faceIcon参数
- `serverId` (string) 可选 - serverId参数

### character.modifyCharacterName

- **描述**: 修改玩家名称 对应old项目: game.player.modifyPlayerName
- **微服务**: character
- **模块**: character
- **方法**: modifyCharacterName

**参数**:

- `characterId` (string) 必需 - characterId参数
- `name` (string) 必需 - name参数
- `serverId` (string) 可选 - serverId参数

### character.getEnergyReward

- **描述**: 获取体力奖励 对应old项目: game.player.getEnergyReward
- **微服务**: character
- **模块**: character
- **方法**: getEnergyReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `type` (number) 必需 - type参数
- `serverId` (string) 可选 - serverId参数

### character.costCashTask

- **描述**: 消耗金币任务 对应old项目: game.player.costCashTask
- **微服务**: character
- **模块**: character
- **方法**: costCashTask

**参数**:

- `characterId` (string) 必需 - characterId参数
- `amount` (number) 必需 - amount参数
- `reason` (string) 可选 - reason参数
- `serverId` (string) 可选 - serverId参数

### character.getScoutData

- **描述**: 获取角色球探数据 基于old项目Scout实体
- **微服务**: character
- **模块**: character
- **方法**: getScoutData

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### character.updateScoutData

- **描述**: 更新角色球探数据 基于old项目Scout实体
- **微服务**: character
- **模块**: character
- **方法**: updateScoutData

**参数**:

- `characterId` (string) 必需 - characterId参数
- `scoutData` (any) 必需 - scoutData参数
- `serverId` (string) 可选 - serverId参数

### character.initializeFromAuth

- **描述**: 接收Auth服务的角色初始化通知
- **微服务**: character
- **模块**: character
- **方法**: initializeFromAuth

**参数**:

- `characterId` (string) 必需 - characterId参数
- `userId` (string) 必需 - userId参数
- `serverId` (string) 必需 - serverId参数
- `characterName` (string) 必需 - characterName参数
- `initialData` (any) 可选 - initialData参数

### formation.getFormations

- **描述**: 获取角色阵容数据 对应old项目: TeamFormations实体的toJSONforClient
- **微服务**: character
- **模块**: formation
- **方法**: getCharacterFormations

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### formation.createFormation

- **描述**: 创建阵容 对应old项目: newTeamFormation方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: createFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `resId` (number) 必需 - resId参数
- `type` (number) 可选 - type参数
- `serverId` (string) 可选 - serverId参数

### formation.addHeroToPosition

- **描述**: 添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: addHeroToPosition

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `position` (string) 必需 - position参数
- `index` (number) 必需 - index参数
- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### formation.removeHeroFromPosition

- **描述**: 从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: removeHeroFromPosition

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `position` (string) 必需 - position参数
- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### formation.setActiveFormation

- **描述**: 设置当前激活阵容 对应old项目: setCurrTeamFormationId方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: setActiveFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `serverId` (string) 可选 - serverId参数

### formation.setLeagueFormation

- **描述**: 设置联赛专用阵容 对应old项目: setLeagueTeamFormationId方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: setLeagueFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `serverId` (string) 可选 - serverId参数

### formation.setWarOfFaithFormation

- **描述**: 设置信仰之战专用阵容 对应old项目: setWarOfFaithTeamFormationId方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: setWarOfFaithFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `serverId` (string) 可选 - serverId参数

### formation.autoFormation

- **描述**: 自动布阵 对应old项目: autoFormation方法
- **微服务**: character
- **模块**: formation
- **方法**: autoFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `heroIds` (array) 可选 - heroIds参数
- `serverId` (string) 可选 - serverId参数

### formation.copyFormation

- **描述**: 复制阵容 对应old项目: copyTeamFormation方法，优化API命名
- **微服务**: character
- **模块**: formation
- **方法**: copyFormation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `sourceFormationId` (string) 必需 - sourceFormationId参数
- `serverId` (string) 可选 - serverId参数

### formation.setFormationTactics

- **描述**: 设置阵容战术 对应old项目: setFormationTactics方法
- **微服务**: character
- **模块**: formation
- **方法**: setFormationTactics

**参数**:

- `characterId` (string) 必需 - characterId参数
- `uid` (string) 必需 - uid参数
- `resId` (number) 必需 - resId参数
- `tacticsType` (string) 必需 - tacticsType参数
- `serverId` (string) 可选 - serverId参数

### inventory.getBag

- **描述**: 获取角色背包数据 对应old项目: Bag实体的toJSONforClient
- **微服务**: character
- **模块**: inventory
- **方法**: getCharacterBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### inventory.getBagList

- **描述**: 获取背包列表（客户端格式） 对应old项目: makeClientBagList方法
- **微服务**: character
- **模块**: inventory
- **方法**: getBagList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### inventory.getBookMark

- **描述**: 获取指定页签数据 对应old项目: getOneBookMark方法
- **微服务**: character
- **模块**: inventory
- **方法**: getBookMark

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `serverId` (string) 可选 - serverId参数

### inventory.addItemToBag

- **描述**: 添加物品到背包 对应old项目: addToBag方法
- **微服务**: character
- **模块**: inventory
- **方法**: addItemToBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `itemId` (string) 必需 - itemId参数
- `serverId` (string) 可选 - serverId参数

### inventory.removeItemFromBag

- **描述**: 从背包移除物品 对应old项目: removeFromBag方法
- **微服务**: character
- **模块**: inventory
- **方法**: removeItemFromBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `itemId` (string) 必需 - itemId参数
- `serverId` (string) 可选 - serverId参数

### inventory.expandBag

- **描述**: 扩展背包容量 对应old项目: expandBag方法
- **微服务**: character
- **模块**: inventory
- **方法**: expandBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `expandCount` (number) 可选 - expandCount参数
- `serverId` (string) 可选 - serverId参数

### inventory.sortBag

- **描述**: 整理背包 对应old项目: sortBag方法
- **微服务**: character
- **模块**: inventory
- **方法**: sortBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `sortType` (string) 可选 - sortType参数
- `serverId` (string) 可选 - serverId参数

### inventory.useItem

- **描述**: 使用物品 对应old项目: useItemMainType方法
- **微服务**: character
- **模块**: inventory
- **方法**: useItem

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `itemId` (string) 必需 - itemId参数
- `configId` (number) 必需 - configId参数
- `useType` (number) 必需 - useType参数
- `subType` (number) 必需 - subType参数
- `quantity` (number) 可选 - quantity参数
- `heroId` (string) 可选 - heroId参数
- `serverId` (string) 可选 - serverId参数

### inventory.findItemInBag

- **描述**: 查找物品在背包中的位置 对应old项目: findItemInBag方法
- **微服务**: character
- **模块**: inventory
- **方法**: findItemInBag

**参数**:

- `characterId` (string) 必需 - characterId参数
- `itemId` (string) 必需 - itemId参数
- `serverId` (string) 可选 - serverId参数

### inventory.calculateExpandCost

- **描述**: 计算扩展费用 对应old项目: calculateExpandCost方法
- **微服务**: character
- **模块**: inventory
- **方法**: calculateExpandCost

**参数**:

- `characterId` (string) 必需 - characterId参数
- `bookMarkId` (number) 必需 - bookMarkId参数
- `expandCount` (number) 可选 - expandCount参数
- `serverId` (string) 可选 - serverId参数

### inventory.batchOperateItems

- **描述**: 批量操作物品 对应old项目的批量操作逻辑
- **微服务**: character
- **模块**: inventory
- **方法**: batchOperateItems

**参数**:

- `characterId` (string) 必需 - characterId参数
- `operations` (array) 必需 - operations参数
- `serverId` (string) 可选 - serverId参数

### item.getItems

- **描述**: 获取角色物品数据 对应old项目: Item实体的toJSONforDB
- **微服务**: character
- **模块**: item
- **方法**: getCharacterItems

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### item.addItem

- **描述**: 添加物品 对应old项目: addItem方法
- **微服务**: character
- **模块**: item
- **方法**: addItem

**参数**:

- `characterId` (string) 必需 - characterId参数
- `resId` (number) 必需 - resId参数
- `num` (number) 必需 - num参数
- `serverId` (string) 可选 - serverId参数

### item.removeItem

- **描述**: 移除物品 对应old项目: delItem方法，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: removeItem

**参数**:

- `characterId` (string) 必需 - characterId参数
- `itemId` (string) 必需 - itemId参数
- `quantity` (number) 必需 - quantity参数
- `serverId` (string) 可选 - serverId参数

### item.useItem

- **描述**: 使用物品 对应old项目: useItem方法
- **微服务**: character
- **模块**: item
- **方法**: useItem

**参数**:

- `characterId` (string) 必需 - characterId参数
- `itemId` (string) 必需 - itemId参数
- `quantity` (number) 可选 - quantity参数
- `serverId` (string) 可选 - serverId参数

### item.getItemQuantity

- **描述**: 获取物品数量 对应old项目: getItemNum方法，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: getItemQuantity

**参数**:

- `characterId` (string) 必需 - characterId参数
- `itemId` (string) 必需 - itemId参数
- `serverId` (string) 可选 - serverId参数

### item.getItemQuantityByConfigId

- **描述**: 根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: getItemQuantityByConfigId

**参数**:

- `characterId` (string) 必需 - characterId参数
- `configId` (number) 必需 - configId参数
- `serverId` (string) 可选 - serverId参数

### item.checkItemSufficient

- **描述**: 检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: checkItemSufficient

**参数**:

- `characterId` (string) 必需 - characterId参数
- `configId` (number) 必需 - configId参数
- `requiredQuantity` (number) 必需 - requiredQuantity参数
- `serverId` (string) 可选 - serverId参数

### item.createItemInstance

- **描述**: 创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: createItemInstance

**参数**:

- `configId` (number) 必需 - configId参数
- `quantity` (number) 必需 - quantity参数

### item.batchAddItems

- **描述**: 批量操作物品 对应old项目的批量操作逻辑
- **微服务**: character
- **模块**: item
- **方法**: batchAddItems

**参数**:

- `characterId` (string) 必需 - characterId参数
- `items` (array) 必需 - items参数
- `serverId` (string) 可选 - serverId参数

### item.batchRemoveItems

- **描述**: 批量移除物品 对应old项目的批量删除逻辑，优化API命名
- **微服务**: character
- **模块**: item
- **方法**: batchRemoveItems

**参数**:

- `characterId` (string) 必需 - characterId参数
- `items` (array) 必需 - items参数
- `serverId` (string) 可选 - serverId参数

### tactic.getTactics

- **描述**: 获取角色战术数据 对应old项目: 战术系统的数据获取
- **微服务**: character
- **模块**: tactic
- **方法**: getCharacterTactics

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### tactic.getTacticList

- **描述**: 获取战术列表（客户端格式） 对应old项目: makeClientTacticList方法
- **微服务**: character
- **模块**: tactic
- **方法**: getTacticList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### tactic.getTacticInfo

- **描述**: 获取战术信息 对应old项目: getTacticInfo方法
- **微服务**: character
- **模块**: tactic
- **方法**: getTacticInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `serverId` (string) 可选 - serverId参数

### tactic.activateTactic

- **描述**: 激活战术 对应old项目: activateTactic方法
- **微服务**: character
- **模块**: tactic
- **方法**: activateTactic

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `serverId` (string) 可选 - serverId参数

### tactic.upgradeTactic

- **描述**: 升级战术 对应old项目: upgradeTactic方法
- **微服务**: character
- **模块**: tactic
- **方法**: upgradeTactic

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `serverId` (string) 可选 - serverId参数

### tactic.unlockTactic

- **描述**: 解锁战术 对应old项目: unlockTactic方法
- **微服务**: character
- **模块**: tactic
- **方法**: unlockTactic

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `serverId` (string) 可选 - serverId参数

### tactic.setFormationTactics

- **描述**: 设置阵容战术 对应old项目: setFormationTactics方法
- **微服务**: character
- **模块**: tactic
- **方法**: setFormationTactics

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `attackTacticId` (string) 必需 - attackTacticId参数
- `defenseTacticId` (string) 必需 - defenseTacticId参数
- `serverId` (string) 可选 - serverId参数

### tactic.getFormationTactics

- **描述**: 获取阵容战术配置 对应old项目: getFormationTactics方法
- **微服务**: character
- **模块**: tactic
- **方法**: getFormationTactics

**参数**:

- `characterId` (string) 必需 - characterId参数
- `formationId` (string) 必需 - formationId参数
- `serverId` (string) 可选 - serverId参数

### tactic.calculateTacticEffects

- **描述**: 计算战术效果 对应old项目: calcTacticEffects方法
- **微服务**: character
- **模块**: tactic
- **方法**: calculateTacticEffects

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `level` (number) 必需 - level参数
- `serverId` (string) 可选 - serverId参数

### tactic.calculateHeroBonus

- **描述**: 计算球员战术加成 对应old项目: calcHeroTacticsAttr方法
- **微服务**: character
- **模块**: tactic
- **方法**: calculateHeroBonus

**参数**:

- `characterId` (string) 必需 - characterId参数
- `heroId` (string) 必需 - heroId参数
- `formationId` (string) 必需 - formationId参数
- `serverId` (string) 可选 - serverId参数

### tactic.updateTacticUsage

- **描述**: 更新战术使用统计 对应old项目: updateTacticUsageStats方法
- **微服务**: character
- **模块**: tactic
- **方法**: updateTacticUsage

**参数**:

- `characterId` (string) 必需 - characterId参数
- `tacticKey` (string) 必需 - tacticKey参数
- `isWin` (boolean) 必需 - isWin参数
- `serverId` (string) 可选 - serverId参数

### tactic.batchOperateTactics

- **描述**: 批量操作战术 对应old项目的批量操作逻辑
- **微服务**: character
- **模块**: tactic
- **方法**: batchOperateTactics

**参数**:

- `characterId` (string) 必需 - characterId参数
- `operations` (array) 必需 - operations参数
- `serverId` (string) 可选 - serverId参数

## ECONOMY 分类

### currency.convert

- **描述**: 货币转换
- **微服务**: economy
- **模块**: currency
- **方法**: convertCurrency

**参数**:

- `fromCurrency` (string) 必需 - fromCurrency参数
- `toCurrency` (string) 必需 - toCurrency参数
- `amount` (number) 必需 - amount参数

### currency.getRate

- **描述**: 获取汇率
- **微服务**: economy
- **模块**: currency
- **方法**: getExchangeRate

**参数**:

- `fromCurrency` (string) 必需 - fromCurrency参数
- `toCurrency` (string) 必需 - toCurrency参数

### exchange.getInfo

- **描述**: 获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法
- **微服务**: economy
- **模块**: exchange
- **方法**: getExchangeHallInfo

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### exchange.compound

- **描述**: 合成物品 对应old项目中的compoundItem方法
- **微服务**: economy
- **模块**: exchange
- **方法**: compoundItem

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `resId` (number) 必需 - resId参数
- `num` (number) 必需 - num参数

### exchange.decompose

- **描述**: 分解物品 对应old项目中的decomposeItem方法
- **微服务**: economy
- **模块**: exchange
- **方法**: decomposeItem

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `resId` (number) 必需 - resId参数
- `num` (number) 必需 - num参数

### exchange.refresh

- **描述**: 刷新兑换大厅 对应old项目中的flushExchangeHall方法
- **微服务**: economy
- **模块**: exchange
- **方法**: flushExchangeHall

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `type` (number) 必需 - type参数
- `teamId` (number) 可选 - teamId参数

### exchange.exchangeItem

- **描述**: 兑换物品 对应old项目中的exchangeItem方法
- **微服务**: economy
- **模块**: exchange
- **方法**: exchangeItem

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `id` (number) 必需 - id参数

### exchange.getStats

- **描述**: 获取兑换统计信息（管理接口）
- **微服务**: economy
- **模块**: exchange
- **方法**: getExchangeStats

**参数**:

- `adminToken` (string) 可选 - adminToken参数

### lottery.lotteryHero

- **描述**: 球员抽奖 基于old项目: Player.prototype.lotteryHero
- **微服务**: economy
- **模块**: lottery
- **方法**: lotteryHero

### lottery.getLotteryConfig

- **描述**: 获取抽奖配置信息
- **微服务**: economy
- **模块**: lottery
- **方法**: getLotteryConfig

### lottery.getLotteryHistory

- **描述**: 获取抽奖历史记录
- **微服务**: economy
- **模块**: lottery
- **方法**: getLotteryHistory

### payment.process

- **描述**: 处理支付
- **微服务**: economy
- **模块**: payment
- **方法**: processPayment

**参数**:

- `data` (any) 必需 - data参数

### payment.verify

- **描述**: 验证支付
- **微服务**: economy
- **模块**: payment
- **方法**: verifyPayment

**参数**:

- `transactionId` (string) 必需 - transactionId参数

### relay.getInfo

- **描述**: 获取联赛转播信息
- **微服务**: economy
- **模块**: relay
- **方法**: getRelayInfo

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### relay.buy

- **描述**: 购买联赛转播 对应old项目中的buyRelay方法
- **微服务**: economy
- **模块**: relay
- **方法**: buyRelay

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### relay.convert

- **描述**: 商城兑换 对应old项目中的convertibility方法
- **微服务**: economy
- **模块**: relay
- **方法**: convertibility

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `id` (number) 必需 - id参数

### relay.receiveAward

- **描述**: 领取奖励 对应old项目中的receiveAward方法
- **微服务**: economy
- **模块**: relay
- **方法**: receiveAward

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `awardId` (number) 必需 - awardId参数

### relay.receiveAllAward

- **描述**: 一键领取所有奖励 对应old项目中的receiveAllAward方法
- **微服务**: economy
- **模块**: relay
- **方法**: receiveAllAward

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数

### relay.addIntegral

- **描述**: 添加积分（内部接口）
- **微服务**: economy
- **模块**: relay
- **方法**: addIntegral

**参数**:

- `uid` (string) 必需 - uid参数
- `serverId` (string) 必需 - serverId参数
- `amount` (number) 必需 - amount参数

### relay.getRanking

- **描述**: 获取积分排行榜
- **微服务**: economy
- **模块**: relay
- **方法**: getIntegralRanking

**参数**:

- `limit` (number) 可选 - limit参数

### relay.getStats

- **描述**: 获取转播统计信息（管理接口）
- **微服务**: economy
- **模块**: relay
- **方法**: getRelayStats

**参数**:

- `adminToken` (string) 可选 - adminToken参数

### shop.getInfo

- **描述**: 获取商店信息
- **微服务**: economy
- **模块**: shop
- **方法**: getShopInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `shopType` (object) 必需 - shopType参数

### shop.getList

- **描述**: 获取商店列表
- **微服务**: economy
- **模块**: shop
- **方法**: getShopList

**参数**:

- `getShopListDto` (object) 必需 - getShopListDto参数

### shop.purchase

- **描述**: 购买商品
- **微服务**: economy
- **模块**: shop
- **方法**: purchaseGoods

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `purchaseDto` (object) 必需 - purchaseDto参数

### shop.refresh

- **描述**: 刷新商店
- **微服务**: economy
- **模块**: shop
- **方法**: refreshShop

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `refreshDto` (object) 必需 - refreshDto参数

### shop.buyMonthCard

- **描述**: 购买月卡
- **微服务**: economy
- **模块**: shop
- **方法**: buyMonthCard

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `buyDto` (object) 必需 - buyDto参数

### shop.claimMonthCard

- **描述**: 领取月卡奖励
- **微服务**: economy
- **模块**: shop
- **方法**: claimMonthCardReward

**参数**:

- `claimDto` (object) 必需 - claimDto参数

### shop.getVipShop

- **描述**: 获取VIP商店信息
- **微服务**: economy
- **模块**: shop
- **方法**: getVipShop

**参数**:

- `vipShopDto` (object) 必需 - vipShopDto参数
- `serverId` (string) 必需 - serverId参数

### shop.getLimitShop

- **描述**: 获取限时商店信息
- **微服务**: economy
- **模块**: shop
- **方法**: getLimitShop

**参数**:

- `limitShopDto` (object) 必需 - limitShopDto参数
- `serverId` (string) 必需 - serverId参数

### shop.getPurchaseHistory

- **描述**: 获取购买历史
- **微服务**: economy
- **模块**: shop
- **方法**: getPurchaseHistory

**参数**:

- `query` (object) 必需 - query参数

### shop.getStats

- **描述**: 获取商店统计
- **微服务**: economy
- **模块**: shop
- **方法**: getShopStats

**参数**:

- `query` (object) 必需 - query参数

### shop.batchRefresh

- **描述**: 批量刷新商店（定时任务用）
- **微服务**: economy
- **模块**: shop
- **方法**: batchRefreshShops

**参数**:

- `cycle` (object) 必需 - cycle参数

### trade.create

- **描述**: 创建交易
- **微服务**: economy
- **模块**: trade
- **方法**: createTrade

**参数**:

- `tradeDto` (any) 必需 - tradeDto参数

### trade.confirm

- **描述**: 确认交易
- **微服务**: economy
- **模块**: trade
- **方法**: confirmTrade

**参数**:

- `tradeId` (string) 必需 - tradeId参数

## HERO 分类

### career.addContractDays

- **描述**: 增加球员合约天数 对应old项目: addHeroLeftDay
- **微服务**: hero
- **模块**: career
- **方法**: addHeroLeftDay

**参数**:

- `heroId` (string) 必需 - heroId参数
- `days` (number) 必需 - days参数
- `serverId` (string) 可选 - serverId参数

### career.renewContract

- **描述**: 续约球员 对应old项目: renewTheContract（支持批量续约）
- **微服务**: hero
- **模块**: career
- **方法**: renewTheContract

**参数**:

- `heroIds` (array) 必需 - heroIds参数
- `serverId` (string) 可选 - serverId参数

### career.promoteStatus

- **描述**: 提升球员状态 对应old项目: promoteHeroStatus（使用道具）
- **微服务**: hero
- **模块**: career
- **方法**: promoteHeroStatus

**参数**:

- `heroId` (string) 必需 - heroId参数
- `itemId` (number) 必需 - itemId参数
- `serverId` (string) 可选 - serverId参数

### career.addToRetirement

- **描述**: 加入退役名单 对应old项目: addHeroToRetirementList
- **微服务**: hero
- **模块**: career
- **方法**: addHeroToRetirementList

**参数**:

- `heroId` (string) 必需 - heroId参数
- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### career.processRetirement

- **描述**: 处理球员退役 检查并处理到期的球员
- **微服务**: hero
- **模块**: career
- **方法**: processRetirement

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### career.getStats

- **描述**: 获取球员生涯统计
- **微服务**: hero
- **模块**: career
- **方法**: getCareerStats

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### career.getExpiringContracts

- **描述**: 获取即将到期的球员列表
- **微服务**: hero
- **模块**: career
- **方法**: getExpiringContracts

**参数**:

- `characterId` (string) 必需 - characterId参数
- `days` (number) 必需 - days参数
- `serverId` (string) 可选 - serverId参数

### cultivation.cultivate

- **描述**: 球员养成 对应old项目: cultivateHero
- **微服务**: hero
- **模块**: cultivation
- **方法**: cultivateHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### cultivation.breakOut

- **描述**: 球员突破 对应old项目: breakOutHero
- **微服务**: hero
- **模块**: cultivation
- **方法**: breakOutHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `index` (number) 必需 - index参数
- `arr` (array) 可选 - arr参数
- `serverId` (string) 可选 - serverId参数

### cultivation.reBreakOut

- **描述**: 重新突破 对应old项目: reBreakOutHero
- **微服务**: hero
- **模块**: cultivation
- **方法**: reBreakOutHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### cultivation.upStar

- **描述**: 球员升星 对应old项目: heroUpStar
- **微服务**: hero
- **模块**: cultivation
- **方法**: heroUpStar

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### cultivation.oneKeyCultivate

- **描述**: 一键养成 对应old项目: oneKeyCultivateHero
- **微服务**: hero
- **模块**: cultivation
- **方法**: oneKeyCultivateHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `type` (number) 必需 - type参数
- `serverId` (string) 可选 - serverId参数

### ground.getTrainInfo

- **描述**: 获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo
- **微服务**: hero
- **模块**: ground
- **方法**: getHeroTrainInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### ground.train

- **描述**: 场地训练 对应old项目: game.groundService.heroTrainInGround
- **微服务**: hero
- **模块**: ground
- **方法**: heroTrainInGround

**参数**:

- `characterId` (string) 必需 - characterId参数
- `heroId` (string) 必需 - heroId参数
- `index` (number) 必需 - index参数
- `type` (number) 必需 - type参数
- `isLock` (boolean) 可选 - isLock参数
- `serverId` (string) 可选 - serverId参数

### ground.getReward

- **描述**: 获取场地训练奖励 对应old项目: game.groundService.getHeroTrainReward
- **微服务**: hero
- **模块**: ground
- **方法**: getHeroTrainReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `index` (number) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### ground.inputNotable

- **描述**: 名人堂入驻 对应old项目: game.groundService.inputNotablePos
- **微服务**: hero
- **模块**: ground
- **方法**: inputNotablePos

**参数**:

- `characterId` (string) 必需 - characterId参数
- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### ground.getNotableList

- **描述**: 获取名人堂列表 对应old项目: game.groundService.getNotablePosInfo
- **微服务**: hero
- **模块**: ground
- **方法**: getNotablePosInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### ground.getHospitalInfo

- **描述**: 获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo
- **微服务**: hero
- **模块**: ground
- **方法**: getHospitalPosInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### ground.inputHospital

- **描述**: 医疗中心治疗 对应old项目: game.groundService.inputHospitalPos
- **微服务**: hero
- **模块**: ground
- **方法**: inputHospitalPos

**参数**:

- `characterId` (string) 必需 - characterId参数
- `heroId` (string) 必需 - heroId参数
- `index` (number) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### ground.getHospitalReward

- **描述**: 获取医疗奖励 对应old项目: game.groundService.getHospitalPosHero
- **微服务**: hero
- **模块**: ground
- **方法**: getHospitalPosHero

**参数**:

- `characterId` (string) 必需 - characterId参数
- `index` (number) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### ground.getFansInfo

- **描述**: 获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息
- **微服务**: hero
- **模块**: ground
- **方法**: getFansInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### hero.getConfig

- **描述**: 获取球员配置信息
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroConfig

**参数**:

- `heroId` (number) 必需 - heroId参数

### hero.getConfigByPosition

- **描述**: 根据位置获取球员配置
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroConfigsByPosition

**参数**:

- `position` (object) 必需 - position参数
- `limit` (number) 可选 - limit参数

### hero.setTreatStatus

- **描述**: 设置球员治疗状态
- **微服务**: hero
- **模块**: hero
- **方法**: setHeroTreatStatus

**参数**:

- `heroId` (string) 必需 - heroId参数
- `isTreat` (boolean) 必需 - isTreat参数

### hero.updateFatigue

- **描述**: 更新球员疲劳值
- **微服务**: hero
- **模块**: hero
- **方法**: updateHeroFatigue

**参数**:

- `heroId` (string) 必需 - heroId参数
- `fatigueChange` (number) 必需 - fatigueChange参数

### hero.create

- **描述**: 创建新球员
- **微服务**: hero
- **模块**: hero
- **方法**: createHero

**参数**:

- `createDto` (object) 必需 - createDto参数

### hero.getBatch

- **描述**: 批量获取球员信息
- **微服务**: hero
- **模块**: hero
- **方法**: getBatchHeroes

**参数**:

- `heroIds` (array) 必需 - heroIds参数
- `serverId` (string) 可选 - serverId参数

### hero.getInfo

- **描述**: 获取球员信息
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroInfo

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.update

- **描述**: 更新球员信息
- **微服务**: hero
- **模块**: hero
- **方法**: updateHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `updateDto` (object) 必需 - updateDto参数
- `serverId` (string) 可选 - serverId参数

### hero.getList

- **描述**: 获取球员列表
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroList

**参数**:

- `query` (object) 必需 - query参数

### hero.levelUp

- **描述**: 球员升级
- **微服务**: hero
- **模块**: hero
- **方法**: levelUpHero

**参数**:

- `levelUpDto` (object) 必需 - levelUpDto参数
- `serverId` (string) 可选 - serverId参数

### hero.skill.upgrade

- **描述**: 技能升级
- **微服务**: hero
- **模块**: hero
- **方法**: upgradeSkill

**参数**:

- `skillDto` (object) 必需 - skillDto参数
- `serverId` (string) 可选 - serverId参数

### hero.market.operation

- **描述**: 市场操作
- **微服务**: hero
- **模块**: hero
- **方法**: marketOperation

**参数**:

- `marketDto` (object) 必需 - marketDto参数
- `serverId` (string) 可选 - serverId参数

### hero.getFormation

- **描述**: 获取阵容中的球员
- **微服务**: hero
- **模块**: hero
- **方法**: getFormationHeroes

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### hero.getMarket

- **描述**: 获取市场球员
- **微服务**: hero
- **模块**: hero
- **方法**: getMarketHeroes

**参数**:

- `page` (number) 可选 - page参数
- `limit` (number) 可选 - limit参数
- `serverId` (string) 可选 - serverId参数

### hero.getStats

- **描述**: 获取球员统计 基于old项目: 统计角色的球员数据
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroStats

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### hero.evolution.requirements

- **描述**: 获取升星需求
- **微服务**: hero
- **模块**: hero
- **方法**: getEvolutionRequirements

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.evolve

- **描述**: 球员升星
- **微服务**: hero
- **模块**: hero
- **方法**: evolveHero

**参数**:

- `evolveDto` (object) 必需 - evolveDto参数

### hero.status.get

- **描述**: 获取球员状态
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroStatus

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.treat

- **描述**: 治疗球员
- **微服务**: hero
- **模块**: hero
- **方法**: treatHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `treatmentType` (string) 可选 - treatmentType参数
- `serverId` (string) 可选 - serverId参数

### hero.contract.renew

- **描述**: 球员续约
- **微服务**: hero
- **模块**: hero
- **方法**: renewHeroContract

**参数**:

- `heroId` (string) 必需 - heroId参数
- `contractDays` (number) 必需 - contractDays参数
- `serverId` (string) 可选 - serverId参数

### hero.fatigue.recover

- **描述**: 恢复球员疲劳
- **微服务**: hero
- **模块**: hero
- **方法**: recoverHeroFatigue

**参数**:

- `heroId` (string) 必需 - heroId参数
- `recoveryValue` (number) 可选 - recoveryValue参数
- `serverId` (string) 可选 - serverId参数

### hero.breakthrough.info

- **描述**: 获取突破信息
- **微服务**: hero
- **模块**: hero
- **方法**: getBreakthroughInfo

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.breakthrough

- **描述**: 球员突破
- **微服务**: hero
- **模块**: hero
- **方法**: breakthroughHero

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.breakthrough.revert

- **描述**: 撤销突破
- **微服务**: hero
- **模块**: hero
- **方法**: revertBreakthrough

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.attributes.recalculate

- **描述**: 重新计算球员属性
- **微服务**: hero
- **模块**: hero
- **方法**: recalculateHeroAttributes

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.attributes.recalculateAll

- **描述**: 批量重新计算角色所有球员属性
- **微服务**: hero
- **模块**: hero
- **方法**: recalculateAllHeroAttributes

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### hero.career.renew

- **描述**: 球员续约
- **微服务**: hero
- **模块**: hero
- **方法**: renewHeroCareer

**参数**:

- `heroId` (string) 必需 - heroId参数
- `days` (number) 可选 - days参数
- `serverId` (string) 可选 - serverId参数

### hero.career.info

- **描述**: 获取球员生涯信息
- **微服务**: hero
- **模块**: hero
- **方法**: getHeroCareerInfo

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### hero.career.checkExpiration

- **描述**: 检查合约到期
- **微服务**: hero
- **模块**: hero
- **方法**: checkContractExpiration

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### hero.career.processRetirement

- **描述**: 处理球员退役
- **微服务**: hero
- **模块**: hero
- **方法**: processRetirement

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### scout.getPackInfo

- **描述**: 获取球探包信息 对应old项目: getScoutPackInfo
- **微服务**: hero
- **模块**: scout
- **方法**: getScoutPackInfo

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### scout.deletePackHero

- **描述**: 删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）
- **微服务**: hero
- **模块**: scout
- **方法**: deleteScoutPackHero

**参数**:

- `characterId` (string) 必需 - characterId参数
- `index` (array) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### scout.signHero

- **描述**: 签约球探球员 对应old项目: signScoutHero（使用resId而不是index）
- **微服务**: hero
- **模块**: scout
- **方法**: signScoutHero

**参数**:

- `characterId` (string) 必需 - characterId参数
- `resId` (number) 必需 - resId参数
- `serverId` (string) 可选 - serverId参数

### scout.explore

- **描述**: 球探探索（核心功能） 对应old项目: getScoutReward
- **微服务**: hero
- **模块**: scout
- **方法**: getScoutReward

**参数**:

- `characterId` (string) 必需 - characterId参数
- `type` (number) 必需 - type参数
- `serverId` (string) 可选 - serverId参数

### scout.search

- **描述**: 球探搜索 基于old项目扩展的球探搜索功能
- **微服务**: hero
- **模块**: scout
- **方法**: scoutSearch

**参数**:

- `characterId` (string) 必需 - characterId参数
- `scoutType` (string) 必需 - scoutType参数
- `targetQuality` (string) 可选 - targetQuality参数
- `serverId` (string) 可选 - serverId参数

### scout.exchangeScout

- **描述**: 球探RP值兑换 基于old项目Scout.exchangeScout方法
- **微服务**: hero
- **模块**: scout
- **方法**: exchangeScout

**参数**:

- `characterId` (string) 必需 - characterId参数
- `type` (number) 可选 - type参数
- `serverId` (string) 可选 - serverId参数

### scout.buyEnergy

- **描述**: 购买球探体力 基于old项目的体力购买机制
- **微服务**: hero
- **模块**: scout
- **方法**: buyScoutEnergy

**参数**:

- `characterId` (string) 必需 - characterId参数
- `amount` (number) 可选 - amount参数
- `serverId` (string) 可选 - serverId参数

### scout.recoverEnergy

- **描述**: 手动恢复球探体力 基于old项目的体力恢复机制
- **微服务**: hero
- **模块**: scout
- **方法**: recoverScoutEnergy

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 可选 - serverId参数

### skill.getConfig

- **描述**: 获取技能配置信息
- **微服务**: hero
- **模块**: skill
- **方法**: getSkillConfig

**参数**:

- `skillId` (number) 必需 - skillId参数

### skill.getConfigList

- **描述**: 获取技能配置列表
- **微服务**: hero
- **模块**: skill
- **方法**: getSkillConfigList

**参数**:

- `query` (object) 必需 - query参数

### skill.getConfigByPosition

- **描述**: 根据位置获取技能配置
- **微服务**: hero
- **模块**: skill
- **方法**: getSkillConfigsByPosition

**参数**:

- `position` (object) 必需 - position参数
- `limit` (number) 可选 - limit参数

### skill.learn

- **描述**: 球员学习技能
- **微服务**: hero
- **模块**: skill
- **方法**: learnSkill

**参数**:

- `learnDto` (object) 必需 - learnDto参数
- `serverId` (string) 可选 - serverId参数

### skill.upgrade

- **描述**: 升级球员技能
- **微服务**: hero
- **模块**: skill
- **方法**: upgradeSkill

**参数**:

- `upgradeDto` (object) 必需 - upgradeDto参数
- `serverId` (string) 可选 - serverId参数

### skill.activate

- **描述**: 激活球员技能
- **微服务**: hero
- **模块**: skill
- **方法**: activateSkill

**参数**:

- `activateDto` (object) 必需 - activateDto参数
- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### skill.deactivate

- **描述**: 取消激活球员技能
- **微服务**: hero
- **模块**: skill
- **方法**: deactivateSkill

**参数**:

- `skillId` (string) 必需 - skillId参数
- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### skill.getList

- **描述**: 获取球员技能列表
- **微服务**: hero
- **模块**: skill
- **方法**: getSkillList

**参数**:

- `query` (object) 必需 - query参数
- `serverId` (string) 可选 - serverId参数

### skill.getActive

- **描述**: 获取球员已激活的技能
- **微服务**: hero
- **模块**: skill
- **方法**: getActiveSkills

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### skill.reset

- **描述**: 重置球员技能
- **微服务**: hero
- **模块**: skill
- **方法**: resetSkills

**参数**:

- `heroId` (string) 必需 - heroId参数
- `resetType` (object) 必需 - resetType参数
- `serverId` (string) 可选 - serverId参数

### skill.batchOperation

- **描述**: 批量操作球员技能
- **微服务**: hero
- **模块**: skill
- **方法**: batchOperateSkills

**参数**:

- `heroId` (string) 必需 - heroId参数
- `operation` (object) 必需 - operation参数
- `skillIds` (array) 必需 - skillIds参数
- `serverId` (string) 可选 - serverId参数

### skill.getStats

- **描述**: 获取技能统计
- **微服务**: hero
- **模块**: skill
- **方法**: getSkillStats

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### skill.use

- **描述**: 使用技能
- **微服务**: hero
- **模块**: skill
- **方法**: useSkill

**参数**:

- `skillId` (string) 必需 - skillId参数
- `heroId` (string) 必需 - heroId参数
- `targetId` (string) 可选 - targetId参数
- `serverId` (string) 可选 - serverId参数

### training.getTrainInfo

- **描述**: 获取球员特训信息 对应old项目: getTrainInfo
- **微服务**: hero
- **模块**: training
- **方法**: getTrainInfo

**参数**:

- `heroId` (string) 必需 - heroId参数
- `serverId` (string) 可选 - serverId参数

### training.train

- **描述**: 训练球员 对应old项目: trainHero
- **微服务**: hero
- **模块**: training
- **方法**: trainHero

**参数**:

- `trainDto` (object) 必需 - trainDto参数
- `serverId` (string) 可选 - serverId参数

### training.replaceHeroTrain

- **描述**: 替换特训 对应old项目: replaceHeroTrain
- **微服务**: hero
- **模块**: training
- **方法**: replaceHeroTrain

**参数**:

- `heroId` (string) 必需 - heroId参数
- `index` (number) 必需 - index参数
- `serverId` (string) 可选 - serverId参数

### training.setTrainStatus

- **描述**: 设置球员训练状态 从hero模块迁移而来
- **微服务**: hero
- **模块**: training
- **方法**: setHeroTrainStatus

**参数**:

- `heroId` (string) 必需 - heroId参数
- `isTrain` (boolean) 必需 - isTrain参数
- `isLockTrain` (boolean) 可选 - isLockTrain参数

## MATCH 分类

### battle.pveBattle

- **描述**: PVE战斗 基于old项目的initPveBattle接口
- **微服务**: match
- **模块**: battle
- **方法**: pveBattle

**参数**:

- `pveBattleDto` (object) 必需 - pveBattleDto参数

### battle.pvpBattle

- **描述**: PVP战斗 基于old项目的pvpMatchBattle接口
- **微服务**: match
- **模块**: battle
- **方法**: pvpBattle

**参数**:

- `pvpBattleDto` (object) 必需 - pvpBattleDto参数

### battle.getBattleReplay

- **描述**: 获取战斗回放 基于old项目的战斗回放功能
- **微服务**: match
- **模块**: battle
- **方法**: getBattleReplay

**参数**:

- `getBattleReplayDto` (object) 必需 - getBattleReplayDto参数

### battle.deleteBattleRoom

- **描述**: 删除战斗房间 基于old项目的deleteBattleRoom接口
- **微服务**: match
- **模块**: battle
- **方法**: deleteBattleRoom

**参数**:

- `roomUid` (string) 必需 - roomUid参数

### battle.getStatistics

- **描述**: 获取战斗统计信息（管理接口）
- **微服务**: match
- **模块**: battle
- **方法**: getStatistics

### battle.cleanExpiredRooms

- **描述**: 清理过期房间（管理接口）
- **微服务**: match
- **模块**: battle
- **方法**: cleanExpiredRooms

### business.getBusinessMatchInfo

- **描述**: 获取商业赛信息 基于old项目的getBusinessMatchInfo接口
- **微服务**: match
- **模块**: business
- **方法**: getBusinessMatchInfo

**参数**:

- `getBusinessMatchInfoDto` (object) 必需 - getBusinessMatchInfoDto参数

### business.businessSearch

- **描述**: 商业赛搜索 基于old项目的businessSearch接口
- **微服务**: match
- **模块**: business
- **方法**: businessSearch

**参数**:

- `businessSearchDto` (object) 必需 - businessSearchDto参数

### business.businessMatch

- **描述**: 商业赛匹配 基于old项目的businessMatch接口
- **微服务**: match
- **模块**: business
- **方法**: businessMatch

**参数**:

- `businessMatchDto` (object) 必需 - businessMatchDto参数

### business.buyBusinessMatch

- **描述**: 购买商业赛次数 基于old项目的buyBusinessMatch接口
- **微服务**: match
- **模块**: business
- **方法**: buyBusinessMatch

**参数**:

- `buyBusinessMatchDto` (object) 必需 - buyBusinessMatchDto参数

### business.getStatistics

- **描述**: 获取商业赛统计信息（管理接口）
- **微服务**: match
- **模块**: business
- **方法**: getStatistics

### business.resetDailyFightTimes

- **描述**: 重置每日战斗次数（管理接口）
- **微服务**: match
- **模块**: business
- **方法**: resetDailyFightTimes

**参数**:

- `characterId` (string) 可选 - characterId参数

### league.getLeagueCopyData

- **描述**: 获取联赛副本数据 基于old项目的getLeagueCopyData接口
- **微服务**: match
- **模块**: league
- **方法**: getLeagueCopyData

**参数**:

- `getLeagueCopyDataDto` (object) 必需 - getLeagueCopyDataDto参数

### league.pveBattle

- **描述**: PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口
- **微服务**: match
- **模块**: league
- **方法**: pveBattle

**参数**:

- `pveLeagueBattleDto` (object) 必需 - pveLeagueBattleDto参数

### league.takeLeagueReward

- **描述**: 领取联赛奖励 基于old项目的联赛奖励发放逻辑
- **微服务**: match
- **模块**: league
- **方法**: takeLeagueReward

**参数**:

- `takeLeagueRewardDto` (object) 必需 - takeLeagueRewardDto参数

### league.buyLeagueTimes

- **描述**: 购买联赛次数 基于old项目的购买次数逻辑
- **微服务**: match
- **模块**: league
- **方法**: buyLeagueTimes

**参数**:

- `buyLeagueTimesDto` (object) 必需 - buyLeagueTimesDto参数

### league.getStatistics

- **描述**: 获取联赛统计信息（管理接口）
- **微服务**: match
- **模块**: league
- **方法**: getStatistics

### ranking.getGlobalRanking

- **描述**: 获取全球排名 基于old项目的全球排名功能
- **微服务**: match
- **模块**: ranking
- **方法**: getGlobalRanking

**参数**:

- `getGlobalRankingDto` (object) 必需 - getGlobalRankingDto参数

### ranking.getCharacterRanking

- **描述**: 获取玩家排名信息 基于old项目的玩家个人排名功能
- **微服务**: match
- **模块**: ranking
- **方法**: getCharacterRanking

**参数**:

- `getCharacterRankingDto` (object) 必需 - getCharacterRankingDto参数

### ranking.claimRankingReward

- **描述**: 领取排名奖励 基于old项目的排名奖励发放功能
- **微服务**: match
- **模块**: ranking
- **方法**: claimRankingReward

**参数**:

- `claimRankingRewardDto` (object) 必需 - claimRankingRewardDto参数

### ranking.updateRanking

- **描述**: 更新排名数据（管理接口） 基于old项目的排名更新功能
- **微服务**: match
- **模块**: ranking
- **方法**: updateRanking

**参数**:

- `updateRankingDto` (object) 必需 - updateRankingDto参数

### ranking.getStatistics

- **描述**: 获取排名统计信息（管理接口）
- **微服务**: match
- **模块**: ranking
- **方法**: getStatistics

### ranking.cleanExpiredRankings

- **描述**: 清理过期排名数据（管理接口）
- **微服务**: match
- **模块**: ranking
- **方法**: cleanExpiredRankings

### ranking.updateAllRankings

- **描述**: 批量更新所有排名（管理接口）
- **微服务**: match
- **模块**: ranking
- **方法**: updateAllRankings

**参数**:

- `season` (number) 可选 - season参数

### tournament.getWorldCupInfo

- **描述**: 获取世界杯信息 基于old项目的getWorldCupInfo接口
- **微服务**: match
- **模块**: tournament
- **方法**: getWorldCupInfo

**参数**:

- `getWorldCupInfoDto` (object) 必需 - getWorldCupInfoDto参数

### tournament.joinWorldCup

- **描述**: 参加世界杯 基于old项目的joinWorldCup接口
- **微服务**: match
- **模块**: tournament
- **方法**: joinWorldCup

**参数**:

- `joinWorldCupDto` (object) 必需 - joinWorldCupDto参数

### tournament.worldCupBattle

- **描述**: 世界杯战斗 基于old项目的worldCupBattle接口
- **微服务**: match
- **模块**: tournament
- **方法**: worldCupBattle

**参数**:

- `worldCupBattleDto` (object) 必需 - worldCupBattleDto参数

### tournament.buyWorldCupTimes

- **描述**: 购买世界杯次数 基于old项目的buyWorldCupTimes接口
- **微服务**: match
- **模块**: tournament
- **方法**: buyWorldCupTimes

**参数**:

- `buyWorldCupTimesDto` (object) 必需 - buyWorldCupTimesDto参数

### tournament.getWorldCupReward

- **描述**: 领取世界杯奖励 基于old项目的getWorldCupReward接口
- **微服务**: match
- **模块**: tournament
- **方法**: getWorldCupReward

**参数**:

- `getWorldCupRewardDto` (object) 必需 - getWorldCupRewardDto参数

### tournament.joinRegionalCup

- **描述**: 参加区域杯赛 基于old项目的joinRegionalCup接口
- **微服务**: match
- **模块**: tournament
- **方法**: joinRegionalCup

**参数**:

- `joinRegionalCupDto` (object) 必需 - joinRegionalCupDto参数

### tournament.regionalCupBattle

- **描述**: 区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能
- **微服务**: match
- **模块**: tournament
- **方法**: regionalCupBattle

**参数**:

- `regionalCupBattleDto` (object) 必需 - regionalCupBattleDto参数

### tournament.getStatistics

- **描述**: 获取锦标赛统计信息（管理接口）
- **微服务**: match
- **模块**: tournament
- **方法**: getStatistics

### tournament.resetDailyData

- **描述**: 重置每日锦标赛数据（管理接口）
- **微服务**: match
- **模块**: tournament
- **方法**: resetDailyData

**参数**:

- `characterId` (string) 可选 - characterId参数

### trophy.getTrophyCopyData

- **描述**: 获取杯赛副本数据 基于old项目的getTrophyCopyData接口
- **微服务**: match
- **模块**: trophy
- **方法**: getTrophyCopyData

**参数**:

- `getTrophyCopyDataDto` (object) 必需 - getTrophyCopyDataDto参数

### trophy.pveTrophyBattle

- **描述**: PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口
- **微服务**: match
- **模块**: trophy
- **方法**: pveTrophyBattle

**参数**:

- `pveTrophyBattleDto` (object) 必需 - pveTrophyBattleDto参数

### trophy.buyTrophyTimes

- **描述**: 购买杯赛次数 基于old项目的buyTrophyTimes接口
- **微服务**: match
- **模块**: trophy
- **方法**: buyTrophyTimes

**参数**:

- `buyTrophyTimesDto` (object) 必需 - buyTrophyTimesDto参数

### trophy.getStatistics

- **描述**: 获取杯赛统计信息（管理接口）
- **微服务**: match
- **模块**: trophy
- **方法**: getStatistics

### trophy.resetDailyTimes

- **描述**: 重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式
- **微服务**: match
- **模块**: trophy
- **方法**: resetDailyTimes

**参数**:

- `characterId` (string) 可选 - characterId参数

### trophy.getBattleHistory

- **描述**: 获取杯赛战斗历史（管理接口）
- **微服务**: match
- **模块**: trophy
- **方法**: getBattleHistory

**参数**:

- `characterId` (string) 必需 - characterId参数
- `limit` (number) 可选 - limit参数

## SOCIAL 分类

### chat.sendMessage

- **描述**: 发送聊天消息
- **微服务**: social
- **模块**: chat
- **方法**: sendMessage

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `messageData` (object) 必需 - messageData参数

### chat.getHistory

- **描述**: 获取聊天历史
- **微服务**: social
- **模块**: chat
- **方法**: getChatHistory

**参数**:

- `query` (object) 必需 - query参数

### chat.getPrivateHistory

- **描述**: 获取私聊历史
- **微服务**: social
- **模块**: chat
- **方法**: getPrivateHistory

**参数**:

- `query` (object) 必需 - query参数

### chat.joinChannel

- **描述**: 加入聊天频道
- **微服务**: social
- **模块**: chat
- **方法**: joinChannel

**参数**:

- `characterId` (string) 必需 - characterId参数
- `channelId` (string) 必需 - channelId参数
- `serverId` (string) 必需 - serverId参数

### chat.leaveChannel

- **描述**: 离开聊天频道
- **微服务**: social
- **模块**: chat
- **方法**: leaveChannel

**参数**:

- `characterId` (string) 必需 - characterId参数
- `channelId` (string) 必需 - channelId参数

### friend.getList

- **描述**: 获取好友列表
- **微服务**: social
- **模块**: friend
- **方法**: getFriendList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### friend.add

- **描述**: 添加好友申请
- **微服务**: social
- **模块**: friend
- **方法**: addFriend

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `addDto` (object) 必需 - addDto参数

### friend.handleApply

- **描述**: 处理好友申请
- **微服务**: social
- **模块**: friend
- **方法**: handleFriendApply

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `handleDto` (object) 必需 - handleDto参数

### friend.remove

- **描述**: 删除好友
- **微服务**: social
- **模块**: friend
- **方法**: removeFriend

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `removeDto` (object) 必需 - removeDto参数

### friend.block

- **描述**: 屏蔽玩家
- **微服务**: social
- **模块**: friend
- **方法**: blockCharacter

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `blockDto` (object) 必需 - blockDto参数

### friend.getApplies

- **描述**: 获取好友申请列表
- **微服务**: social
- **模块**: friend
- **方法**: getFriendApplies

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### friend.search

- **描述**: 搜索好友
- **微服务**: social
- **模块**: friend
- **方法**: searchFriends

**参数**:

- `characterId` (string) 必需 - characterId参数
- `searchDto` (object) 必需 - searchDto参数

### friend.findNearby

- **描述**: 查找附近玩家
- **微服务**: social
- **模块**: friend
- **方法**: findNearbyCharacters

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `nearbyDto` (object) 必需 - nearbyDto参数

### friend.updateLocation

- **描述**: 更新玩家位置
- **微服务**: social
- **模块**: friend
- **方法**: updateCharacterLocation

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `locationDto` (object) 必需 - locationDto参数

### friend.recommend

- **描述**: 推荐好友
- **微服务**: social
- **模块**: friend
- **方法**: recommendFriends

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `limit` (number) 可选 - limit参数

### friend.distances

- **描述**: 获取好友距离信息
- **微服务**: social
- **模块**: friend
- **方法**: getFriendDistances

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数
- `longitude` (number) 必需 - longitude参数
- `latitude` (number) 必需 - latitude参数

### friend.blacklist

- **描述**: 获取黑名单
- **微服务**: social
- **模块**: friend
- **方法**: getBlackList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### friend.getStats

- **描述**: 获取好友统计
- **微服务**: social
- **模块**: friend
- **方法**: getFriendStats

**参数**:

- `characterId` (string) 必需 - characterId参数
- `serverId` (string) 必需 - serverId参数

### friend.updateOnlineStatus

- **描述**: 更新玩家在线状态
- **微服务**: social
- **模块**: friend
- **方法**: updateCharacterOnlineStatus

**参数**:

- `characterId` (string) 必需 - characterId参数
- `isOnline` (boolean) 必需 - isOnline参数

### friend.cleanExpiredApplies

- **描述**: 清理过期申请
- **微服务**: social
- **模块**: friend
- **方法**: cleanExpiredApplies

**参数**:

- `expireDays` (number) 可选 - expireDays参数

### guild.create

- **描述**: 创建公会
- **微服务**: social
- **模块**: guild
- **方法**: createGuild

**参数**:

- `characterId` (string) 必需 - characterId参数
- `characterName` (string) 必需 - characterName参数
- `guildName` (string) 必需 - guildName参数
- `faceId` (number) 必需 - faceId参数
- `strength` (number) 必需 - strength参数
- `gid` (string) 必需 - gid参数
- `faceUrl` (string) 必需 - faceUrl参数
- `frontendId` (string) 可选 - frontendId参数
- `sessionId` (string) 可选 - sessionId参数

### guild.apply

- **描述**: 申请加入公会
- **微服务**: social
- **模块**: guild
- **方法**: applyJoinGuild

**参数**:

- `guildId` (string) 必需 - guildId参数
- `characterId` (string) 必需 - characterId参数
- `characterName` (string) 必需 - characterName参数
- `gid` (string) 必需 - gid参数
- `faceUrl` (string) 必需 - faceUrl参数
- `strength` (number) 必需 - strength参数
- `frontendId` (string) 可选 - frontendId参数
- `sessionId` (string) 可选 - sessionId参数

### guild.leave

- **描述**: 离开公会
- **微服务**: social
- **模块**: guild
- **方法**: leaveGuild

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数

### guild.getInfo

- **描述**: 获取公会信息
- **微服务**: social
- **模块**: guild
- **方法**: getGuildInfo

**参数**:

- `guildId` (string) 必需 - guildId参数

### guild.getApplicationList

- **描述**: 获取申请列表
- **微服务**: social
- **模块**: guild
- **方法**: getApplicationList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数

### guild.approveApplication

- **描述**: 同意加入申请
- **微服务**: social
- **模块**: guild
- **方法**: approveApplication

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数
- `agreeId` (string) 必需 - agreeId参数

### guild.rejectApplication

- **描述**: 拒绝加入申请
- **微服务**: social
- **模块**: guild
- **方法**: rejectApplication

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数
- `rejectId` (string) 必需 - rejectId参数

### guild.changePosition

- **描述**: 职位变更
- **微服务**: social
- **模块**: guild
- **方法**: changePosition

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数
- `targetCharacterId` (string) 必需 - targetCharacterId参数
- `newPosition` (number) 必需 - newPosition参数

### guild.transferPresidency

- **描述**: 转让会长
- **微服务**: social
- **模块**: guild
- **方法**: transferPresidency

**参数**:

- `characterId` (string) 必需 - characterId参数
- `guildId` (string) 必需 - guildId参数
- `newPresidentId` (string) 必需 - newPresidentId参数

### guild.search

- **描述**: 搜索公会
- **微服务**: social
- **模块**: guild
- **方法**: searchGuilds

**参数**:

- `keyword` (string) 必需 - keyword参数
- `page` (number) 可选 - page参数
- `limit` (number) 可选 - limit参数

### guild.getRanking

- **描述**: 获取公会排行榜
- **微服务**: social
- **模块**: guild
- **方法**: getGuildRanking

**参数**:

- `page` (number) 可选 - page参数
- `limit` (number) 可选 - limit参数

### mail.send

- **描述**: 发送邮件
- **微服务**: social
- **模块**: mail
- **方法**: sendMail

**参数**:

- `receiverUid` (string) 必需 - receiverUid参数
- `senderUid` (string) 必需 - senderUid参数
- `mailId` (number) 必需 - mailId参数
- `mailType` (number) 必需 - mailType参数
- `attachList` (array) 必需 - attachList参数
- `specialAttachInfo` (any) 可选 - specialAttachInfo参数
- `param1` (any) 可选 - param1参数
- `param2` (any) 可选 - param2参数
- `param3` (any) 可选 - param3参数
- `param4` (any) 可选 - param4参数

### mail.getList

- **描述**: 获取邮件列表
- **微服务**: social
- **模块**: mail
- **方法**: getMailList

**参数**:

- `characterId` (string) 必需 - characterId参数
- `page` (number) 可选 - page参数
- `limit` (number) 可选 - limit参数

### mail.read

- **描述**: 读取邮件
- **微服务**: social
- **模块**: mail
- **方法**: readMail

**参数**:

- `characterId` (string) 必需 - characterId参数
- `mailUid` (string) 必需 - mailUid参数

### mail.delete

- **描述**: 删除邮件
- **微服务**: social
- **模块**: mail
- **方法**: deleteMail

**参数**:

- `characterId` (string) 必需 - characterId参数
- `mailUid` (string) 必需 - mailUid参数

### mail.claimAttachment

- **描述**: 领取邮件附件
- **微服务**: social
- **模块**: mail
- **方法**: claimMailAttachment

**参数**:

- `characterId` (string) 必需 - characterId参数
- `mailUid` (string) 必需 - mailUid参数

### mail.batchDelete

- **描述**: 批量删除邮件
- **微服务**: social
- **模块**: mail
- **方法**: batchDeleteMails

**参数**:

- `characterId` (string) 必需 - characterId参数
- `mailUids` (array) 必需 - mailUids参数

### mail.batchClaim

- **描述**: 批量领取附件
- **微服务**: social
- **模块**: mail
- **方法**: batchClaimAttachments

**参数**:

- `characterId` (string) 必需 - characterId参数
- `mailUids` (array) 必需 - mailUids参数

