const { Project } = require('ts-morph');
const glob = require('glob');
const path = require('path');
const fs = require('fs');

// 创建TypeScript项目
const project = new Project({
  compilerOptions: {
    target: 'ES2020',
    module: 'CommonJS',
    strict: false,
    skipLibCheck: true,
    allowJs: true,
  },
});

async function debugDTOSearch() {
  console.log('🔍 调试DTO搜索...');

  // 1. 测试glob搜索
  const searchPaths = [
    `../apps/*/src/**/*.dto.ts`,
    `../apps/*/src/common/dto/*.ts`,
    `../apps/*/src/modules/*/dto/*.ts`,
    `../libs/*/src/**/*.dto.ts`,
    `../libs/game-types/src/**/*.ts`,
    `../libs/*/src/dtos/*.ts`
  ];

  console.log('📂 搜索路径:');
  for (const pattern of searchPaths) {
    console.log(`  - ${pattern}`);
    const files = glob.sync(pattern);
    console.log(`    找到 ${files.length} 个文件`);

    // 检查是否包含character.dto.ts
    const characterDtoFiles = files.filter(f => f.includes('character.dto.ts'));
    if (characterDtoFiles.length > 0) {
      console.log(`    ✅ 找到character.dto.ts文件:`, characterDtoFiles);

      // 测试AST解析
      for (const filePath of characterDtoFiles) {
        console.log(`\n🔍 解析文件: ${filePath}`);
        try {
          const sourceFile = project.addSourceFileAtPath(filePath);

          // 查找UpdateCharacterDto
          const updateCharacterDto = sourceFile.getClass('UpdateCharacterDto');
          if (updateCharacterDto) {
            console.log('✅ 找到UpdateCharacterDto类');

            // 获取属性
            const properties = updateCharacterDto.getProperties();
            console.log(`📋 属性数量: ${properties.length}`);

            for (const prop of properties) {
              const propName = prop.getName();
              const isOptional = prop.hasQuestionToken();

              // 获取类型信息
              let propTypeText = '';
              const typeNode = prop.getTypeNode();
              if (typeNode) {
                propTypeText = typeNode.getText();
              } else {
                const propType = prop.getType();
                propTypeText = propType.getText();
              }

              console.log(`  - ${propName}${isOptional ? '?' : ''}: ${propTypeText}`);
            }
          } else {
            console.log('❌ 未找到UpdateCharacterDto类');

            // 列出所有类
            const classes = sourceFile.getClasses();
            console.log('📋 文件中的所有类:');
            for (const cls of classes) {
              console.log(`  - ${cls.getName()}`);
            }
          }
        } catch (error) {
          console.error(`❌ 解析文件失败: ${error.message}`);
        }
      }
    }
  }
}

debugDTOSearch().catch(console.error);
