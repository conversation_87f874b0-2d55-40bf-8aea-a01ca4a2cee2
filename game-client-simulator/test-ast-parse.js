const { Project } = require('ts-morph');
const path = require('path');

async function testASTParseDirectly() {
  console.log('🔍 直接测试AST解析...');

  // 创建TypeScript项目
  const project = new Project({
    compilerOptions: {
      target: 'ES2020',
      module: 'CommonJS',
      strict: false,
      skipLibCheck: true,
      allowJs: true,
    },
  });

  // 直接加载character.dto.ts文件
  const filePath = '../apps/character/src/common/dto/character.dto.ts';
  console.log(`📄 加载文件: ${filePath}`);

  try {
    const sourceFile = project.addSourceFileAtPath(filePath);
    console.log('✅ 文件加载成功');

    // 查找UpdateCharacterDto类
    const updateCharacterDto = sourceFile.getClass('UpdateCharacterDto');
    if (updateCharacterDto) {
      console.log('✅ 找到UpdateCharacterDto类');

      // 获取属性
      const properties = updateCharacterDto.getProperties();
      console.log(`📋 属性数量: ${properties.length}`);

      const params = {};
      for (const prop of properties) {
        const propName = prop.getName();
        const isOptional = prop.hasQuestionToken();

        // 获取类型信息
        let propTypeText = '';
        const typeNode = prop.getTypeNode();
        if (typeNode) {
          propTypeText = typeNode.getText();
        } else {
          const propType = prop.getType();
          propTypeText = propType.getText();
        }

        // 获取JSDoc注释
        const jsDoc = prop.getJsDocs()[0];
        const propDescription = jsDoc ? jsDoc.getDescription().trim() : `${propName}参数`;

        console.log(`  - ${propName}${isOptional ? '?' : ''}: ${propTypeText} (${propDescription})`);

        params[propName] = {
          type: mapTypeScriptTypeToJS(propTypeText),
          required: !isOptional,
          description: propDescription
        };
      }

      console.log('\n📋 最终解析结果:');
      console.log(JSON.stringify(params, null, 2));

    } else {
      console.log('❌ 未找到UpdateCharacterDto类');

      // 列出所有类
      const classes = sourceFile.getClasses();
      console.log('📋 文件中的所有类:');
      for (const cls of classes) {
        console.log(`  - ${cls.getName()}`);
      }
    }
  } catch (error) {
    console.error(`❌ 解析文件失败: ${error.message}`);
    console.error(error.stack);
  }
}

// 简化的类型映射函数
function mapTypeScriptTypeToJS(tsType) {
  const cleanType = tsType.toLowerCase().trim();

  if (cleanType.includes('string')) return 'string';
  if (cleanType.includes('number')) return 'number';
  if (cleanType.includes('boolean')) return 'boolean';
  if (cleanType.includes('[]') || cleanType.includes('array')) return 'array';
  if (cleanType === 'any') return 'any';

  return 'object';
}

testASTParseDirectly().catch(console.error);
