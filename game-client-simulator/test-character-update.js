const ASTAPIScanner = require('./src/utils/ast-api-scanner');

async function testCharacterUpdate() {
  console.log('🔍 测试character.update方法的DTO解析...');

  const scanner = new ASTAPIScanner({
    projectRoot: '..',
    outputDir: './src/actions'
  });

  // 直接测试UpdateCharacterDto的解析
  console.log('\n📋 测试UpdateCharacterDto解析:');
  const updateDtoResult = await scanner.searchDTOGlobally('UpdateCharacterDto');
  console.log('解析结果:', JSON.stringify(updateDtoResult, null, 2));

  // 测试内联类型解析
  console.log('\n📋 测试内联类型解析:');
  const inlineType = '{ characterId: string; updateDto: UpdateCharacterDto; serverId?: string; injectedContext?: InjectedContext }';
  const inlineResult = await scanner.parseInlineType(inlineType);
  console.log('内联解析结果:', JSON.stringify(inlineResult, null, 2));

  // 测试过滤功能
  console.log('\n🚫 测试参数过滤:');
  const testParams = {
    characterId: { type: 'string', required: true },
    updateDto: { type: 'object', required: true },
    serverId: { type: 'string', required: false },
    injectedContext: { type: 'object', required: false }
  };
  const filteredParams = scanner.filterInjectedParams(testParams);
  console.log('过滤后参数:', JSON.stringify(filteredParams, null, 2));
}

testCharacterUpdate().catch(console.error);
