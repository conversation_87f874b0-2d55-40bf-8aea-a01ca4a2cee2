/**
 * 修改密码 - 微服务调用
 * 
 * 微服务: auth
 * 模块: user
 * Controller: users
 * Pattern: user.changePassword
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.208Z
 */

const BaseAction = require('../../core/base-action');

class UserchangePasswordAction extends BaseAction {
  static metadata = {
    name: '修改密码 - 微服务调用',
    description: '修改密码 - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'user',
    actionName: 'user.changePassword',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      },
      "oldPassword": {
            "type": "string",
            "required": true,
            "description": "oldPassword参数"
      },
      "newPassword": {
            "type": "string",
            "required": true,
            "description": "newPassword参数"
      },
      "confirmNewPassword": {
            "type": "string",
            "required": true,
            "description": "confirmNewPassword参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId, oldPassword, newPassword, confirmNewPassword } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId,
      oldPassword,
      newPassword,
      confirmNewPassword
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '修改密码 - 微服务调用成功'
      };
    } else {
      throw new Error(`修改密码 - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = UserchangePasswordAction;