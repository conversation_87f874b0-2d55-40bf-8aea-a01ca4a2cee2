/**
 * 验证API Key - 微服务调用
 * 
 * 微服务: auth
 * 模块: api-key
 * Controller: api-key
 * Pattern: validateApiKey
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.188Z
 */

const BaseAction = require('../../core/base-action');

class ValidateApiKeyAction extends BaseAction {
  static metadata = {
    name: '验证API Key - 微服务调用',
    description: '验证API Key - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'api-key',
    actionName: 'validateApiKey',
    prerequisites: ["login"],
    params: {
      "apiKey": {
            "type": "string",
            "required": true,
            "description": "apiKey参数"
      },
      "clientIp": {
            "type": "string",
            "required": false,
            "description": "clientIp参数"
      },
      "userAgent": {
            "type": "string",
            "required": false,
            "description": "userAgent参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { apiKey, clientIp, userAgent } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      apiKey,
      clientIp,
      userAgent
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '验证API Key - 微服务调用成功'
      };
    } else {
      throw new Error(`验证API Key - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = ValidateApiKeyAction;