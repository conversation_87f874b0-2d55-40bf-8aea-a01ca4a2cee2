/**
 * 获取用户资料 - 微服务调用
 * 
 * 微服务: auth
 * 模块: user
 * Controller: users
 * Pattern: user.getUserProfile
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.209Z
 */

const BaseAction = require('../../core/base-action');

class UsergetUserProfileAction extends BaseAction {
  static metadata = {
    name: '获取用户资料 - 微服务调用',
    description: '获取用户资料 - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'user',
    actionName: 'user.getUserProfile',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取用户资料 - 微服务调用成功'
      };
    } else {
      throw new Error(`获取用户资料 - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = UsergetUserProfileAction;