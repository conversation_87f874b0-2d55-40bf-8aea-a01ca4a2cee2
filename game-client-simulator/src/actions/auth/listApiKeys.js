/**
 * 列出用户的API Keys - 微服务调用
 * 
 * 微服务: auth
 * 模块: api-key
 * Controller: api-key
 * Pattern: listApiKeys
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.191Z
 */

const BaseAction = require('../../core/base-action');

class ListApiKeysAction extends BaseAction {
  static metadata = {
    name: '列出用户的API Keys - 微服务调用',
    description: '列出用户的API Keys - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'api-key',
    actionName: 'listApiKeys',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '列出用户的API Keys - 微服务调用成功'
      };
    } else {
      throw new Error(`列出用户的API Keys - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = ListApiKeysAction;