/**
 * 手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作
 * 
 * 微服务: auth
 * 模块: health
 * Controller: health
 * Pattern: manual-cache-test
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.203Z
 */

const BaseAction = require('../../core/base-action');

class ManualCacheTestAction extends BaseAction {
  static metadata = {
    name: '手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作',
    description: '手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作',
    category: 'auth',
    serviceName: 'auth',
    module: 'health',
    actionName: 'manual-cache-test',
    prerequisites: ["login"],
    params: {
      "testKey": {
            "type": "string",
            "required": true,
            "description": "testKey参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { testKey } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      testKey
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作成功'
      };
    } else {
      throw new Error(`手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存） 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作失败: ${response.message}`);
    }
  }
}

module.exports = ManualCacheTestAction;