/**
 * 生成API Key - 微服务调用
 * 
 * 微服务: auth
 * 模块: api-key
 * Controller: api-key
 * Pattern: generateApiKey
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.189Z
 */

const BaseAction = require('../../core/base-action');

class GenerateApiKeyAction extends BaseAction {
  static metadata = {
    name: '生成API Key - 微服务调用',
    description: '生成API Key - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'api-key',
    actionName: 'generateApiKey',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      },
      "name": {
            "type": "string",
            "required": true,
            "description": "name参数"
      },
      "permissions": {
            "type": "array",
            "required": false,
            "description": "permissions参数"
      },
      "expiresAt": {
            "type": "object",
            "required": false,
            "description": "expiresAt参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "Date类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      },
      "ipWhitelist": {
            "type": "array",
            "required": false,
            "description": "ipWhitelist参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId, name, permissions, expiresAt, ipWhitelist } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId,
      name,
      permissions,
      expiresAt,
      ipWhitelist
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '生成API Key - 微服务调用成功'
      };
    } else {
      throw new Error(`生成API Key - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = GenerateApiKeyAction;