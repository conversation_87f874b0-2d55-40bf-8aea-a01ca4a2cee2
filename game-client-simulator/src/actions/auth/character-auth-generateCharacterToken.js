/**
 * 生成角色Token - 微服务调用
 * 
 * 微服务: auth
 * 模块: auth
 * Controller: character-auth
 * Pattern: character-auth.generateCharacterToken
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.193Z
 */

const BaseAction = require('../../core/base-action');

class CharacterAuthgenerateCharacterTokenAction extends BaseAction {
  static metadata = {
    name: '生成角色Token - 微服务调用',
    description: '生成角色Token - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'auth',
    actionName: 'character-auth.generateCharacterToken',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      },
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "characterName": {
            "type": "string",
            "required": false,
            "description": "characterName参数"
      },
      "sessionData": {
            "type": "any",
            "required": false,
            "description": "sessionData参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId, characterId, serverId, characterName, sessionData } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId,
      characterId,
      serverId,
      characterName,
      sessionData
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '生成角色Token - 微服务调用成功'
      };
    } else {
      throw new Error(`生成角色Token - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = CharacterAuthgenerateCharacterTokenAction;