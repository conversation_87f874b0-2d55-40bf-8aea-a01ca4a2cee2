/**
 * 验证角色Token - 微服务调用
 * 
 * 微服务: auth
 * 模块: auth
 * Controller: character-auth
 * Pattern: character-auth.verifyCharacterToken
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.198Z
 */

const BaseAction = require('../../core/base-action');

class CharacterAuthverifyCharacterTokenAction extends BaseAction {
  static metadata = {
    name: '验证角色Token - 微服务调用',
    description: '验证角色Token - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'auth',
    actionName: 'character-auth.verifyCharacterToken',
    prerequisites: ["login"],
    params: {
      "characterToken": {
            "type": "string",
            "required": true,
            "description": "characterToken参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterToken } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterToken
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '验证角色Token - 微服务调用成功'
      };
    } else {
      throw new Error(`验证角色Token - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = CharacterAuthverifyCharacterTokenAction;