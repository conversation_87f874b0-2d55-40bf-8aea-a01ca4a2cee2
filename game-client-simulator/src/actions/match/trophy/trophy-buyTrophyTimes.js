/**
 * 购买杯赛次数 基于old项目的buyTrophyTimes接口
 * 
 * 微服务: match
 * 模块: trophy
 * Controller: trophy
 * Pattern: trophy.buyTrophyTimes
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.614Z
 */

const BaseAction = require('../../../core/base-action');

class TrophybuyTrophyTimesAction extends BaseAction {
  static metadata = {
    name: '购买杯赛次数 基于old项目的buyTrophyTimes接口',
    description: '购买杯赛次数 基于old项目的buyTrophyTimes接口',
    category: 'match',
    serviceName: 'match',
    module: 'trophy',
    actionName: 'trophy.buyTrophyTimes',
    prerequisites: ["login","character"],
    params: {
      "buyTrophyTimesDto": {
            "type": "object",
            "required": true,
            "description": "buyTrophyTimesDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "trophyId": {
                        "type": "number",
                        "required": true,
                        "description": "trophyId参数"
                  },
                  "teamId": {
                        "type": "number",
                        "required": false,
                        "description": "teamId参数"
                  },
                  "num": {
                        "type": "number",
                        "required": true,
                        "description": "num参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "trophyId": 1,
                  "teamId": 1,
                  "num": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { buyTrophyTimesDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      buyTrophyTimesDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买杯赛次数 基于old项目的buyTrophyTimes接口成功'
      };
    } else {
      throw new Error(`购买杯赛次数 基于old项目的buyTrophyTimes接口失败: ${response.message}`);
    }
  }
}

module.exports = TrophybuyTrophyTimesAction;