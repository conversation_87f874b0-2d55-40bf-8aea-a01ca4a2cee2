/**
 * 获取杯赛战斗历史（管理接口）
 * 
 * 微服务: match
 * 模块: trophy
 * Controller: trophy
 * Pattern: trophy.getBattleHistory
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.617Z
 */

const BaseAction = require('../../../core/base-action');

class TrophygetBattleHistoryAction extends BaseAction {
  static metadata = {
    name: '获取杯赛战斗历史（管理接口）',
    description: '获取杯赛战斗历史（管理接口）',
    category: 'match',
    serviceName: 'match',
    module: 'trophy',
    actionName: 'trophy.getBattleHistory',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取杯赛战斗历史（管理接口）成功'
      };
    } else {
      throw new Error(`获取杯赛战斗历史（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = TrophygetBattleHistoryAction;