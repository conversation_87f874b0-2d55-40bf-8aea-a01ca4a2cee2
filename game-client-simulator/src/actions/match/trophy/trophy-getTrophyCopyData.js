/**
 * 获取杯赛副本数据 基于old项目的getTrophyCopyData接口
 * 
 * 微服务: match
 * 模块: trophy
 * Controller: trophy
 * Pattern: trophy.getTrophyCopyData
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.610Z
 */

const BaseAction = require('../../../core/base-action');

class TrophygetTrophyCopyDataAction extends BaseAction {
  static metadata = {
    name: '获取杯赛副本数据 基于old项目的getTrophyCopyData接口',
    description: '获取杯赛副本数据 基于old项目的getTrophyCopyData接口',
    category: 'match',
    serviceName: 'match',
    module: 'trophy',
    actionName: 'trophy.getTrophyCopyData',
    prerequisites: ["login","character"],
    params: {
      "getTrophyCopyDataDto": {
            "type": "object",
            "required": true,
            "description": "getTrophyCopyDataDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getTrophyCopyDataDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getTrophyCopyDataDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取杯赛副本数据 基于old项目的getTrophyCopyData接口成功'
      };
    } else {
      throw new Error(`获取杯赛副本数据 基于old项目的getTrophyCopyData接口失败: ${response.message}`);
    }
  }
}

module.exports = TrophygetTrophyCopyDataAction;