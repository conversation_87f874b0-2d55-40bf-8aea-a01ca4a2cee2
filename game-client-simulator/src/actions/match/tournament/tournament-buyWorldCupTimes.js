/**
 * 购买世界杯次数 基于old项目的buyWorldCupTimes接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.buyWorldCupTimes
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.599Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentbuyWorldCupTimesAction extends BaseAction {
  static metadata = {
    name: '购买世界杯次数 基于old项目的buyWorldCupTimes接口',
    description: '购买世界杯次数 基于old项目的buyWorldCupTimes接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.buyWorldCupTimes',
    prerequisites: ["login","character"],
    params: {
      "buyWorldCupTimesDto": {
            "type": "object",
            "required": true,
            "description": "buyWorldCupTimesDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { buyWorldCupTimesDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      buyWorldCupTimesDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买世界杯次数 基于old项目的buyWorldCupTimes接口成功'
      };
    } else {
      throw new Error(`购买世界杯次数 基于old项目的buyWorldCupTimes接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentbuyWorldCupTimesAction;