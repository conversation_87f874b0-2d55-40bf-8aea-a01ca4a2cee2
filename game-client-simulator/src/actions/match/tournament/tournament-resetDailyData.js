/**
 * 重置每日锦标赛数据（管理接口）
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.resetDailyData
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.608Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentresetDailyDataAction extends BaseAction {
  static metadata = {
    name: '重置每日锦标赛数据（管理接口）',
    description: '重置每日锦标赛数据（管理接口）',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.resetDailyData',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": false,
            "description": "characterId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '重置每日锦标赛数据（管理接口）成功'
      };
    } else {
      throw new Error(`重置每日锦标赛数据（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = TournamentresetDailyDataAction;