/**
 * 世界杯战斗 基于old项目的worldCupBattle接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.worldCupBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.598Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentworldCupBattleAction extends BaseAction {
  static metadata = {
    name: '世界杯战斗 基于old项目的worldCupBattle接口',
    description: '世界杯战斗 基于old项目的worldCupBattle接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.worldCupBattle',
    prerequisites: ["login","character"],
    params: {
      "worldCupBattleDto": {
            "type": "object",
            "required": true,
            "description": "worldCupBattleDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { worldCupBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      worldCupBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '世界杯战斗 基于old项目的worldCupBattle接口成功'
      };
    } else {
      throw new Error(`世界杯战斗 基于old项目的worldCupBattle接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentworldCupBattleAction;