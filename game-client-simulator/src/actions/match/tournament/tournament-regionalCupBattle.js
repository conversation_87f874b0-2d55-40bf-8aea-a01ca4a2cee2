/**
 * 区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.regionalCupBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.605Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentregionalCupBattleAction extends BaseAction {
  static metadata = {
    name: '区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能',
    description: '区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.regionalCupBattle',
    prerequisites: ["login","character"],
    params: {
      "regionalCupBattleDto": {
            "type": "object",
            "required": true,
            "description": "regionalCupBattleDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "cupType": {
                        "type": "string",
                        "required": true,
                        "description": "cupType参数"
                  },
                  "enemyTeamId": {
                        "type": "number",
                        "required": true,
                        "description": "enemyTeamId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "cupType": "示例cupType",
                  "enemyTeamId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { regionalCupBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      regionalCupBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能成功'
      };
    } else {
      throw new Error(`区域杯赛战斗 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口 修复：实现缺失的区域杯赛战斗功能失败: ${response.message}`);
    }
  }
}

module.exports = TournamentregionalCupBattleAction;