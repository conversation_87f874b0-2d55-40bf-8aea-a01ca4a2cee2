/**
 * 删除战斗房间 基于old项目的deleteBattleRoom接口
 * 
 * 微服务: match
 * 模块: battle
 * Controller: battle
 * Pattern: battle.deleteBattleRoom
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.545Z
 */

const BaseAction = require('../../../core/base-action');

class BattledeleteBattleRoomAction extends BaseAction {
  static metadata = {
    name: '删除战斗房间 基于old项目的deleteBattleRoom接口',
    description: '删除战斗房间 基于old项目的deleteBattleRoom接口',
    category: 'match',
    serviceName: 'match',
    module: 'battle',
    actionName: 'battle.deleteBattleRoom',
    prerequisites: ["login","character"],
    params: {
      "roomUid": {
            "type": "string",
            "required": true,
            "description": "roomUid参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { roomUid } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      roomUid
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '删除战斗房间 基于old项目的deleteBattleRoom接口成功'
      };
    } else {
      throw new Error(`删除战斗房间 基于old项目的deleteBattleRoom接口失败: ${response.message}`);
    }
  }
}

module.exports = BattledeleteBattleRoomAction;