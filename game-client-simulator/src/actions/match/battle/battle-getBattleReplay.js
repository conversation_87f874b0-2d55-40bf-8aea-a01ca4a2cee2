/**
 * 获取战斗回放 基于old项目的战斗回放功能
 * 
 * 微服务: match
 * 模块: battle
 * Controller: battle
 * Pattern: battle.getBattleReplay
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.543Z
 */

const BaseAction = require('../../../core/base-action');

class BattlegetBattleReplayAction extends BaseAction {
  static metadata = {
    name: '获取战斗回放 基于old项目的战斗回放功能',
    description: '获取战斗回放 基于old项目的战斗回放功能',
    category: 'match',
    serviceName: 'match',
    module: 'battle',
    actionName: 'battle.getBattleReplay',
    prerequisites: ["login","character"],
    params: {
      "getBattleReplayDto": {
            "type": "object",
            "required": true,
            "description": "getBattleReplayDto参数",
            "properties": {
                  "roomUid": {
                        "type": "string",
                        "required": true,
                        "description": "roomUid参数"
                  },
                  "characterId": {
                        "type": "string",
                        "required": false,
                        "description": "characterId参数"
                  }
            },
            "example": {
                  "roomUid": "示例roomUid",
                  "characterId": "示例characterId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getBattleReplayDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getBattleReplayDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取战斗回放 基于old项目的战斗回放功能成功'
      };
    } else {
      throw new Error(`获取战斗回放 基于old项目的战斗回放功能失败: ${response.message}`);
    }
  }
}

module.exports = BattlegetBattleReplayAction;