/**
 * PVE战斗 基于old项目的initPveBattle接口
 * 
 * 微服务: match
 * 模块: battle
 * Controller: battle
 * Pattern: battle.pveBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.541Z
 */

const BaseAction = require('../../../core/base-action');

class BattlepveBattleAction extends BaseAction {
  static metadata = {
    name: 'PVE战斗 基于old项目的initPveBattle接口',
    description: 'PVE战斗 基于old项目的initPveBattle接口',
    category: 'match',
    serviceName: 'match',
    module: 'battle',
    actionName: 'battle.pveBattle',
    prerequisites: ["login","character"],
    params: {
      "pveBattleDto": {
            "type": "object",
            "required": true,
            "description": "pveBattleDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "characterBattleData": {
                        "type": "object",
                        "required": true,
                        "description": "characterBattleData参数"
                  },
                  "enemyBattleData": {
                        "type": "object",
                        "required": true,
                        "description": "enemyBattleData参数"
                  },
                  "battleType": {
                        "type": "string",
                        "required": true,
                        "description": "battleType参数"
                  },
                  "leagueId": {
                        "type": "number",
                        "required": false,
                        "description": "leagueId参数"
                  },
                  "teamCopyId": {
                        "type": "number",
                        "required": false,
                        "description": "teamCopyId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "characterBattleData": {},
                  "enemyBattleData": {},
                  "battleType": "示例battleType",
                  "leagueId": 1,
                  "teamCopyId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { pveBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      pveBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: 'PVE战斗 基于old项目的initPveBattle接口成功'
      };
    } else {
      throw new Error(`PVE战斗 基于old项目的initPveBattle接口失败: ${response.message}`);
    }
  }
}

module.exports = BattlepveBattleAction;