/**
 * PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口
 * 
 * 微服务: match
 * 模块: league
 * Controller: league
 * Pattern: league.pveBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.572Z
 */

const BaseAction = require('../../../core/base-action');

class LeaguepveBattleAction extends BaseAction {
  static metadata = {
    name: 'PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口',
    description: 'PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口',
    category: 'match',
    serviceName: 'match',
    module: 'league',
    actionName: 'league.pveBattle',
    prerequisites: ["login","character"],
    params: {
      "pveLeagueBattleDto": {
            "type": "object",
            "required": true,
            "description": "pveLeagueBattleDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "leagueId": {
                        "type": "number",
                        "required": true,
                        "description": "leagueId参数"
                  },
                  "teamCopyId": {
                        "type": "number",
                        "required": true,
                        "description": "teamCopyId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "leagueId": 1,
                  "teamCopyId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { pveLeagueBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      pveLeagueBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: 'PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口成功'
      };
    } else {
      throw new Error(`PVE联赛战斗 基于old项目的PVELeagueCopyBattle接口失败: ${response.message}`);
    }
  }
}

module.exports = LeaguepveBattleAction;