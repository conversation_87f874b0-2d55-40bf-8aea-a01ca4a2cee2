/**
 * 获取联赛副本数据 基于old项目的getLeagueCopyData接口
 * 
 * 微服务: match
 * 模块: league
 * Controller: league
 * Pattern: league.getLeagueCopyData
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.652Z
 */

const BaseAction = require('../../../core/base-action');

class LeaguegetLeagueCopyDataAction extends BaseAction {
  static metadata = {
    name: '获取联赛副本数据 基于old项目的getLeagueCopyData接口',
    description: '获取联赛副本数据 基于old项目的getLeagueCopyData接口',
    category: 'match',
    serviceName: 'match',
    module: 'league',
    actionName: 'league.getLeagueCopyData',
    prerequisites: ["login","character"],
    params: {
      "getLeagueCopyDataDto": {
            "type": "object",
            "required": true,
            "description": "getLeagueCopyDataDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  },
                  "type": {
                        "type": "number",
                        "required": false,
                        "description": "type参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId",
                  "type": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getLeagueCopyDataDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getLeagueCopyDataDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取联赛副本数据 基于old项目的getLeagueCopyData接口成功'
      };
    } else {
      throw new Error(`获取联赛副本数据 基于old项目的getLeagueCopyData接口失败: ${response.message}`);
    }
  }
}

module.exports = LeaguegetLeagueCopyDataAction;