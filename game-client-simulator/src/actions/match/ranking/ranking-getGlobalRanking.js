/**
 * 获取全球排名 基于old项目的全球排名功能
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.getGlobalRanking
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.579Z
 */

const BaseAction = require('../../../core/base-action');

class RankinggetGlobalRankingAction extends BaseAction {
  static metadata = {
    name: '获取全球排名 基于old项目的全球排名功能',
    description: '获取全球排名 基于old项目的全球排名功能',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.getGlobalRanking',
    prerequisites: ["login","character"],
    params: {
      "getGlobalRankingDto": {
            "type": "object",
            "required": true,
            "description": "getGlobalRankingDto参数",
            "properties": {
                  "rankType": {
                        "type": "string",
                        "required": true,
                        "description": "rankType参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  },
                  "offset": {
                        "type": "number",
                        "required": false,
                        "description": "offset参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "rankType": "示例rankType",
                  "limit": 1,
                  "offset": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getGlobalRankingDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getGlobalRankingDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取全球排名 基于old项目的全球排名功能成功'
      };
    } else {
      throw new Error(`获取全球排名 基于old项目的全球排名功能失败: ${response.message}`);
    }
  }
}

module.exports = RankinggetGlobalRankingAction;