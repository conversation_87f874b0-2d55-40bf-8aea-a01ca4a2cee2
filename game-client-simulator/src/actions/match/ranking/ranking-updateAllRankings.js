/**
 * 批量更新所有排名（管理接口）
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.updateAllRankings
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.592Z
 */

const BaseAction = require('../../../core/base-action');

class RankingupdateAllRankingsAction extends BaseAction {
  static metadata = {
    name: '批量更新所有排名（管理接口）',
    description: '批量更新所有排名（管理接口）',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.updateAllRankings',
    prerequisites: ["login","character"],
    params: {
      "season": {
            "type": "number",
            "required": false,
            "description": "season参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { season } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      season
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量更新所有排名（管理接口）成功'
      };
    } else {
      throw new Error(`批量更新所有排名（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = RankingupdateAllRankingsAction;