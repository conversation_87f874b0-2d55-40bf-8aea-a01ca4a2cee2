/**
 * 更新排名数据（管理接口） 基于old项目的排名更新功能
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.updateRanking
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.584Z
 */

const BaseAction = require('../../../core/base-action');

class RankingupdateRankingAction extends BaseAction {
  static metadata = {
    name: '更新排名数据（管理接口） 基于old项目的排名更新功能',
    description: '更新排名数据（管理接口） 基于old项目的排名更新功能',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.updateRanking',
    prerequisites: ["login","character"],
    params: {
      "updateRankingDto": {
            "type": "object",
            "required": true,
            "description": "updateRankingDto参数",
            "properties": {
                  "rankType": {
                        "type": "string",
                        "required": true,
                        "description": "rankType参数"
                  },
                  "season": {
                        "type": "number",
                        "required": false,
                        "description": "season参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "rankType": "示例rankType",
                  "season": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { updateRankingDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      updateRankingDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新排名数据（管理接口） 基于old项目的排名更新功能成功'
      };
    } else {
      throw new Error(`更新排名数据（管理接口） 基于old项目的排名更新功能失败: ${response.message}`);
    }
  }
}

module.exports = RankingupdateRankingAction;