/**
 * 领取排名奖励 基于old项目的排名奖励发放功能
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.claimRankingReward
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.663Z
 */

const BaseAction = require('../../../core/base-action');

class RankingclaimRankingRewardAction extends BaseAction {
  static metadata = {
    name: '领取排名奖励 基于old项目的排名奖励发放功能',
    description: '领取排名奖励 基于old项目的排名奖励发放功能',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.claimRankingReward',
    prerequisites: ["login","character"],
    params: {
      "claimRankingRewardDto": {
            "type": "object",
            "required": true,
            "description": "claimRankingRewardDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "rankType": {
                        "type": "string",
                        "required": true,
                        "description": "rankType参数"
                  },
                  "season": {
                        "type": "number",
                        "required": true,
                        "description": "season参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "rankType": "示例rankType",
                  "season": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { claimRankingRewardDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      claimRankingRewardDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取排名奖励 基于old项目的排名奖励发放功能成功'
      };
    } else {
      throw new Error(`领取排名奖励 基于old项目的排名奖励发放功能失败: ${response.message}`);
    }
  }
}

module.exports = RankingclaimRankingRewardAction;