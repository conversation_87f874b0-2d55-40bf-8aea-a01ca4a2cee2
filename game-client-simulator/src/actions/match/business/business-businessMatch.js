/**
 * 商业赛匹配 基于old项目的businessMatch接口
 * 
 * 微服务: match
 * 模块: business
 * Controller: business
 * Pattern: business.businessMatch
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.559Z
 */

const BaseAction = require('../../../core/base-action');

class BusinessbusinessMatchAction extends BaseAction {
  static metadata = {
    name: '商业赛匹配 基于old项目的businessMatch接口',
    description: '商业赛匹配 基于old项目的businessMatch接口',
    category: 'match',
    serviceName: 'match',
    module: 'business',
    actionName: 'business.businessMatch',
    prerequisites: ["login","character"],
    params: {
      "businessMatchDto": {
            "type": "object",
            "required": true,
            "description": "businessMatchDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "enemyUid": {
                        "type": "string",
                        "required": true,
                        "description": "enemyUid参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "enemyUid": "示例enemyUid",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { businessMatchDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      businessMatchDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '商业赛匹配 基于old项目的businessMatch接口成功'
      };
    } else {
      throw new Error(`商业赛匹配 基于old项目的businessMatch接口失败: ${response.message}`);
    }
  }
}

module.exports = BusinessbusinessMatchAction;