/**
 * 获取商业赛统计信息（管理接口）
 * 
 * 微服务: match
 * 模块: business
 * Controller: business
 * Pattern: business.getStatistics
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.564Z
 */

const BaseAction = require('../../../core/base-action');

class BusinessgetStatisticsAction extends BaseAction {
  static metadata = {
    name: '获取商业赛统计信息（管理接口）',
    description: '获取商业赛统计信息（管理接口）',
    category: 'match',
    serviceName: 'match',
    module: 'business',
    actionName: 'business.getStatistics',
    prerequisites: ["login","character"],
    params: {},
    timeout: 10000
  };

  async perform(client, params) {
    // 无参数
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取商业赛统计信息（管理接口）成功'
      };
    } else {
      throw new Error(`获取商业赛统计信息（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = BusinessgetStatisticsAction;