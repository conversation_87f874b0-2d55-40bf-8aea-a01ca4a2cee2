/**
 * 获取商业赛信息 基于old项目的getBusinessMatchInfo接口
 * 
 * 微服务: match
 * 模块: business
 * Controller: business
 * Pattern: business.getBusinessMatchInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.551Z
 */

const BaseAction = require('../../../core/base-action');

class BusinessgetBusinessMatchInfoAction extends BaseAction {
  static metadata = {
    name: '获取商业赛信息 基于old项目的getBusinessMatchInfo接口',
    description: '获取商业赛信息 基于old项目的getBusinessMatchInfo接口',
    category: 'match',
    serviceName: 'match',
    module: 'business',
    actionName: 'business.getBusinessMatchInfo',
    prerequisites: ["login","character"],
    params: {
      "getBusinessMatchInfoDto": {
            "type": "object",
            "required": true,
            "description": "getBusinessMatchInfoDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getBusinessMatchInfoDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getBusinessMatchInfoDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取商业赛信息 基于old项目的getBusinessMatchInfo接口成功'
      };
    } else {
      throw new Error(`获取商业赛信息 基于old项目的getBusinessMatchInfo接口失败: ${response.message}`);
    }
  }
}

module.exports = BusinessgetBusinessMatchInfoAction;