/**
 * 转让会长
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.transferPresidency
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.666Z
 */

const BaseAction = require('../../../core/base-action');

class GuildtransferPresidencyAction extends BaseAction {
  static metadata = {
    name: '转让会长',
    description: '转让会长',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.transferPresidency',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      },
      "newPresidentId": {
            "type": "string",
            "required": true,
            "description": "newPresidentId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId, newPresidentId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId,
      newPresidentId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '转让会长成功'
      };
    } else {
      throw new Error(`转让会长失败: ${response.message}`);
    }
  }
}

module.exports = GuildtransferPresidencyAction;