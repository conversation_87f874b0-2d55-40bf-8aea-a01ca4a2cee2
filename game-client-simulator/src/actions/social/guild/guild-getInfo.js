/**
 * 获取公会信息
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.660Z
 */

const BaseAction = require('../../../core/base-action');

class GuildgetInfoAction extends BaseAction {
  static metadata = {
    name: '获取公会信息',
    description: '获取公会信息',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.getInfo',
    prerequisites: ["login","character"],
    params: {
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { guildId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      guildId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取公会信息成功'
      };
    } else {
      throw new Error(`获取公会信息失败: ${response.message}`);
    }
  }
}

module.exports = GuildgetInfoAction;