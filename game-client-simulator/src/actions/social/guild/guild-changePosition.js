/**
 * 职位变更
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.changePosition
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.665Z
 */

const BaseAction = require('../../../core/base-action');

class GuildchangePositionAction extends BaseAction {
  static metadata = {
    name: '职位变更',
    description: '职位变更',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.changePosition',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      },
      "targetCharacterId": {
            "type": "string",
            "required": true,
            "description": "targetCharacterId参数"
      },
      "newPosition": {
            "type": "number",
            "required": true,
            "description": "newPosition参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId, targetCharacterId, newPosition } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId,
      targetCharacterId,
      newPosition
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '职位变更成功'
      };
    } else {
      throw new Error(`职位变更失败: ${response.message}`);
    }
  }
}

module.exports = GuildchangePositionAction;