/**
 * 离开公会
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.leave
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.659Z
 */

const BaseAction = require('../../../core/base-action');

class GuildleaveAction extends BaseAction {
  static metadata = {
    name: '离开公会',
    description: '离开公会',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.leave',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '离开公会成功'
      };
    } else {
      throw new Error(`离开公会失败: ${response.message}`);
    }
  }
}

module.exports = GuildleaveAction;