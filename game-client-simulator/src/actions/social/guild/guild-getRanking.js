/**
 * 获取公会排行榜
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.getRanking
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.670Z
 */

const BaseAction = require('../../../core/base-action');

class GuildgetRankingAction extends BaseAction {
  static metadata = {
    name: '获取公会排行榜',
    description: '获取公会排行榜',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.getRanking',
    prerequisites: ["login","character"],
    params: {
      "page": {
            "type": "number",
            "required": false,
            "description": "page参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { page, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      page,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取公会排行榜成功'
      };
    } else {
      throw new Error(`获取公会排行榜失败: ${response.message}`);
    }
  }
}

module.exports = GuildgetRankingAction;