/**
 * 拒绝加入申请
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.rejectApplication
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.664Z
 */

const BaseAction = require('../../../core/base-action');

class GuildrejectApplicationAction extends BaseAction {
  static metadata = {
    name: '拒绝加入申请',
    description: '拒绝加入申请',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.rejectApplication',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      },
      "rejectId": {
            "type": "string",
            "required": true,
            "description": "rejectId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId, rejectId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId,
      rejectId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '拒绝加入申请成功'
      };
    } else {
      throw new Error(`拒绝加入申请失败: ${response.message}`);
    }
  }
}

module.exports = GuildrejectApplicationAction;