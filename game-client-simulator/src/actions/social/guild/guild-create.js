/**
 * 创建公会
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.create
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.655Z
 */

const BaseAction = require('../../../core/base-action');

class GuildcreateAction extends BaseAction {
  static metadata = {
    name: '创建公会',
    description: '创建公会',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.create',
    prerequisites: ["login"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "characterName": {
            "type": "string",
            "required": true,
            "description": "characterName参数"
      },
      "guildName": {
            "type": "string",
            "required": true,
            "description": "guildName参数"
      },
      "faceId": {
            "type": "number",
            "required": true,
            "description": "faceId参数"
      },
      "strength": {
            "type": "number",
            "required": true,
            "description": "strength参数"
      },
      "gid": {
            "type": "string",
            "required": true,
            "description": "gid参数"
      },
      "faceUrl": {
            "type": "string",
            "required": true,
            "description": "faceUrl参数"
      },
      "frontendId": {
            "type": "string",
            "required": false,
            "description": "frontendId参数"
      },
      "sessionId": {
            "type": "string",
            "required": false,
            "description": "sessionId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, characterName, guildName, faceId, strength, gid, faceUrl, frontendId, sessionId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      characterName,
      guildName,
      faceId,
      strength,
      gid,
      faceUrl,
      frontendId,
      sessionId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建公会成功'
      };
    } else {
      throw new Error(`创建公会失败: ${response.message}`);
    }
  }
}

module.exports = GuildcreateAction;