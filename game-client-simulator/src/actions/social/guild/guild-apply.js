/**
 * 申请加入公会
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.apply
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.657Z
 */

const BaseAction = require('../../../core/base-action');

class GuildapplyAction extends BaseAction {
  static metadata = {
    name: '申请加入公会',
    description: '申请加入公会',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.apply',
    prerequisites: ["login","character"],
    params: {
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      },
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "characterName": {
            "type": "string",
            "required": true,
            "description": "characterName参数"
      },
      "gid": {
            "type": "string",
            "required": true,
            "description": "gid参数"
      },
      "faceUrl": {
            "type": "string",
            "required": true,
            "description": "faceUrl参数"
      },
      "strength": {
            "type": "number",
            "required": true,
            "description": "strength参数"
      },
      "frontendId": {
            "type": "string",
            "required": false,
            "description": "frontendId参数"
      },
      "sessionId": {
            "type": "string",
            "required": false,
            "description": "sessionId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { guildId, characterId, characterName, gid, faceUrl, strength, frontendId, sessionId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      guildId,
      characterId,
      characterName,
      gid,
      faceUrl,
      strength,
      frontendId,
      sessionId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '申请加入公会成功'
      };
    } else {
      throw new Error(`申请加入公会失败: ${response.message}`);
    }
  }
}

module.exports = GuildapplyAction;