/**
 * 同意加入申请
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.approveApplication
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.798Z
 */

const BaseAction = require('../../../core/base-action');

class GuildapproveApplicationAction extends BaseAction {
  static metadata = {
    name: '同意加入申请',
    description: '同意加入申请',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.approveApplication',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      },
      "agreeId": {
            "type": "string",
            "required": true,
            "description": "agreeId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId, agreeId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId,
      agreeId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '同意加入申请成功'
      };
    } else {
      throw new Error(`同意加入申请失败: ${response.message}`);
    }
  }
}

module.exports = GuildapproveApplicationAction;