/**
 * 获取好友申请列表
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.getApplies
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.736Z
 */

const BaseAction = require('../../../core/base-action');

class FriendgetAppliesAction extends BaseAction {
  static metadata = {
    name: '获取好友申请列表',
    description: '获取好友申请列表',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.getApplies',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取好友申请列表成功'
      };
    } else {
      throw new Error(`获取好友申请列表失败: ${response.message}`);
    }
  }
}

module.exports = FriendgetAppliesAction;