/**
 * 搜索好友
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.search
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.642Z
 */

const BaseAction = require('../../../core/base-action');

class FriendsearchAction extends BaseAction {
  static metadata = {
    name: '搜索好友',
    description: '搜索好友',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.search',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "searchDto": {
            "type": "object",
            "required": true,
            "description": "searchDto参数",
            "properties": {
                  "keyword": {
                        "type": "string",
                        "required": false,
                        "description": "keyword参数"
                  },
                  "minLevel": {
                        "type": "number",
                        "required": false,
                        "description": "minLevel参数"
                  },
                  "maxLevel": {
                        "type": "number",
                        "required": false,
                        "description": "maxLevel参数"
                  },
                  "onlineOnly": {
                        "type": "boolean",
                        "required": false,
                        "description": "onlineOnly参数"
                  },
                  "maxDistance": {
                        "type": "number",
                        "required": false,
                        "description": "maxDistance参数"
                  },
                  "longitude": {
                        "type": "number",
                        "required": false,
                        "description": "longitude参数"
                  },
                  "latitude": {
                        "type": "number",
                        "required": false,
                        "description": "latitude参数"
                  }
            },
            "example": {
                  "keyword": "示例keyword",
                  "minLevel": 1,
                  "maxLevel": 1,
                  "onlineOnly": true,
                  "maxDistance": 1,
                  "longitude": 1,
                  "latitude": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, searchDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      searchDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '搜索好友成功'
      };
    } else {
      throw new Error(`搜索好友失败: ${response.message}`);
    }
  }
}

module.exports = FriendsearchAction;