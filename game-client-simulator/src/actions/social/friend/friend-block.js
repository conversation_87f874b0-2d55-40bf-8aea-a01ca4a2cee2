/**
 * 屏蔽玩家
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.block
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.640Z
 */

const BaseAction = require('../../../core/base-action');

class FriendblockAction extends BaseAction {
  static metadata = {
    name: '屏蔽玩家',
    description: '屏蔽玩家',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.block',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "blockDto": {
            "type": "object",
            "required": true,
            "description": "blockDto参数",
            "properties": {
                  "targetCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "targetCharacterId参数"
                  }
            },
            "example": {
                  "targetCharacterId": "示例targetCharacterId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, blockDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      blockDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '屏蔽玩家成功'
      };
    } else {
      throw new Error(`屏蔽玩家失败: ${response.message}`);
    }
  }
}

module.exports = FriendblockAction;