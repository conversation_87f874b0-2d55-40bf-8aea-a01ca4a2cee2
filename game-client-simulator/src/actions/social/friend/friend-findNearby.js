/**
 * 查找附近玩家
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.findNearby
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.644Z
 */

const BaseAction = require('../../../core/base-action');

class FriendfindNearbyAction extends BaseAction {
  static metadata = {
    name: '查找附近玩家',
    description: '查找附近玩家',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.findNearby',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "nearbyDto": {
            "type": "object",
            "required": true,
            "description": "nearbyDto参数",
            "properties": {
                  "longitude": {
                        "type": "number",
                        "required": true,
                        "description": "longitude参数"
                  },
                  "latitude": {
                        "type": "number",
                        "required": true,
                        "description": "latitude参数"
                  },
                  "radius": {
                        "type": "number",
                        "required": false,
                        "description": "radius参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  }
            },
            "example": {
                  "longitude": 1,
                  "latitude": 1,
                  "radius": 1,
                  "limit": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, nearbyDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      nearbyDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '查找附近玩家成功'
      };
    } else {
      throw new Error(`查找附近玩家失败: ${response.message}`);
    }
  }
}

module.exports = FriendfindNearbyAction;