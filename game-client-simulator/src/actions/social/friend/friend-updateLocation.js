/**
 * 更新玩家位置
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.updateLocation
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.645Z
 */

const BaseAction = require('../../../core/base-action');

class FriendupdateLocationAction extends BaseAction {
  static metadata = {
    name: '更新玩家位置',
    description: '更新玩家位置',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.updateLocation',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "locationDto": {
            "type": "object",
            "required": true,
            "description": "locationDto参数",
            "properties": {
                  "longitude": {
                        "type": "number",
                        "required": true,
                        "description": "longitude参数"
                  },
                  "latitude": {
                        "type": "number",
                        "required": true,
                        "description": "latitude参数"
                  }
            },
            "example": {
                  "longitude": 1,
                  "latitude": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, locationDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      locationDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新玩家位置成功'
      };
    } else {
      throw new Error(`更新玩家位置失败: ${response.message}`);
    }
  }
}

module.exports = FriendupdateLocationAction;