/**
 * 添加好友申请
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.add
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.634Z
 */

const BaseAction = require('../../../core/base-action');

class FriendaddAction extends BaseAction {
  static metadata = {
    name: '添加好友申请',
    description: '添加好友申请',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.add',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "addDto": {
            "type": "object",
            "required": true,
            "description": "addDto参数",
            "properties": {
                  "targetCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "targetCharacterId参数"
                  },
                  "message": {
                        "type": "string",
                        "required": false,
                        "description": "message参数"
                  },
                  "longitude": {
                        "type": "number",
                        "required": false,
                        "description": "longitude参数"
                  },
                  "latitude": {
                        "type": "number",
                        "required": false,
                        "description": "latitude参数"
                  }
            },
            "example": {
                  "targetCharacterId": "示例targetCharacterId",
                  "message": "示例message",
                  "longitude": 1,
                  "latitude": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, addDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      addDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加好友申请成功'
      };
    } else {
      throw new Error(`添加好友申请失败: ${response.message}`);
    }
  }
}

module.exports = FriendaddAction;