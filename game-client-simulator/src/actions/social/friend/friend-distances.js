/**
 * 获取好友距离信息
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.distances
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.648Z
 */

const BaseAction = require('../../../core/base-action');

class FrienddistancesAction extends BaseAction {
  static metadata = {
    name: '获取好友距离信息',
    description: '获取好友距离信息',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.distances',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "longitude": {
            "type": "number",
            "required": true,
            "description": "longitude参数"
      },
      "latitude": {
            "type": "number",
            "required": true,
            "description": "latitude参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, longitude, latitude } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      longitude,
      latitude
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取好友距离信息成功'
      };
    } else {
      throw new Error(`获取好友距离信息失败: ${response.message}`);
    }
  }
}

module.exports = FrienddistancesAction;