/**
 * 推荐好友
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.recommend
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.646Z
 */

const BaseAction = require('../../../core/base-action');

class FriendrecommendAction extends BaseAction {
  static metadata = {
    name: '推荐好友',
    description: '推荐好友',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.recommend',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '推荐好友成功'
      };
    } else {
      throw new Error(`推荐好友失败: ${response.message}`);
    }
  }
}

module.exports = FriendrecommendAction;