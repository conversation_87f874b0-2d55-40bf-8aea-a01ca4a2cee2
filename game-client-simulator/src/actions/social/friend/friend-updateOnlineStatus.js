/**
 * 更新玩家在线状态
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.updateOnlineStatus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.650Z
 */

const BaseAction = require('../../../core/base-action');

class FriendupdateOnlineStatusAction extends BaseAction {
  static metadata = {
    name: '更新玩家在线状态',
    description: '更新玩家在线状态',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.updateOnlineStatus',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "isOnline": {
            "type": "boolean",
            "required": true,
            "description": "isOnline参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, isOnline } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      isOnline
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新玩家在线状态成功'
      };
    } else {
      throw new Error(`更新玩家在线状态失败: ${response.message}`);
    }
  }
}

module.exports = FriendupdateOnlineStatusAction;