/**
 * 删除好友
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.remove
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.638Z
 */

const BaseAction = require('../../../core/base-action');

class FriendremoveAction extends BaseAction {
  static metadata = {
    name: '删除好友',
    description: '删除好友',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.remove',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "removeDto": {
            "type": "object",
            "required": true,
            "description": "removeDto参数",
            "properties": {
                  "friendCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "friendCharacterId参数"
                  }
            },
            "example": {
                  "friendCharacterId": "示例friendCharacterId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, removeDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      removeDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '删除好友成功'
      };
    } else {
      throw new Error(`删除好友失败: ${response.message}`);
    }
  }
}

module.exports = FriendremoveAction;