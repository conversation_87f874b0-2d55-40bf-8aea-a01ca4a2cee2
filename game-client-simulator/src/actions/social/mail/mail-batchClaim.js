/**
 * 批量领取附件
 * 
 * 微服务: social
 * 模块: mail
 * Controller: mail
 * Pattern: mail.batchClaim
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.681Z
 */

const BaseAction = require('../../../core/base-action');

class MailbatchClaimAction extends BaseAction {
  static metadata = {
    name: '批量领取附件',
    description: '批量领取附件',
    category: 'social',
    serviceName: 'social',
    module: 'mail',
    actionName: 'mail.batchClaim',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "mailUids": {
            "type": "array",
            "required": true,
            "description": "mailUids参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, mailUids } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      mailUids
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量领取附件成功'
      };
    } else {
      throw new Error(`批量领取附件失败: ${response.message}`);
    }
  }
}

module.exports = MailbatchClaimAction;