/**
 * 发送聊天消息
 * 
 * 微服务: social
 * 模块: chat
 * Controller: chat
 * Pattern: chat.sendMessage
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.623Z
 */

const BaseAction = require('../../../core/base-action');

class ChatsendMessageAction extends BaseAction {
  static metadata = {
    name: '发送聊天消息',
    description: '发送聊天消息',
    category: 'social',
    serviceName: 'social',
    module: 'chat',
    actionName: 'chat.sendMessage',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "messageData": {
            "type": "object",
            "required": true,
            "description": "messageData参数",
            "properties": {
                  "senderId": {
                        "type": "string",
                        "required": true,
                        "description": "senderId参数"
                  },
                  "senderName": {
                        "type": "string",
                        "required": true,
                        "description": "senderName参数"
                  },
                  "channelId": {
                        "type": "string",
                        "required": true,
                        "description": "channelId参数"
                  },
                  "messageType": {
                        "type": "object",
                        "required": true,
                        "description": "messageType参数"
                  },
                  "content": {
                        "type": "string",
                        "required": true,
                        "description": "content参数"
                  },
                  "receiverId": {
                        "type": "string",
                        "required": false,
                        "description": "receiverId参数"
                  },
                  "receiverName": {
                        "type": "string",
                        "required": false,
                        "description": "receiverName参数"
                  },
                  "extraData": {
                        "type": "any",
                        "required": false,
                        "description": "extraData参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": true,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "senderId": "示例senderId",
                  "senderName": "示例senderName",
                  "channelId": "示例channelId",
                  "messageType": {},
                  "content": "示例content",
                  "receiverId": "示例receiverId",
                  "receiverName": "示例receiverName",
                  "extraData": {},
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, messageData } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      messageData
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '发送聊天消息成功'
      };
    } else {
      throw new Error(`发送聊天消息失败: ${response.message}`);
    }
  }
}

module.exports = ChatsendMessageAction;