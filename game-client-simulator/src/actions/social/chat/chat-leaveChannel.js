/**
 * 离开聊天频道
 * 
 * 微服务: social
 * 模块: chat
 * Controller: chat
 * Pattern: chat.leaveChannel
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.631Z
 */

const BaseAction = require('../../../core/base-action');

class ChatleaveChannelAction extends BaseAction {
  static metadata = {
    name: '离开聊天频道',
    description: '离开聊天频道',
    category: 'social',
    serviceName: 'social',
    module: 'chat',
    actionName: 'chat.leaveChannel',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "channelId": {
            "type": "string",
            "required": true,
            "description": "channelId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, channelId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      channelId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '离开聊天频道成功'
      };
    } else {
      throw new Error(`离开聊天频道失败: ${response.message}`);
    }
  }
}

module.exports = ChatleaveChannelAction;