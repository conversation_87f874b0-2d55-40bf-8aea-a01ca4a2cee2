/**
 * 领取每日精力
 * 
 * 微服务: activity
 * 模块: energy
 * Controller: energy
 * Pattern: energy.takeDaily
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.117Z
 */

const BaseAction = require('../../../core/base-action');

class EnergytakeDailyAction extends BaseAction {
  static metadata = {
    name: '领取每日精力',
    description: '领取每日精力',
    category: 'activity',
    serviceName: 'activity',
    module: 'energy',
    actionName: 'energy.takeDaily',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "timeSlot": {
            "type": "object",
            "required": true,
            "description": "timeSlot参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "EnergyTimeSlot类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, timeSlot } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      timeSlot
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取每日精力成功'
      };
    } else {
      throw new Error(`领取每日精力失败: ${response.message}`);
    }
  }
}

module.exports = EnergytakeDailyAction;