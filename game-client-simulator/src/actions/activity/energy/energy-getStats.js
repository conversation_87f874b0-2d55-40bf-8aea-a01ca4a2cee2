/**
 * 获取精力统计信息（管理接口）
 * 
 * 微服务: activity
 * 模块: energy
 * Controller: energy
 * Pattern: energy.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.122Z
 */

const BaseAction = require('../../../core/base-action');

class EnergygetStatsAction extends BaseAction {
  static metadata = {
    name: '获取精力统计信息（管理接口）',
    description: '获取精力统计信息（管理接口）',
    category: 'activity',
    serviceName: 'activity',
    module: 'energy',
    actionName: 'energy.getStats',
    prerequisites: ["login","character"],
    params: {
      "adminToken": {
            "type": "string",
            "required": false,
            "description": "adminToken参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { adminToken } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      adminToken
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取精力统计信息（管理接口）成功'
      };
    } else {
      throw new Error(`获取精力统计信息（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = EnergygetStatsAction;