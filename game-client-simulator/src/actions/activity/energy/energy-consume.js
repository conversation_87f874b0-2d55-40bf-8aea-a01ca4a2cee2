/**
 * 消耗精力
 * 
 * 微服务: activity
 * 模块: energy
 * Controller: energy
 * Pattern: energy.consume
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.118Z
 */

const BaseAction = require('../../../core/base-action');

class EnergyconsumeAction extends BaseAction {
  static metadata = {
    name: '消耗精力',
    description: '消耗精力',
    category: 'activity',
    serviceName: 'activity',
    module: 'energy',
    actionName: 'energy.consume',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "amount": {
            "type": "number",
            "required": true,
            "description": "amount参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, amount } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      amount
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '消耗精力成功'
      };
    } else {
      throw new Error(`消耗精力失败: ${response.message}`);
    }
  }
}

module.exports = EnergyconsumeAction;