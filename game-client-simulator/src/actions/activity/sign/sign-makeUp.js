/**
 * 补签
 * 
 * 微服务: activity
 * 模块: sign
 * Controller: sign
 * Pattern: sign.makeUp
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.156Z
 */

const BaseAction = require('../../../core/base-action');

class SignmakeUpAction extends BaseAction {
  static metadata = {
    name: '补签',
    description: '补签',
    category: 'activity',
    serviceName: 'activity',
    module: 'sign',
    actionName: 'sign.makeUp',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "targetDay": {
            "type": "number",
            "required": true,
            "description": "targetDay参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, targetDay } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      targetDay
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '补签成功'
      };
    } else {
      throw new Error(`补签失败: ${response.message}`);
    }
  }
}

module.exports = SignmakeUpAction;