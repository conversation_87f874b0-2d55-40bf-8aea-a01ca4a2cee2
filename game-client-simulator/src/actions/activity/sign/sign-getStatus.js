/**
 * 获取签到状态
 * 
 * 微服务: activity
 * 模块: sign
 * Controller: sign
 * Pattern: sign.getStatus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.153Z
 */

const BaseAction = require('../../../core/base-action');

class SigngetStatusAction extends BaseAction {
  static metadata = {
    name: '获取签到状态',
    description: '获取签到状态',
    category: 'activity',
    serviceName: 'activity',
    module: 'sign',
    actionName: 'sign.getStatus',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "openServerTime": {
            "type": "number",
            "required": false,
            "description": "openServerTime参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, openServerTime } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      openServerTime
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取签到状态成功'
      };
    } else {
      throw new Error(`获取签到状态失败: ${response.message}`);
    }
  }
}

module.exports = SigngetStatusAction;