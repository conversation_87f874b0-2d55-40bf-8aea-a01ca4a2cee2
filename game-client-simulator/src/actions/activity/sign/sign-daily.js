/**
 * 每日签到
 * 
 * 微服务: activity
 * 模块: sign
 * Controller: sign
 * Pattern: sign.daily
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.151Z
 */

const BaseAction = require('../../../core/base-action');

class SigndailyAction extends BaseAction {
  static metadata = {
    name: '每日签到',
    description: '每日签到',
    category: 'activity',
    serviceName: 'activity',
    module: 'sign',
    actionName: 'sign.daily',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "openServerTime": {
            "type": "number",
            "required": false,
            "description": "openServerTime参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, openServerTime } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      openServerTime
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '每日签到成功'
      };
    } else {
      throw new Error(`每日签到失败: ${response.message}`);
    }
  }
}

module.exports = SigndailyAction;