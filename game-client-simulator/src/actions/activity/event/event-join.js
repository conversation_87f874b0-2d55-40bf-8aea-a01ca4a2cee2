/**
 * 参与活动
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.join
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.222Z
 */

const BaseAction = require('../../../core/base-action');

class EventjoinAction extends BaseAction {
  static metadata = {
    name: '参与活动',
    description: '参与活动',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.join',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "eventId": {
            "type": "number",
            "required": true,
            "description": "eventId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, eventId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      eventId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '参与活动成功'
      };
    } else {
      throw new Error(`参与活动失败: ${response.message}`);
    }
  }
}

module.exports = EventjoinAction;