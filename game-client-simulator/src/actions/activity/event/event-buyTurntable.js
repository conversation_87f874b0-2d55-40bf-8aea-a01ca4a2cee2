/**
 * 老虎机抽奖
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.buyTurntable
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.131Z
 */

const BaseAction = require('../../../core/base-action');

class EventbuyTurntableAction extends BaseAction {
  static metadata = {
    name: '老虎机抽奖',
    description: '老虎机抽奖',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.buyTurntable',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "frequencyType": {
            "type": "number",
            "required": true,
            "description": "frequencyType参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, frequencyType } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      frequencyType
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '老虎机抽奖成功'
      };
    } else {
      throw new Error(`老虎机抽奖失败: ${response.message}`);
    }
  }
}

module.exports = EventbuyTurntableAction;