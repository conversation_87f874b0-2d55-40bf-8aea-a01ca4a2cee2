/**
 * 周末返场抽奖 基于old项目: Act.prototype.weekDayEncore
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.weekDayEncore
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.133Z
 */

const BaseAction = require('../../../core/base-action');

class EventweekDayEncoreAction extends BaseAction {
  static metadata = {
    name: '周末返场抽奖 基于old项目: Act.prototype.weekDayEncore',
    description: '周末返场抽奖 基于old项目: Act.prototype.weekDayEncore',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.weekDayEncore',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '周末返场抽奖 基于old项目: Act.prototype.weekDayEncore成功'
      };
    } else {
      throw new Error(`周末返场抽奖 基于old项目: Act.prototype.weekDayEncore失败: ${response.message}`);
    }
  }
}

module.exports = EventweekDayEncoreAction;