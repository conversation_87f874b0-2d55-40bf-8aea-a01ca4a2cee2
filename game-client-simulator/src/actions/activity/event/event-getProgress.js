/**
 * 获取活动进度
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.getProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.128Z
 */

const BaseAction = require('../../../core/base-action');

class EventgetProgressAction extends BaseAction {
  static metadata = {
    name: '获取活动进度',
    description: '获取活动进度',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.getProgress',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "eventId": {
            "type": "number",
            "required": true,
            "description": "eventId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, eventId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      eventId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取活动进度成功'
      };
    } else {
      throw new Error(`获取活动进度失败: ${response.message}`);
    }
  }
}

module.exports = EventgetProgressAction;