/**
 * 拉霸抽奖
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.buySlots
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.132Z
 */

const BaseAction = require('../../../core/base-action');

class EventbuySlotsAction extends BaseAction {
  static metadata = {
    name: '拉霸抽奖',
    description: '拉霸抽奖',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.buySlots',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "frequencyType": {
            "type": "number",
            "required": true,
            "description": "frequencyType参数"
      },
      "securityMoney": {
            "type": "number",
            "required": true,
            "description": "securityMoney参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, frequencyType, securityMoney, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      frequencyType,
      securityMoney,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '拉霸抽奖成功'
      };
    } else {
      throw new Error(`拉霸抽奖失败: ${response.message}`);
    }
  }
}

module.exports = EventbuySlotsAction;