/**
 * 获取活动列表
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.221Z
 */

const BaseAction = require('../../../core/base-action');

class EventgetListAction extends BaseAction {
  static metadata = {
    name: '获取活动列表',
    description: '获取活动列表',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.getList',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取活动列表成功'
      };
    } else {
      throw new Error(`获取活动列表失败: ${response.message}`);
    }
  }
}

module.exports = EventgetListAction;