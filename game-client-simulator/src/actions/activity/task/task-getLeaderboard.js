/**
 * 获取任务排行榜
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.getLeaderboard
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.171Z
 */

const BaseAction = require('../../../core/base-action');

class TaskgetLeaderboardAction extends BaseAction {
  static metadata = {
    name: '获取任务排行榜',
    description: '获取任务排行榜',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.getLeaderboard',
    prerequisites: ["login","character"],
    params: {
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskType": {
            "type": "object",
            "required": false,
            "description": "taskType参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "TaskType类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { serverId, taskType, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      serverId,
      taskType,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取任务排行榜成功'
      };
    } else {
      throw new Error(`获取任务排行榜失败: ${response.message}`);
    }
  }
}

module.exports = TaskgetLeaderboardAction;