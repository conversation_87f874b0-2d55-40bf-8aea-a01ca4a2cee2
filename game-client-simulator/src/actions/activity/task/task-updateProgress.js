/**
 * 更新任务进度
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.updateProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.159Z
 */

const BaseAction = require('../../../core/base-action');

class TaskupdateProgressAction extends BaseAction {
  static metadata = {
    name: '更新任务进度',
    description: '更新任务进度',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.updateProgress',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "updateDto": {
            "type": "object",
            "required": true,
            "description": "updateDto参数",
            "properties": {
                  "taskId": {
                        "type": "number",
                        "required": true,
                        "description": "taskId参数"
                  },
                  "value": {
                        "type": "number",
                        "required": true,
                        "description": "value参数"
                  },
                  "forceComplete": {
                        "type": "boolean",
                        "required": false,
                        "description": "forceComplete参数"
                  }
            },
            "example": {
                  "taskId": 1,
                  "value": 1,
                  "forceComplete": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, updateDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      updateDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新任务进度成功'
      };
    } else {
      throw new Error(`更新任务进度失败: ${response.message}`);
    }
  }
}

module.exports = TaskupdateProgressAction;