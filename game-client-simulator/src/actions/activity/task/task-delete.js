/**
 * 删除任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.delete
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.180Z
 */

const BaseAction = require('../../../core/base-action');

class TaskdeleteAction extends BaseAction {
  static metadata = {
    name: '删除任务',
    description: '删除任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.delete',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskType": {
            "type": "object",
            "required": true,
            "description": "taskType参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "TaskType类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      },
      "taskId": {
            "type": "number",
            "required": true,
            "description": "taskId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, taskType, taskId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      taskType,
      taskId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '删除任务成功'
      };
    } else {
      throw new Error(`删除任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskdeleteAction;