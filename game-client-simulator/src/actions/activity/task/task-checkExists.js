/**
 * 检查任务是否存在
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.checkExists
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.182Z
 */

const BaseAction = require('../../../core/base-action');

class TaskcheckExistsAction extends BaseAction {
  static metadata = {
    name: '检查任务是否存在',
    description: '检查任务是否存在',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.checkExists',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskId": {
            "type": "number",
            "required": true,
            "description": "taskId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, taskId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      taskId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '检查任务是否存在成功'
      };
    } else {
      throw new Error(`检查任务是否存在失败: ${response.message}`);
    }
  }
}

module.exports = TaskcheckExistsAction;