/**
 * 更新活动任务进度 对应old项目act.js中的updateTaskProgress方法
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.updateActivityProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.183Z
 */

const BaseAction = require('../../../core/base-action');

class TaskupdateActivityProgressAction extends BaseAction {
  static metadata = {
    name: '更新活动任务进度 对应old项目act.js中的updateTaskProgress方法',
    description: '更新活动任务进度 对应old项目act.js中的updateTaskProgress方法',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.updateActivityProgress',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "activityId": {
            "type": "string",
            "required": true,
            "description": "activityId参数"
      },
      "taskId": {
            "type": "number",
            "required": true,
            "description": "taskId参数"
      },
      "progress": {
            "type": "number",
            "required": true,
            "description": "progress参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, activityId, taskId, progress } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      activityId,
      taskId,
      progress
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新活动任务进度 对应old项目act.js中的updateTaskProgress方法成功'
      };
    } else {
      throw new Error(`更新活动任务进度 对应old项目act.js中的updateTaskProgress方法失败: ${response.message}`);
    }
  }
}

module.exports = TaskupdateActivityProgressAction;