/**
 * 领取任务奖励
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.claimReward
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.262Z
 */

const BaseAction = require('../../../core/base-action');

class TaskclaimRewardAction extends BaseAction {
  static metadata = {
    name: '领取任务奖励',
    description: '领取任务奖励',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.claimReward',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "claimDto": {
            "type": "object",
            "required": true,
            "description": "claimDto参数",
            "properties": {
                  "taskId": {
                        "type": "number",
                        "required": true,
                        "description": "taskId参数"
                  },
                  "taskType": {
                        "type": "enum",
                        "required": false,
                        "description": "taskType参数"
                  }
            },
            "example": {
                  "taskId": 1,
                  "taskType": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, claimDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      claimDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取任务奖励成功'
      };
    } else {
      throw new Error(`领取任务奖励失败: ${response.message}`);
    }
  }
}

module.exports = TaskclaimRewardAction;