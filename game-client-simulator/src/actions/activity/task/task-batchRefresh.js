/**
 * 批量刷新任务（定时任务用）
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.batchRefresh
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.173Z
 */

const BaseAction = require('../../../core/base-action');

class TaskbatchRefreshAction extends BaseAction {
  static metadata = {
    name: '批量刷新任务（定时任务用）',
    description: '批量刷新任务（定时任务用）',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.batchRefresh',
    prerequisites: ["login","character"],
    params: {
      "taskType": {
            "type": "object",
            "required": true,
            "description": "taskType参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "TaskType类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { taskType } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      taskType
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量刷新任务（定时任务用）成功'
      };
    } else {
      throw new Error(`批量刷新任务（定时任务用）失败: ${response.message}`);
    }
  }
}

module.exports = TaskbatchRefreshAction;