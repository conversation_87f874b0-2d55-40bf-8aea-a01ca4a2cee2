/**
 * 触发任务进度更新 对应old项目中最核心的triggerTask方法
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.trigger
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.178Z
 */

const BaseAction = require('../../../core/base-action');

class TasktriggerAction extends BaseAction {
  static metadata = {
    name: '触发任务进度更新 对应old项目中最核心的triggerTask方法',
    description: '触发任务进度更新 对应old项目中最核心的triggerTask方法',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.trigger',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "triggerType": {
            "type": "number",
            "required": true,
            "description": "triggerType参数"
      },
      "arg1": {
            "type": "any",
            "required": false,
            "description": "arg1参数"
      },
      "arg2": {
            "type": "any",
            "required": false,
            "description": "arg2参数"
      },
      "arg3": {
            "type": "any",
            "required": false,
            "description": "arg3参数"
      },
      "arg4": {
            "type": "any",
            "required": false,
            "description": "arg4参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, triggerType, arg1, arg2, arg3, arg4 } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      triggerType,
      arg1,
      arg2,
      arg3,
      arg4
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '触发任务进度更新 对应old项目中最核心的triggerTask方法成功'
      };
    } else {
      throw new Error(`触发任务进度更新 对应old项目中最核心的triggerTask方法失败: ${response.message}`);
    }
  }
}

module.exports = TasktriggerAction;