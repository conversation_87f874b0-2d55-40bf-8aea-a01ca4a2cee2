/**
 * 刷新任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.refresh
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.162Z
 */

const BaseAction = require('../../../core/base-action');

class TaskrefreshAction extends BaseAction {
  static metadata = {
    name: '刷新任务',
    description: '刷新任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.refresh',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "refreshDto": {
            "type": "object",
            "required": true,
            "description": "refreshDto参数",
            "properties": {
                  "taskType": {
                        "type": "object",
                        "required": true,
                        "description": "taskType参数"
                  },
                  "forceRefresh": {
                        "type": "boolean",
                        "required": false,
                        "description": "forceRefresh参数"
                  }
            },
            "example": {
                  "taskType": {},
                  "forceRefresh": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, refreshDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      refreshDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '刷新任务成功'
      };
    } else {
      throw new Error(`刷新任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskrefreshAction;