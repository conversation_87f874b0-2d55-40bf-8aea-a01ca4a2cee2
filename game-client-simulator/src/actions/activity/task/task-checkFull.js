/**
 * 检查任务列表是否已满
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.checkFull
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.181Z
 */

const BaseAction = require('../../../core/base-action');

class TaskcheckFullAction extends BaseAction {
  static metadata = {
    name: '检查任务列表是否已满',
    description: '检查任务列表是否已满',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.checkFull',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskType": {
            "type": "object",
            "required": true,
            "description": "taskType参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "TaskType类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, taskType } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      taskType
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '检查任务列表是否已满成功'
      };
    } else {
      throw new Error(`检查任务列表是否已满失败: ${response.message}`);
    }
  }
}

module.exports = TaskcheckFullAction;