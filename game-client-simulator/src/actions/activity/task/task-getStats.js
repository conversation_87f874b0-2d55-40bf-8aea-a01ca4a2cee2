/**
 * 获取任务统计
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.166Z
 */

const BaseAction = require('../../../core/base-action');

class TaskgetStatsAction extends BaseAction {
  static metadata = {
    name: '获取任务统计',
    description: '获取任务统计',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.getStats',
    prerequisites: ["login","character"],
    params: {
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "days": {
                        "type": "number",
                        "required": false,
                        "description": "days参数"
                  },
                  "taskType": {
                        "type": "object",
                        "required": false,
                        "description": "taskType参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "days": 1,
                  "taskType": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { serverId, query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      serverId,
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取任务统计成功'
      };
    } else {
      throw new Error(`获取任务统计失败: ${response.message}`);
    }
  }
}

module.exports = TaskgetStatsAction;