/**
 * 添加新任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.add
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.164Z
 */

const BaseAction = require('../../../core/base-action');

class TaskaddAction extends BaseAction {
  static metadata = {
    name: '添加新任务',
    description: '添加新任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.add',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "addDto": {
            "type": "object",
            "required": true,
            "description": "addDto参数",
            "properties": {
                  "taskType": {
                        "type": "object",
                        "required": true,
                        "description": "taskType参数"
                  },
                  "taskId": {
                        "type": "number",
                        "required": true,
                        "description": "taskId参数"
                  },
                  "targetValue": {
                        "type": "number",
                        "required": true,
                        "description": "targetValue参数"
                  },
                  "description": {
                        "type": "string",
                        "required": true,
                        "description": "description参数"
                  },
                  "rewards": {
                        "type": "array",
                        "required": true,
                        "description": "rewards参数"
                  },
                  "expireTime": {
                        "type": "number",
                        "required": false,
                        "description": "expireTime参数"
                  }
            },
            "example": {
                  "taskType": {},
                  "taskId": 1,
                  "targetValue": 1,
                  "description": "示例description",
                  "rewards": [],
                  "expireTime": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, addDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      addDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加新任务成功'
      };
    } else {
      throw new Error(`添加新任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskaddAction;