/**
 * 批量更新任务进度
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.batchUpdateProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.160Z
 */

const BaseAction = require('../../../core/base-action');

class TaskbatchUpdateProgressAction extends BaseAction {
  static metadata = {
    name: '批量更新任务进度',
    description: '批量更新任务进度',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.batchUpdateProgress',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "batchDto": {
            "type": "object",
            "required": true,
            "description": "batchDto参数",
            "properties": {
                  "updates": {
                        "type": "array",
                        "required": true,
                        "description": "updates参数"
                  }
            },
            "example": {
                  "updates": []
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, batchDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      batchDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量更新任务进度成功'
      };
    } else {
      throw new Error(`批量更新任务进度失败: ${response.message}`);
    }
  }
}

module.exports = TaskbatchUpdateProgressAction;