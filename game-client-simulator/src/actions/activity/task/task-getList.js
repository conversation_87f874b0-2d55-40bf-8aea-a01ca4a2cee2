/**
 * 获取任务列表
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.158Z
 */

const BaseAction = require('../../../core/base-action');

class TaskgetListAction extends BaseAction {
  static metadata = {
    name: '获取任务列表',
    description: '获取任务列表',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.getList',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "query": {
            "type": "object",
            "required": false,
            "description": "query参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "taskType": {
                        "type": "object",
                        "required": false,
                        "description": "taskType参数"
                  },
                  "status": {
                        "type": "object",
                        "required": false,
                        "description": "status参数"
                  },
                  "claimableOnly": {
                        "type": "boolean",
                        "required": false,
                        "description": "claimableOnly参数"
                  },
                  "includeExpired": {
                        "type": "boolean",
                        "required": false,
                        "description": "includeExpired参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "taskType": {},
                  "status": {},
                  "claimableOnly": true,
                  "includeExpired": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取任务列表成功'
      };
    } else {
      throw new Error(`获取任务列表失败: ${response.message}`);
    }
  }
}

module.exports = TaskgetListAction;