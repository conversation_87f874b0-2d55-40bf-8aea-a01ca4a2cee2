/**
 * 批量更新活动任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.batchUpdateActivity
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.186Z
 */

const BaseAction = require('../../../core/base-action');

class TaskbatchUpdateActivityAction extends BaseAction {
  static metadata = {
    name: '批量更新活动任务',
    description: '批量更新活动任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.batchUpdateActivity',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "updates": {
            "type": "array",
            "required": true,
            "description": "updates参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, updates } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      updates
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量更新活动任务成功'
      };
    } else {
      throw new Error(`批量更新活动任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskbatchUpdateActivityAction;