/**
 * 清理过期任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.cleanExpired
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.175Z
 */

const BaseAction = require('../../../core/base-action');

class TaskcleanExpiredAction extends BaseAction {
  static metadata = {
    name: '清理过期任务',
    description: '清理过期任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.cleanExpired',
    prerequisites: ["login","character"],
    params: {},
    timeout: 10000
  };

  async perform(client, params) {
    // 无参数
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '清理过期任务成功'
      };
    } else {
      throw new Error(`清理过期任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskcleanExpiredAction;