/**
 * 完成引导步骤
 * 
 * 微服务: activity
 * 模块: guide
 * Controller: guide
 * Pattern: guide.completeStep
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.137Z
 */

const BaseAction = require('../../../core/base-action');

class GuidecompleteStepAction extends BaseAction {
  static metadata = {
    name: '完成引导步骤',
    description: '完成引导步骤',
    category: 'activity',
    serviceName: 'activity',
    module: 'guide',
    actionName: 'guide.completeStep',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "stepId": {
            "type": "number",
            "required": true,
            "description": "stepId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, stepId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      stepId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '完成引导步骤成功'
      };
    } else {
      throw new Error(`完成引导步骤失败: ${response.message}`);
    }
  }
}

module.exports = GuidecompleteStepAction;