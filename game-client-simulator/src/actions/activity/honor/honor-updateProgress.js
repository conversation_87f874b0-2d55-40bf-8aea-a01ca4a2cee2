/**
 * 更新荣誉任务进度
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.updateProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.144Z
 */

const BaseAction = require('../../../core/base-action');

class HonorupdateProgressAction extends BaseAction {
  static metadata = {
    name: '更新荣誉任务进度',
    description: '更新荣誉任务进度',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.updateProgress',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskId": {
            "type": "number",
            "required": true,
            "description": "taskId参数"
      },
      "progress": {
            "type": "number",
            "required": true,
            "description": "progress参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, taskId, progress } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      taskId,
      progress
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新荣誉任务进度成功'
      };
    } else {
      throw new Error(`更新荣誉任务进度失败: ${response.message}`);
    }
  }
}

module.exports = HonorupdateProgressAction;