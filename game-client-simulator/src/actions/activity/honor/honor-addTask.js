/**
 * 添加荣誉任务
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.addTask
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.145Z
 */

const BaseAction = require('../../../core/base-action');

class HonoraddTaskAction extends BaseAction {
  static metadata = {
    name: '添加荣誉任务',
    description: '添加荣誉任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.addTask',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskConfig": {
            "type": "any",
            "required": true,
            "description": "taskConfig参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, taskConfig } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      taskConfig
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加荣誉任务成功'
      };
    } else {
      throw new Error(`添加荣誉任务失败: ${response.message}`);
    }
  }
}

module.exports = HonoraddTaskAction;