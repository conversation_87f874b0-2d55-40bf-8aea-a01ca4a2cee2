/**
 * 获取荣誉等级奖励
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.claimLevelReward
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.147Z
 */

const BaseAction = require('../../../core/base-action');

class HonorclaimLevelRewardAction extends BaseAction {
  static metadata = {
    name: '获取荣誉等级奖励',
    description: '获取荣誉等级奖励',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.claimLevelReward',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "level": {
            "type": "number",
            "required": true,
            "description": "level参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, level } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      level
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取荣誉等级奖励成功'
      };
    } else {
      throw new Error(`获取荣誉等级奖励失败: ${response.message}`);
    }
  }
}

module.exports = HonorclaimLevelRewardAction;