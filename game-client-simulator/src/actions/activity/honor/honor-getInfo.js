/**
 * 获取荣誉墙信息
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.239Z
 */

const BaseAction = require('../../../core/base-action');

class HonorgetInfoAction extends BaseAction {
  static metadata = {
    name: '获取荣誉墙信息',
    description: '获取荣誉墙信息',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.getInfo',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取荣誉墙信息成功'
      };
    } else {
      throw new Error(`获取荣誉墙信息失败: ${response.message}`);
    }
  }
}

module.exports = HonorgetInfoAction;