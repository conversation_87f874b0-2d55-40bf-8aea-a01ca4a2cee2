/**
 * 触发荣誉任务检查
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.trigger
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.148Z
 */

const BaseAction = require('../../../core/base-action');

class HonortriggerAction extends BaseAction {
  static metadata = {
    name: '触发荣誉任务检查',
    description: '触发荣誉任务检查',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.trigger',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "triggerType": {
            "type": "number",
            "required": true,
            "description": "triggerType参数"
      },
      "param": {
            "type": "any",
            "required": false,
            "description": "param参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, triggerType, param } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      triggerType,
      param
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '触发荣誉任务检查成功'
      };
    } else {
      throw new Error(`触发荣誉任务检查失败: ${response.message}`);
    }
  }
}

module.exports = HonortriggerAction;