/**
 * 货币转换
 * 
 * 微服务: economy
 * 模块: currency
 * Controller: currency
 * Pattern: currency.convert
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.324Z
 */

const BaseAction = require('../../../core/base-action');

class CurrencyconvertAction extends BaseAction {
  static metadata = {
    name: '货币转换',
    description: '货币转换',
    category: 'economy',
    serviceName: 'economy',
    module: 'currency',
    actionName: 'currency.convert',
    prerequisites: ["login","character"],
    params: {
      "fromCurrency": {
            "type": "string",
            "required": true,
            "description": "fromCurrency参数"
      },
      "toCurrency": {
            "type": "string",
            "required": true,
            "description": "toCurrency参数"
      },
      "amount": {
            "type": "number",
            "required": true,
            "description": "amount参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { fromCurrency, toCurrency, amount } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      fromCurrency,
      toCurrency,
      amount
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '货币转换成功'
      };
    } else {
      throw new Error(`货币转换失败: ${response.message}`);
    }
  }
}

module.exports = CurrencyconvertAction;