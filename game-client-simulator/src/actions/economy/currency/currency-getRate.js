/**
 * 获取汇率
 * 
 * 微服务: economy
 * 模块: currency
 * Controller: currency
 * Pattern: currency.getRate
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.325Z
 */

const BaseAction = require('../../../core/base-action');

class CurrencygetRateAction extends BaseAction {
  static metadata = {
    name: '获取汇率',
    description: '获取汇率',
    category: 'economy',
    serviceName: 'economy',
    module: 'currency',
    actionName: 'currency.getRate',
    prerequisites: ["login","character"],
    params: {
      "fromCurrency": {
            "type": "string",
            "required": true,
            "description": "fromCurrency参数"
      },
      "toCurrency": {
            "type": "string",
            "required": true,
            "description": "toCurrency参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { fromCurrency, toCurrency } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      fromCurrency,
      toCurrency
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取汇率成功'
      };
    } else {
      throw new Error(`获取汇率失败: ${response.message}`);
    }
  }
}

module.exports = CurrencygetRateAction;