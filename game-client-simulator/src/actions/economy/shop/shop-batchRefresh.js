/**
 * 批量刷新商店（定时任务用）
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.batchRefresh
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.383Z
 */

const BaseAction = require('../../../core/base-action');

class ShopbatchRefreshAction extends BaseAction {
  static metadata = {
    name: '批量刷新商店（定时任务用）',
    description: '批量刷新商店（定时任务用）',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.batchRefresh',
    prerequisites: ["login","character"],
    params: {
      "cycle": {
            "type": "object",
            "required": true,
            "description": "cycle参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "RefreshCycle类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { cycle } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      cycle
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量刷新商店（定时任务用）成功'
      };
    } else {
      throw new Error(`批量刷新商店（定时任务用）失败: ${response.message}`);
    }
  }
}

module.exports = ShopbatchRefreshAction;