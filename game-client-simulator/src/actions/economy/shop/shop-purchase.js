/**
 * 购买商品
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.purchase
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.372Z
 */

const BaseAction = require('../../../core/base-action');

class ShoppurchaseAction extends BaseAction {
  static metadata = {
    name: '购买商品',
    description: '购买商品',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.purchase',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "purchaseDto": {
            "type": "object",
            "required": true,
            "description": "purchaseDto参数",
            "properties": {
                  "goodsId": {
                        "type": "number",
                        "required": true,
                        "description": "goodsId参数"
                  },
                  "quantity": {
                        "type": "number",
                        "required": true,
                        "description": "quantity参数"
                  },
                  "shopType": {
                        "type": "object",
                        "required": true,
                        "description": "shopType参数"
                  },
                  "currency": {
                        "type": "string",
                        "required": false,
                        "description": "currency参数"
                  },
                  "useDiscount": {
                        "type": "boolean",
                        "required": false,
                        "description": "useDiscount参数"
                  }
            },
            "example": {
                  "goodsId": 1,
                  "quantity": 1,
                  "shopType": {},
                  "currency": "示例currency",
                  "useDiscount": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, purchaseDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      purchaseDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买商品成功'
      };
    } else {
      throw new Error(`购买商品失败: ${response.message}`);
    }
  }
}

module.exports = ShoppurchaseAction;