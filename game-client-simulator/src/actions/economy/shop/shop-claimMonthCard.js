/**
 * 领取月卡奖励
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.claimMonthCard
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.376Z
 */

const BaseAction = require('../../../core/base-action');

class ShopclaimMonthCardAction extends BaseAction {
  static metadata = {
    name: '领取月卡奖励',
    description: '领取月卡奖励',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.claimMonthCard',
    prerequisites: ["login","character"],
    params: {
      "claimDto": {
            "type": "object",
            "required": true,
            "description": "claimDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "cardType": {
                        "type": "number",
                        "required": false,
                        "description": "cardType参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "cardType": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { claimDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      claimDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取月卡奖励成功'
      };
    } else {
      throw new Error(`领取月卡奖励失败: ${response.message}`);
    }
  }
}

module.exports = ShopclaimMonthCardAction;