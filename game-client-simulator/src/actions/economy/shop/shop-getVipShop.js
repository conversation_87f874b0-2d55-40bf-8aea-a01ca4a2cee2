/**
 * 获取VIP商店信息
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getVipShop
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.378Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetVipShopAction extends BaseAction {
  static metadata = {
    name: '获取VIP商店信息',
    description: '获取VIP商店信息',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getVipShop',
    prerequisites: ["login","character"],
    params: {
      "vipShopDto": {
            "type": "object",
            "required": true,
            "description": "vipShopDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "vipLevel": {
                        "type": "number",
                        "required": true,
                        "description": "vipLevel参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "vipLevel": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { vipShopDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      vipShopDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取VIP商店信息成功'
      };
    } else {
      throw new Error(`获取VIP商店信息失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetVipShopAction;