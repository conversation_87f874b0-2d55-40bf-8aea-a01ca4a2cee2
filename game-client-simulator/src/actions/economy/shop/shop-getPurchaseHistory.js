/**
 * 获取购买历史
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getPurchaseHistory
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.381Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetPurchaseHistoryAction extends BaseAction {
  static metadata = {
    name: '获取购买历史',
    description: '获取购买历史',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getPurchaseHistory',
    prerequisites: ["login","character"],
    params: {
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "shopType": {
                        "type": "object",
                        "required": false,
                        "description": "shopType参数"
                  },
                  "goodsId": {
                        "type": "number",
                        "required": false,
                        "description": "goodsId参数"
                  },
                  "startTime": {
                        "type": "number",
                        "required": false,
                        "description": "startTime参数"
                  },
                  "endTime": {
                        "type": "number",
                        "required": false,
                        "description": "endTime参数"
                  },
                  "page": {
                        "type": "number",
                        "required": false,
                        "description": "page参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "shopType": {},
                  "goodsId": 1,
                  "startTime": 1,
                  "endTime": 1,
                  "page": 1,
                  "limit": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取购买历史成功'
      };
    } else {
      throw new Error(`获取购买历史失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetPurchaseHistoryAction;