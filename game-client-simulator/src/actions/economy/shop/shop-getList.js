/**
 * 获取商店列表
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.370Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetListAction extends BaseAction {
  static metadata = {
    name: '获取商店列表',
    description: '获取商店列表',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getList',
    prerequisites: ["login","character"],
    params: {
      "getShopListDto": {
            "type": "object",
            "required": true,
            "description": "getShopListDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "shopType": {
                        "type": "object",
                        "required": false,
                        "description": "shopType参数"
                  },
                  "hasRecordsOnly": {
                        "type": "boolean",
                        "required": false,
                        "description": "hasRecordsOnly参数"
                  },
                  "sortBy": {
                        "type": "object",
                        "required": false,
                        "description": "sortBy参数"
                  },
                  "sortOrder": {
                        "type": "object",
                        "required": false,
                        "description": "sortOrder参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "shopType": {},
                  "hasRecordsOnly": true,
                  "sortBy": {},
                  "sortOrder": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getShopListDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getShopListDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取商店列表成功'
      };
    } else {
      throw new Error(`获取商店列表失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetListAction;