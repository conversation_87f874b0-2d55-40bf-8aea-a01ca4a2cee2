/**
 * 获取商店统计
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.382Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetStatsAction extends BaseAction {
  static metadata = {
    name: '获取商店统计',
    description: '获取商店统计',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getStats',
    prerequisites: ["login","character"],
    params: {
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "days": {
                        "type": "number",
                        "required": false,
                        "description": "days参数"
                  },
                  "shopType": {
                        "type": "object",
                        "required": false,
                        "description": "shopType参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "days": 1,
                  "shopType": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取商店统计成功'
      };
    } else {
      throw new Error(`获取商店统计失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetStatsAction;