/**
 * 获取商店信息
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.367Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetInfoAction extends BaseAction {
  static metadata = {
    name: '获取商店信息',
    description: '获取商店信息',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "shopType": {
            "type": "object",
            "required": true,
            "description": "shopType参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "ShopType类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, shopType } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      shopType
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取商店信息成功'
      };
    } else {
      throw new Error(`获取商店信息失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetInfoAction;