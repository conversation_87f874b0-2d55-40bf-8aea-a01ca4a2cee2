/**
 * 获取限时商店信息
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.getLimitShop
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.379Z
 */

const BaseAction = require('../../../core/base-action');

class ShopgetLimitShopAction extends BaseAction {
  static metadata = {
    name: '获取限时商店信息',
    description: '获取限时商店信息',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.getLimitShop',
    prerequisites: ["login","character"],
    params: {
      "limitShopDto": {
            "type": "object",
            "required": true,
            "description": "limitShopDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "activityId": {
                        "type": "number",
                        "required": false,
                        "description": "activityId参数"
                  },
                  "limitType": {
                        "type": "string",
                        "required": false,
                        "description": "limitType参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "activityId": 1,
                  "limitType": "示例limitType"
            }
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { limitShopDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      limitShopDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取限时商店信息成功'
      };
    } else {
      throw new Error(`获取限时商店信息失败: ${response.message}`);
    }
  }
}

module.exports = ShopgetLimitShopAction;