/**
 * 购买月卡
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.buyMonthCard
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.374Z
 */

const BaseAction = require('../../../core/base-action');

class ShopbuyMonthCardAction extends BaseAction {
  static metadata = {
    name: '购买月卡',
    description: '购买月卡',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.buyMonthCard',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "buyDto": {
            "type": "object",
            "required": true,
            "description": "buyDto参数",
            "properties": {
                  "cardType": {
                        "type": "number",
                        "required": true,
                        "description": "cardType参数"
                  },
                  "days": {
                        "type": "number",
                        "required": true,
                        "description": "days参数"
                  },
                  "paymentMethod": {
                        "type": "string",
                        "required": false,
                        "description": "paymentMethod参数"
                  },
                  "orderId": {
                        "type": "string",
                        "required": false,
                        "description": "orderId参数"
                  }
            },
            "example": {
                  "cardType": 1,
                  "days": 1,
                  "paymentMethod": "示例paymentMethod",
                  "orderId": "示例orderId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, buyDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      buyDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买月卡成功'
      };
    } else {
      throw new Error(`购买月卡失败: ${response.message}`);
    }
  }
}

module.exports = ShopbuyMonthCardAction;