/**
 * 确认交易
 * 
 * 微服务: economy
 * 模块: trade
 * Controller: trade
 * Pattern: trade.confirm
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.391Z
 */

const BaseAction = require('../../../core/base-action');

class TradeconfirmAction extends BaseAction {
  static metadata = {
    name: '确认交易',
    description: '确认交易',
    category: 'economy',
    serviceName: 'economy',
    module: 'trade',
    actionName: 'trade.confirm',
    prerequisites: ["login","character"],
    params: {
      "tradeId": {
            "type": "string",
            "required": true,
            "description": "tradeId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { tradeId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      tradeId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '确认交易成功'
      };
    } else {
      throw new Error(`确认交易失败: ${response.message}`);
    }
  }
}

module.exports = TradeconfirmAction;