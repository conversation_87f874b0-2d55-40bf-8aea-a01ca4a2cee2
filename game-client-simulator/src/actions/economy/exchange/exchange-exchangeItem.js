/**
 * 兑换物品 对应old项目中的exchangeItem方法
 * 
 * 微服务: economy
 * 模块: exchange
 * Controller: exchange
 * Pattern: exchange.exchangeItem
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.334Z
 */

const BaseAction = require('../../../core/base-action');

class ExchangeexchangeItemAction extends BaseAction {
  static metadata = {
    name: '兑换物品 对应old项目中的exchangeItem方法',
    description: '兑换物品 对应old项目中的exchangeItem方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'exchange',
    actionName: 'exchange.exchangeItem',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "id": {
            "type": "number",
            "required": true,
            "description": "id参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, id } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      id
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '兑换物品 对应old项目中的exchangeItem方法成功'
      };
    } else {
      throw new Error(`兑换物品 对应old项目中的exchangeItem方法失败: ${response.message}`);
    }
  }
}

module.exports = ExchangeexchangeItemAction;