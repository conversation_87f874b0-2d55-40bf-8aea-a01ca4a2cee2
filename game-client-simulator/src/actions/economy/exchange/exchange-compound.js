/**
 * 合成物品 对应old项目中的compoundItem方法
 * 
 * 微服务: economy
 * 模块: exchange
 * Controller: exchange
 * Pattern: exchange.compound
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.329Z
 */

const BaseAction = require('../../../core/base-action');

class ExchangecompoundAction extends BaseAction {
  static metadata = {
    name: '合成物品 对应old项目中的compoundItem方法',
    description: '合成物品 对应old项目中的compoundItem方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'exchange',
    actionName: 'exchange.compound',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "resId": {
            "type": "number",
            "required": true,
            "description": "resId参数"
      },
      "num": {
            "type": "number",
            "required": true,
            "description": "num参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, resId, num } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      resId,
      num
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '合成物品 对应old项目中的compoundItem方法成功'
      };
    } else {
      throw new Error(`合成物品 对应old项目中的compoundItem方法失败: ${response.message}`);
    }
  }
}

module.exports = ExchangecompoundAction;