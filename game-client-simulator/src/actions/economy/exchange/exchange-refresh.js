/**
 * 刷新兑换大厅 对应old项目中的flushExchangeHall方法
 * 
 * 微服务: economy
 * 模块: exchange
 * Controller: exchange
 * Pattern: exchange.refresh
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.332Z
 */

const BaseAction = require('../../../core/base-action');

class ExchangerefreshAction extends BaseAction {
  static metadata = {
    name: '刷新兑换大厅 对应old项目中的flushExchangeHall方法',
    description: '刷新兑换大厅 对应old项目中的flushExchangeHall方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'exchange',
    actionName: 'exchange.refresh',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "type": {
            "type": "number",
            "required": true,
            "description": "type参数"
      },
      "teamId": {
            "type": "number",
            "required": false,
            "description": "teamId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, type, teamId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      type,
      teamId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '刷新兑换大厅 对应old项目中的flushExchangeHall方法成功'
      };
    } else {
      throw new Error(`刷新兑换大厅 对应old项目中的flushExchangeHall方法失败: ${response.message}`);
    }
  }
}

module.exports = ExchangerefreshAction;