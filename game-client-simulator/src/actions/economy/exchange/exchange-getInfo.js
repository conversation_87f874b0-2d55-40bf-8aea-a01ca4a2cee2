/**
 * 获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法
 * 
 * 微服务: economy
 * 模块: exchange
 * Controller: exchange
 * Pattern: exchange.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.327Z
 */

const BaseAction = require('../../../core/base-action');

class ExchangegetInfoAction extends BaseAction {
  static metadata = {
    name: '获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法',
    description: '获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'exchange',
    actionName: 'exchange.getInfo',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法成功'
      };
    } else {
      throw new Error(`获取兑换大厅信息 对应old项目中的getExchangeHallInfo方法失败: ${response.message}`);
    }
  }
}

module.exports = ExchangegetInfoAction;