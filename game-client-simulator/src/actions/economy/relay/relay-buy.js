/**
 * 购买联赛转播 对应old项目中的buyRelay方法
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.buy
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.350Z
 */

const BaseAction = require('../../../core/base-action');

class RelaybuyAction extends BaseAction {
  static metadata = {
    name: '购买联赛转播 对应old项目中的buyRelay方法',
    description: '购买联赛转播 对应old项目中的buyRelay方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.buy',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买联赛转播 对应old项目中的buyRelay方法成功'
      };
    } else {
      throw new Error(`购买联赛转播 对应old项目中的buyRelay方法失败: ${response.message}`);
    }
  }
}

module.exports = RelaybuyAction;