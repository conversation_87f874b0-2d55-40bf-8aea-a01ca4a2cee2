/**
 * 一键领取所有奖励 对应old项目中的receiveAllAward方法
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.receiveAllAward
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.360Z
 */

const BaseAction = require('../../../core/base-action');

class RelayreceiveAllAwardAction extends BaseAction {
  static metadata = {
    name: '一键领取所有奖励 对应old项目中的receiveAllAward方法',
    description: '一键领取所有奖励 对应old项目中的receiveAllAward方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.receiveAllAward',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '一键领取所有奖励 对应old项目中的receiveAllAward方法成功'
      };
    } else {
      throw new Error(`一键领取所有奖励 对应old项目中的receiveAllAward方法失败: ${response.message}`);
    }
  }
}

module.exports = RelayreceiveAllAwardAction;