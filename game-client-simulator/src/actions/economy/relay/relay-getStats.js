/**
 * 获取转播统计信息（管理接口）
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.366Z
 */

const BaseAction = require('../../../core/base-action');

class RelaygetStatsAction extends BaseAction {
  static metadata = {
    name: '获取转播统计信息（管理接口）',
    description: '获取转播统计信息（管理接口）',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.getStats',
    prerequisites: ["login","character"],
    params: {
      "adminToken": {
            "type": "string",
            "required": false,
            "description": "adminToken参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { adminToken } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      adminToken
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取转播统计信息（管理接口）成功'
      };
    } else {
      throw new Error(`获取转播统计信息（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = RelaygetStatsAction;