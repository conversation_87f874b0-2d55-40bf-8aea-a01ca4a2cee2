/**
 * 处理支付
 * 
 * 微服务: economy
 * 模块: payment
 * Controller: payment
 * Pattern: payment.process
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.345Z
 */

const BaseAction = require('../../../core/base-action');

class PaymentprocessAction extends BaseAction {
  static metadata = {
    name: '处理支付',
    description: '处理支付',
    category: 'economy',
    serviceName: 'economy',
    module: 'payment',
    actionName: 'payment.process',
    prerequisites: ["login","character"],
    params: {
      "data": {
            "type": "any",
            "required": true,
            "description": "data参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { data } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      data
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '处理支付成功'
      };
    } else {
      throw new Error(`处理支付失败: ${response.message}`);
    }
  }
}

module.exports = PaymentprocessAction;