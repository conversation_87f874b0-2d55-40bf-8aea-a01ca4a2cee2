/**
 * 验证支付
 * 
 * 微服务: economy
 * 模块: payment
 * Controller: payment
 * Pattern: payment.verify
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.347Z
 */

const BaseAction = require('../../../core/base-action');

class PaymentverifyAction extends BaseAction {
  static metadata = {
    name: '验证支付',
    description: '验证支付',
    category: 'economy',
    serviceName: 'economy',
    module: 'payment',
    actionName: 'payment.verify',
    prerequisites: ["login","character"],
    params: {
      "transactionId": {
            "type": "string",
            "required": true,
            "description": "transactionId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { transactionId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      transactionId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '验证支付成功'
      };
    } else {
      throw new Error(`验证支付失败: ${response.message}`);
    }
  }
}

module.exports = PaymentverifyAction;