/**
 * 球员抽奖 基于old项目: Player.prototype.lotteryHero
 * 
 * 微服务: economy
 * 模块: lottery
 * Controller: lottery
 * Pattern: lottery.lotteryHero
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.340Z
 */

const BaseAction = require('../../../core/base-action');

class LotterylotteryHeroAction extends BaseAction {
  static metadata = {
    name: '球员抽奖 基于old项目: Player.prototype.lotteryHero',
    description: '球员抽奖 基于old项目: Player.prototype.lotteryHero',
    category: 'economy',
    serviceName: 'economy',
    module: 'lottery',
    actionName: 'lottery.lotteryHero',
    prerequisites: ["login","character"],
    params: {},
    timeout: 10000
  };

  async perform(client, params) {
    // 无参数
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员抽奖 基于old项目: Player.prototype.lotteryHero成功'
      };
    } else {
      throw new Error(`球员抽奖 基于old项目: Player.prototype.lotteryHero失败: ${response.message}`);
    }
  }
}

module.exports = LotterylotteryHeroAction;