/**
 * 获取角色信息
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.215Z
 */

const BaseAction = require('../../../core/base-action');

class CharactergetInfoAction extends BaseAction {
  static metadata = {
    name: '获取角色信息',
    description: '获取角色信息',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.getInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取角色信息成功'
      };
    } else {
      throw new Error(`获取角色信息失败: ${response.message}`);
    }
  }
}

module.exports = CharactergetInfoAction;