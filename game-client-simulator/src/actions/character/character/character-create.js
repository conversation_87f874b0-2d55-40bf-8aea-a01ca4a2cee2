/**
 * 创建新角色
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.create
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.305Z
 */

const BaseAction = require('../../../core/base-action');

class CharactercreateAction extends BaseAction {
  static metadata = {
    name: '创建新角色',
    description: '创建新角色',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.create',
    prerequisites: ["login"],
    params: {
      "createDto": {
            "type": "object",
            "required": true,
            "description": "createDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": false,
                        "description": "characterId参数"
                  },
                  "userId": {
                        "type": "string",
                        "required": true,
                        "description": "userId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": true,
                        "description": "serverId参数"
                  },
                  "openId": {
                        "type": "string",
                        "required": true,
                        "description": "openId参数"
                  },
                  "name": {
                        "type": "string",
                        "required": true,
                        "description": "name参数"
                  },
                  "avatar": {
                        "type": "string",
                        "required": false,
                        "description": "avatar参数"
                  },
                  "faceIcon": {
                        "type": "number",
                        "required": false,
                        "description": "faceIcon参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "userId": "示例userId",
                  "serverId": "示例serverId",
                  "openId": "示例openId",
                  "name": "示例name",
                  "avatar": "示例avatar",
                  "faceIcon": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { createDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      createDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建新角色成功'
      };
    } else {
      throw new Error(`创建新角色失败: ${response.message}`);
    }
  }
}

module.exports = CharactercreateAction;