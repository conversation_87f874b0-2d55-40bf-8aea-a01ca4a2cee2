/**
 * 使用兑换码
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.useRedeemCode
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.227Z
 */

const BaseAction = require('../../../core/base-action');

class CharacteruseRedeemCodeAction extends BaseAction {
  static metadata = {
    name: '使用兑换码',
    description: '使用兑换码',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.useRedeemCode',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "group": {
            "type": "string",
            "required": true,
            "description": "group参数"
      },
      "codeId": {
            "type": "string",
            "required": true,
            "description": "codeId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, group, codeId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      group,
      codeId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '使用兑换码成功'
      };
    } else {
      throw new Error(`使用兑换码失败: ${response.message}`);
    }
  }
}

module.exports = CharacteruseRedeemCodeAction;