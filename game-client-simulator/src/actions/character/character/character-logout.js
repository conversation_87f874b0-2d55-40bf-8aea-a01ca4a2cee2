/**
 * 角色登出
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.logout
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.214Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterlogoutAction extends BaseAction {
  static metadata = {
    name: '角色登出',
    description: '角色登出',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.logout',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '角色登出成功'
      };
    } else {
      throw new Error(`角色登出失败: ${response.message}`);
    }
  }
}

module.exports = CharacterlogoutAction;