/**
 * 接收Auth服务的角色初始化通知
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.initializeFromAuth
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.243Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterinitializeFromAuthAction extends BaseAction {
  static metadata = {
    name: '接收Auth服务的角色初始化通知',
    description: '接收Auth服务的角色初始化通知',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.initializeFromAuth',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "characterName": {
            "type": "string",
            "required": true,
            "description": "characterName参数"
      },
      "initialData": {
            "type": "any",
            "required": false,
            "description": "initialData参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, userId, serverId, characterName, initialData } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      userId,
      serverId,
      characterName,
      initialData
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '接收Auth服务的角色初始化通知成功'
      };
    } else {
      throw new Error(`接收Auth服务的角色初始化通知失败: ${response.message}`);
    }
  }
}

module.exports = CharacterinitializeFromAuthAction;