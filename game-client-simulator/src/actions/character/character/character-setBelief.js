/**
 * 设置角色信仰
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.setBelief
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.225Z
 */

const BaseAction = require('../../../core/base-action');

class CharactersetBeliefAction extends BaseAction {
  static metadata = {
    name: '设置角色信仰',
    description: '设置角色信仰',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.setBelief',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "beliefId": {
            "type": "number",
            "required": true,
            "description": "beliefId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, beliefId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      beliefId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '设置角色信仰成功'
      };
    } else {
      throw new Error(`设置角色信仰失败: ${response.message}`);
    }
  }
}

module.exports = CharactersetBeliefAction;