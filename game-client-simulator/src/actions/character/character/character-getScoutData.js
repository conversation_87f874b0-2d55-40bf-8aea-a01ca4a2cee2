/**
 * 获取角色球探数据 基于old项目Scout实体
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.getScoutData
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.240Z
 */

const BaseAction = require('../../../core/base-action');

class CharactergetScoutDataAction extends BaseAction {
  static metadata = {
    name: '获取角色球探数据 基于old项目Scout实体',
    description: '获取角色球探数据 基于old项目Scout实体',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.getScoutData',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取角色球探数据 基于old项目Scout实体成功'
      };
    } else {
      throw new Error(`获取角色球探数据 基于old项目Scout实体失败: ${response.message}`);
    }
  }
}

module.exports = CharactergetScoutDataAction;