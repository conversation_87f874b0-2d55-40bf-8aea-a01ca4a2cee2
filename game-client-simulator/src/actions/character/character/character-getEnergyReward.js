/**
 * 获取体力奖励 对应old项目: game.player.getEnergyReward
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.getEnergyReward
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.236Z
 */

const BaseAction = require('../../../core/base-action');

class CharactergetEnergyRewardAction extends BaseAction {
  static metadata = {
    name: '获取体力奖励 对应old项目: game.player.getEnergyReward',
    description: '获取体力奖励 对应old项目: game.player.getEnergyReward',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.getEnergyReward',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "type": {
            "type": "number",
            "required": true,
            "description": "type参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, type, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      type,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取体力奖励 对应old项目: game.player.getEnergyReward成功'
      };
    } else {
      throw new Error(`获取体力奖励 对应old项目: game.player.getEnergyReward失败: ${response.message}`);
    }
  }
}

module.exports = CharactergetEnergyRewardAction;