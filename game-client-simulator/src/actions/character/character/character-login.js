/**
 * 角色登录
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.login
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.212Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterloginAction extends BaseAction {
  static metadata = {
    name: '角色登录',
    description: '角色登录',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.login',
    prerequisites: ["login","character"],
    params: {
      "loginDto": {
            "type": "object",
            "required": true,
            "description": "loginDto参数",
            "properties": {
                  "userId": {
                        "type": "string",
                        "required": true,
                        "description": "userId参数"
                  },
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": true,
                        "description": "serverId参数"
                  },
                  "ip": {
                        "type": "string",
                        "required": false,
                        "description": "ip参数"
                  },
                  "frontendId": {
                        "type": "string",
                        "required": false,
                        "description": "frontendId参数"
                  },
                  "sessionId": {
                        "type": "string",
                        "required": false,
                        "description": "sessionId参数"
                  }
            },
            "example": {
                  "userId": "示例userId",
                  "characterId": "示例characterId",
                  "serverId": "示例serverId",
                  "ip": "示例ip",
                  "frontendId": "示例frontendId",
                  "sessionId": "示例sessionId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { loginDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      loginDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '角色登录成功'
      };
    } else {
      throw new Error(`角色登录失败: ${response.message}`);
    }
  }
}

module.exports = CharacterloginAction;