/**
 * 消耗金币任务 对应old项目: game.player.costCashTask
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.costCashTask
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.238Z
 */

const BaseAction = require('../../../core/base-action');

class CharactercostCashTaskAction extends BaseAction {
  static metadata = {
    name: '消耗金币任务 对应old项目: game.player.costCashTask',
    description: '消耗金币任务 对应old项目: game.player.costCashTask',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.costCashTask',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "amount": {
            "type": "number",
            "required": true,
            "description": "amount参数"
      },
      "reason": {
            "type": "string",
            "required": false,
            "description": "reason参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, amount, reason, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      amount,
      reason,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '消耗金币任务 对应old项目: game.player.costCashTask成功'
      };
    } else {
      throw new Error(`消耗金币任务 对应old项目: game.player.costCashTask失败: ${response.message}`);
    }
  }
}

module.exports = CharactercostCashTaskAction;