/**
 * 更新角色信息
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.update
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.216Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterupdateAction extends BaseAction {
  static metadata = {
    name: '更新角色信息',
    description: '更新角色信息',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.update',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "updateDto": {
            "type": "object",
            "required": true,
            "description": "updateDto参数",
            "properties": {
                  "name": {
                        "type": "string",
                        "required": false,
                        "description": "name参数"
                  },
                  "avatar": {
                        "type": "string",
                        "required": false,
                        "description": "avatar参数"
                  },
                  "faceIcon": {
                        "type": "number",
                        "required": false,
                        "description": "faceIcon参数"
                  },
                  "faceUrl": {
                        "type": "string",
                        "required": false,
                        "description": "faceUrl参数"
                  },
                  "clubFaceIcon": {
                        "type": "array",
                        "required": false,
                        "description": "clubFaceIcon参数"
                  },
                  "countryFaceIcon": {
                        "type": "array",
                        "required": false,
                        "description": "countryFaceIcon参数"
                  },
                  "isShowTactics": {
                        "type": "number",
                        "required": false,
                        "description": "isShowTactics参数"
                  }
            },
            "example": {
                  "name": "示例name",
                  "avatar": "示例avatar",
                  "faceIcon": 1,
                  "faceUrl": "示例faceUrl",
                  "clubFaceIcon": [],
                  "countryFaceIcon": [],
                  "isShowTactics": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, updateDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      updateDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新角色信息成功'
      };
    } else {
      throw new Error(`更新角色信息失败: ${response.message}`);
    }
  }
}

module.exports = CharacterupdateAction;