/**
 * 根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.searchByName
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.229Z
 */

const BaseAction = require('../../../core/base-action');

class CharactersearchByNameAction extends BaseAction {
  static metadata = {
    name: '根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索',
    description: '根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.searchByName',
    prerequisites: ["login","character"],
    params: {
      "name": {
            "type": "string",
            "required": true,
            "description": "name参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { name, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      name,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索成功'
      };
    } else {
      throw new Error(`根据名称搜索角色 基于old项目: accountService.searchPlayerName 用于商业赛等功能的对手搜索失败: ${response.message}`);
    }
  }
}

module.exports = CharactersearchByNameAction;