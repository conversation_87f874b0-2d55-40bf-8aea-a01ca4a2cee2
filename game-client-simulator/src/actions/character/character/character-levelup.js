/**
 * 角色升级
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.levelup
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.221Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterlevelupAction extends BaseAction {
  static metadata = {
    name: '角色升级',
    description: '角色升级',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.levelup',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "levelUpDto": {
            "type": "object",
            "required": true,
            "description": "levelUpDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "targetLevel": {
                        "type": "number",
                        "required": false,
                        "description": "targetLevel参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "targetLevel": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, levelUpDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      levelUpDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '角色升级成功'
      };
    } else {
      throw new Error(`角色升级失败: ${response.message}`);
    }
  }
}

module.exports = CharacterlevelupAction;