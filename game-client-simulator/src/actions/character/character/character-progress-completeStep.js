/**
 * 完成创角步骤
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.progress.completeStep
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.223Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterprogresscompleteStepAction extends BaseAction {
  static metadata = {
    name: '完成创角步骤',
    description: '完成创角步骤',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.progress.completeStep',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "step": {
            "type": "number",
            "required": true,
            "description": "step参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, step, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      step,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '完成创角步骤成功'
      };
    } else {
      throw new Error(`完成创角步骤失败: ${response.message}`);
    }
  }
}

module.exports = CharacterprogresscompleteStepAction;