/**
 * 修改玩家名称 对应old项目: game.player.modifyPlayerName
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.modifyCharacterName
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.234Z
 */

const BaseAction = require('../../../core/base-action');

class CharactermodifyCharacterNameAction extends BaseAction {
  static metadata = {
    name: '修改玩家名称 对应old项目: game.player.modifyPlayerName',
    description: '修改玩家名称 对应old项目: game.player.modifyPlayerName',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.modifyCharacterName',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "name": {
            "type": "string",
            "required": true,
            "description": "name参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, name, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      name,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '修改玩家名称 对应old项目: game.player.modifyPlayerName成功'
      };
    } else {
      throw new Error(`修改玩家名称 对应old项目: game.player.modifyPlayerName失败: ${response.message}`);
    }
  }
}

module.exports = CharactermodifyCharacterNameAction;