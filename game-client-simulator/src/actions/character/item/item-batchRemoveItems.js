/**
 * 批量移除物品 对应old项目的批量删除逻辑，优化API命名
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.batchRemoveItems
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.294Z
 */

const BaseAction = require('../../../core/base-action');

class ItembatchRemoveItemsAction extends BaseAction {
  static metadata = {
    name: '批量移除物品 对应old项目的批量删除逻辑，优化API命名',
    description: '批量移除物品 对应old项目的批量删除逻辑，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.batchRemoveItems',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "items": {
            "type": "array",
            "required": true,
            "description": "items参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, items, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      items,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量移除物品 对应old项目的批量删除逻辑，优化API命名成功'
      };
    } else {
      throw new Error(`批量移除物品 对应old项目的批量删除逻辑，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = ItembatchRemoveItemsAction;