/**
 * 添加物品 对应old项目: addItem方法
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.addItem
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.280Z
 */

const BaseAction = require('../../../core/base-action');

class ItemaddItemAction extends BaseAction {
  static metadata = {
    name: '添加物品 对应old项目: addItem方法',
    description: '添加物品 对应old项目: addItem方法',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.addItem',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "resId": {
            "type": "number",
            "required": true,
            "description": "resId参数"
      },
      "num": {
            "type": "number",
            "required": true,
            "description": "num参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, resId, num, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      resId,
      num,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加物品 对应old项目: addItem方法成功'
      };
    } else {
      throw new Error(`添加物品 对应old项目: addItem方法失败: ${response.message}`);
    }
  }
}

module.exports = ItemaddItemAction;