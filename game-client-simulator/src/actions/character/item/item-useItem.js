/**
 * 使用物品 对应old项目: useItem方法
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.useItem
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.283Z
 */

const BaseAction = require('../../../core/base-action');

class ItemuseItemAction extends BaseAction {
  static metadata = {
    name: '使用物品 对应old项目: useItem方法',
    description: '使用物品 对应old项目: useItem方法',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.useItem',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "itemId": {
            "type": "string",
            "required": true,
            "description": "itemId参数"
      },
      "quantity": {
            "type": "number",
            "required": false,
            "description": "quantity参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, itemId, quantity, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      itemId,
      quantity,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '使用物品 对应old项目: useItem方法成功'
      };
    } else {
      throw new Error(`使用物品 对应old项目: useItem方法失败: ${response.message}`);
    }
  }
}

module.exports = ItemuseItemAction;