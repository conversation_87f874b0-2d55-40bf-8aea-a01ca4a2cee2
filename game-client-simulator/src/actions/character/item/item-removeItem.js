/**
 * 移除物品 对应old项目: delItem方法，优化API命名
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.removeItem
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.282Z
 */

const BaseAction = require('../../../core/base-action');

class ItemremoveItemAction extends BaseAction {
  static metadata = {
    name: '移除物品 对应old项目: delItem方法，优化API命名',
    description: '移除物品 对应old项目: delItem方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.removeItem',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "itemId": {
            "type": "string",
            "required": true,
            "description": "itemId参数"
      },
      "quantity": {
            "type": "number",
            "required": true,
            "description": "quantity参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, itemId, quantity, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      itemId,
      quantity,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '移除物品 对应old项目: delItem方法，优化API命名成功'
      };
    } else {
      throw new Error(`移除物品 对应old项目: delItem方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = ItemremoveItemAction;