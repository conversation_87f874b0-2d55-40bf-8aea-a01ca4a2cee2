/**
 * 获取角色物品数据 对应old项目: Item实体的toJSONforDB
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.getItems
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.279Z
 */

const BaseAction = require('../../../core/base-action');

class ItemgetItemsAction extends BaseAction {
  static metadata = {
    name: '获取角色物品数据 对应old项目: Item实体的toJSONforDB',
    description: '获取角色物品数据 对应old项目: Item实体的toJSONforDB',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.getItems',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取角色物品数据 对应old项目: Item实体的toJSONforDB成功'
      };
    } else {
      throw new Error(`获取角色物品数据 对应old项目: Item实体的toJSONforDB失败: ${response.message}`);
    }
  }
}

module.exports = ItemgetItemsAction;