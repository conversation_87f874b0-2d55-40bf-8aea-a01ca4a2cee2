/**
 * 扩展背包容量 对应old项目: expandBag方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.expandBag
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.265Z
 */

const BaseAction = require('../../../core/base-action');

class InventoryexpandBagAction extends BaseAction {
  static metadata = {
    name: '扩展背包容量 对应old项目: expandBag方法',
    description: '扩展背包容量 对应old项目: expandBag方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.expandBag',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "expandCount": {
            "type": "number",
            "required": false,
            "description": "expandCount参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, expandCount, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      expandCount,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '扩展背包容量 对应old项目: expandBag方法成功'
      };
    } else {
      throw new Error(`扩展背包容量 对应old项目: expandBag方法失败: ${response.message}`);
    }
  }
}

module.exports = InventoryexpandBagAction;