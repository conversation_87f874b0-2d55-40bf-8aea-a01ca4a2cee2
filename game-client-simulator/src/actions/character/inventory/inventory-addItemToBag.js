/**
 * 添加物品到背包 对应old项目: addToBag方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.addItemToBag
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.362Z
 */

const BaseAction = require('../../../core/base-action');

class InventoryaddItemToBagAction extends BaseAction {
  static metadata = {
    name: '添加物品到背包 对应old项目: addToBag方法',
    description: '添加物品到背包 对应old项目: addToBag方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.addItemToBag',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "itemId": {
            "type": "string",
            "required": true,
            "description": "itemId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, itemId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      itemId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加物品到背包 对应old项目: addToBag方法成功'
      };
    } else {
      throw new Error(`添加物品到背包 对应old项目: addToBag方法失败: ${response.message}`);
    }
  }
}

module.exports = InventoryaddItemToBagAction;