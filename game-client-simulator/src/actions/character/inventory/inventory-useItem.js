/**
 * 使用物品 对应old项目: useItemMainType方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.useItem
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.269Z
 */

const BaseAction = require('../../../core/base-action');

class InventoryuseItemAction extends BaseAction {
  static metadata = {
    name: '使用物品 对应old项目: useItemMainType方法',
    description: '使用物品 对应old项目: useItemMainType方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.useItem',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "itemId": {
            "type": "string",
            "required": true,
            "description": "itemId参数"
      },
      "configId": {
            "type": "number",
            "required": true,
            "description": "configId参数"
      },
      "useType": {
            "type": "number",
            "required": true,
            "description": "useType参数"
      },
      "subType": {
            "type": "number",
            "required": true,
            "description": "subType参数"
      },
      "quantity": {
            "type": "number",
            "required": false,
            "description": "quantity参数"
      },
      "heroId": {
            "type": "string",
            "required": false,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, itemId, configId, useType, subType, quantity, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      itemId,
      configId,
      useType,
      subType,
      quantity,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '使用物品 对应old项目: useItemMainType方法成功'
      };
    } else {
      throw new Error(`使用物品 对应old项目: useItemMainType方法失败: ${response.message}`);
    }
  }
}

module.exports = InventoryuseItemAction;