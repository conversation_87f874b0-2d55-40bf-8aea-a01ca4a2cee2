/**
 * 查找物品在背包中的位置 对应old项目: findItemInBag方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.findItemInBag
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.273Z
 */

const BaseAction = require('../../../core/base-action');

class InventoryfindItemInBagAction extends BaseAction {
  static metadata = {
    name: '查找物品在背包中的位置 对应old项目: findItemInBag方法',
    description: '查找物品在背包中的位置 对应old项目: findItemInBag方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.findItemInBag',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "itemId": {
            "type": "string",
            "required": true,
            "description": "itemId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, itemId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      itemId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '查找物品在背包中的位置 对应old项目: findItemInBag方法成功'
      };
    } else {
      throw new Error(`查找物品在背包中的位置 对应old项目: findItemInBag方法失败: ${response.message}`);
    }
  }
}

module.exports = InventoryfindItemInBagAction;