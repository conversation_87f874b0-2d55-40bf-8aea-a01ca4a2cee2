/**
 * 计算扩展费用 对应old项目: calculateExpandCost方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.calculateExpandCost
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.275Z
 */

const BaseAction = require('../../../core/base-action');

class InventorycalculateExpandCostAction extends BaseAction {
  static metadata = {
    name: '计算扩展费用 对应old项目: calculateExpandCost方法',
    description: '计算扩展费用 对应old项目: calculateExpandCost方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.calculateExpandCost',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "expandCount": {
            "type": "number",
            "required": false,
            "description": "expandCount参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, expandCount, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      expandCount,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '计算扩展费用 对应old项目: calculateExpandCost方法成功'
      };
    } else {
      throw new Error(`计算扩展费用 对应old项目: calculateExpandCost方法失败: ${response.message}`);
    }
  }
}

module.exports = InventorycalculateExpandCostAction;