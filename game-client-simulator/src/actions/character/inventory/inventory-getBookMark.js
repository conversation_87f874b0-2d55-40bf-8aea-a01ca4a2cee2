/**
 * 获取指定页签数据 对应old项目: getOneBookMark方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.getBookMark
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.261Z
 */

const BaseAction = require('../../../core/base-action');

class InventorygetBookMarkAction extends BaseAction {
  static metadata = {
    name: '获取指定页签数据 对应old项目: getOneBookMark方法',
    description: '获取指定页签数据 对应old项目: getOneBookMark方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.getBookMark',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取指定页签数据 对应old项目: getOneBookMark方法成功'
      };
    } else {
      throw new Error(`获取指定页签数据 对应old项目: getOneBookMark方法失败: ${response.message}`);
    }
  }
}

module.exports = InventorygetBookMarkAction;