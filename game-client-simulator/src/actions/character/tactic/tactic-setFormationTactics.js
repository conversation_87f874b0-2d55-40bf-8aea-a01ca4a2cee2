/**
 * 设置阵容战术 对应old项目: setFormationTactics方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.setFormationTactics
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.310Z
 */

const BaseAction = require('../../../core/base-action');

class TacticsetFormationTacticsAction extends BaseAction {
  static metadata = {
    name: '设置阵容战术 对应old项目: setFormationTactics方法',
    description: '设置阵容战术 对应old项目: setFormationTactics方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.setFormationTactics',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "attackTacticId": {
            "type": "string",
            "required": true,
            "description": "attackTacticId参数"
      },
      "defenseTacticId": {
            "type": "string",
            "required": true,
            "description": "defenseTacticId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, formationId, attackTacticId, defenseTacticId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      formationId,
      attackTacticId,
      defenseTacticId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '设置阵容战术 对应old项目: setFormationTactics方法成功'
      };
    } else {
      throw new Error(`设置阵容战术 对应old项目: setFormationTactics方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticsetFormationTacticsAction;