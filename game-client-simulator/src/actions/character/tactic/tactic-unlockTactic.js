/**
 * 解锁战术 对应old项目: unlockTactic方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.unlockTactic
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.307Z
 */

const BaseAction = require('../../../core/base-action');

class TacticunlockTacticAction extends BaseAction {
  static metadata = {
    name: '解锁战术 对应old项目: unlockTactic方法',
    description: '解锁战术 对应old项目: unlockTactic方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.unlockTactic',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "tacticKey": {
            "type": "string",
            "required": true,
            "description": "tacticKey参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, tacticKey, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      tacticKey,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '解锁战术 对应old项目: unlockTactic方法成功'
      };
    } else {
      throw new Error(`解锁战术 对应old项目: unlockTactic方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticunlockTacticAction;