/**
 * 计算球员战术加成 对应old项目: calcHeroTacticsAttr方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.calculateHeroBonus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.315Z
 */

const BaseAction = require('../../../core/base-action');

class TacticcalculateHeroBonusAction extends BaseAction {
  static metadata = {
    name: '计算球员战术加成 对应old项目: calcHeroTacticsAttr方法',
    description: '计算球员战术加成 对应old项目: calcHeroTacticsAttr方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.calculateHeroBonus',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, heroId, formationId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      heroId,
      formationId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '计算球员战术加成 对应old项目: calcHeroTacticsAttr方法成功'
      };
    } else {
      throw new Error(`计算球员战术加成 对应old项目: calcHeroTacticsAttr方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticcalculateHeroBonusAction;