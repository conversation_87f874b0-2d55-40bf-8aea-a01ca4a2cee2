/**
 * 更新战术使用统计 对应old项目: updateTacticUsageStats方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.updateTacticUsage
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.316Z
 */

const BaseAction = require('../../../core/base-action');

class TacticupdateTacticUsageAction extends BaseAction {
  static metadata = {
    name: '更新战术使用统计 对应old项目: updateTacticUsageStats方法',
    description: '更新战术使用统计 对应old项目: updateTacticUsageStats方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.updateTacticUsage',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "tacticKey": {
            "type": "string",
            "required": true,
            "description": "tacticKey参数"
      },
      "isWin": {
            "type": "boolean",
            "required": true,
            "description": "isWin参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, tacticKey, isWin, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      tacticKey,
      isWin,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新战术使用统计 对应old项目: updateTacticUsageStats方法成功'
      };
    } else {
      throw new Error(`更新战术使用统计 对应old项目: updateTacticUsageStats方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticupdateTacticUsageAction;