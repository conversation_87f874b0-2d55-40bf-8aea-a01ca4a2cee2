/**
 * 自动布阵 对应old项目: autoFormation方法
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.autoFormation
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.254Z
 */

const BaseAction = require('../../../core/base-action');

class FormationautoFormationAction extends BaseAction {
  static metadata = {
    name: '自动布阵 对应old项目: autoFormation方法',
    description: '自动布阵 对应old项目: autoFormation方法',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.autoFormation',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "heroIds": {
            "type": "array",
            "required": false,
            "description": "heroIds参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, formationId, heroIds, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      formationId,
      heroIds,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '自动布阵 对应old项目: autoFormation方法成功'
      };
    } else {
      throw new Error(`自动布阵 对应old项目: autoFormation方法失败: ${response.message}`);
    }
  }
}

module.exports = FormationautoFormationAction;