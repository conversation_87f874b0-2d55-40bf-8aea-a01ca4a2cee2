/**
 * 创建阵容 对应old项目: newTeamFormation方法，优化API命名
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.createFormation
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.245Z
 */

const BaseAction = require('../../../core/base-action');

class FormationcreateFormationAction extends BaseAction {
  static metadata = {
    name: '创建阵容 对应old项目: newTeamFormation方法，优化API命名',
    description: '创建阵容 对应old项目: newTeamFormation方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.createFormation',
    prerequisites: ["login"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "resId": {
            "type": "number",
            "required": true,
            "description": "resId参数"
      },
      "type": {
            "type": "number",
            "required": false,
            "description": "type参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, resId, type, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      resId,
      type,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建阵容 对应old项目: newTeamFormation方法，优化API命名成功'
      };
    } else {
      throw new Error(`创建阵容 对应old项目: newTeamFormation方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = FormationcreateFormationAction;