/**
 * 添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.addHeroToPosition
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.246Z
 */

const BaseAction = require('../../../core/base-action');

class FormationaddHeroToPositionAction extends BaseAction {
  static metadata = {
    name: '添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名',
    description: '添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.addHeroToPosition',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "position": {
            "type": "string",
            "required": true,
            "description": "position参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, formationId, position, index, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      formationId,
      position,
      index,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名成功'
      };
    } else {
      throw new Error(`添加球员到阵容位置 对应old项目: addHeroInTeam方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = FormationaddHeroToPositionAction;