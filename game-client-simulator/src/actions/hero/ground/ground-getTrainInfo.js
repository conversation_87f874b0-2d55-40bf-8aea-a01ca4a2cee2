/**
 * 获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.getTrainInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.416Z
 */

const BaseAction = require('../../../core/base-action');

class GroundgetTrainInfoAction extends BaseAction {
  static metadata = {
    name: '获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo',
    description: '获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.getTrainInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo成功'
      };
    } else {
      throw new Error(`获取场地训练信息 对应old项目: game.groundService.getHeroTrainInfo失败: ${response.message}`);
    }
  }
}

module.exports = GroundgetTrainInfoAction;