/**
 * 名人堂入驻 对应old项目: game.groundService.inputNotablePos
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.inputNotable
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.423Z
 */

const BaseAction = require('../../../core/base-action');

class GroundinputNotableAction extends BaseAction {
  static metadata = {
    name: '名人堂入驻 对应old项目: game.groundService.inputNotablePos',
    description: '名人堂入驻 对应old项目: game.groundService.inputNotablePos',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.inputNotable',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '名人堂入驻 对应old项目: game.groundService.inputNotablePos成功'
      };
    } else {
      throw new Error(`名人堂入驻 对应old项目: game.groundService.inputNotablePos失败: ${response.message}`);
    }
  }
}

module.exports = GroundinputNotableAction;