/**
 * 场地训练 对应old项目: game.groundService.heroTrainInGround
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.train
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.418Z
 */

const BaseAction = require('../../../core/base-action');

class GroundtrainAction extends BaseAction {
  static metadata = {
    name: '场地训练 对应old项目: game.groundService.heroTrainInGround',
    description: '场地训练 对应old项目: game.groundService.heroTrainInGround',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.train',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "type": {
            "type": "number",
            "required": true,
            "description": "type参数"
      },
      "isLock": {
            "type": "boolean",
            "required": false,
            "description": "isLock参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, heroId, index, type, isLock, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      heroId,
      index,
      type,
      isLock,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '场地训练 对应old项目: game.groundService.heroTrainInGround成功'
      };
    } else {
      throw new Error(`场地训练 对应old项目: game.groundService.heroTrainInGround失败: ${response.message}`);
    }
  }
}

module.exports = GroundtrainAction;