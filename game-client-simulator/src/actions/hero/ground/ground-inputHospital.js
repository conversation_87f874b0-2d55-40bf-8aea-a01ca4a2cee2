/**
 * 医疗中心治疗 对应old项目: game.groundService.inputHospitalPos
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.inputHospital
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.431Z
 */

const BaseAction = require('../../../core/base-action');

class GroundinputHospitalAction extends BaseAction {
  static metadata = {
    name: '医疗中心治疗 对应old项目: game.groundService.inputHospitalPos',
    description: '医疗中心治疗 对应old项目: game.groundService.inputHospitalPos',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.inputHospital',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, heroId, index, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      heroId,
      index,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '医疗中心治疗 对应old项目: game.groundService.inputHospitalPos成功'
      };
    } else {
      throw new Error(`医疗中心治疗 对应old项目: game.groundService.inputHospitalPos失败: ${response.message}`);
    }
  }
}

module.exports = GroundinputHospitalAction;