/**
 * 激活球员技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.activate
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.515Z
 */

const BaseAction = require('../../../core/base-action');

class SkillactivateAction extends BaseAction {
  static metadata = {
    name: '激活球员技能',
    description: '激活球员技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.activate',
    prerequisites: ["login","character"],
    params: {
      "activateDto": {
            "type": "object",
            "required": true,
            "description": "activateDto参数",
            "properties": {
                  "skillId": {
                        "type": "string",
                        "required": true,
                        "description": "skillId参数"
                  },
                  "slotPosition": {
                        "type": "number",
                        "required": true,
                        "description": "slotPosition参数"
                  }
            },
            "example": {
                  "skillId": "示例skillId",
                  "slotPosition": 1
            }
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { activateDto, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      activateDto,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '激活球员技能成功'
      };
    } else {
      throw new Error(`激活球员技能失败: ${response.message}`);
    }
  }
}

module.exports = SkillactivateAction;