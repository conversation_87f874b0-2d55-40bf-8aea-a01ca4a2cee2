/**
 * 获取技能统计
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.527Z
 */

const BaseAction = require('../../../core/base-action');

class SkillgetStatsAction extends BaseAction {
  static metadata = {
    name: '获取技能统计',
    description: '获取技能统计',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.getStats',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取技能统计成功'
      };
    } else {
      throw new Error(`获取技能统计失败: ${response.message}`);
    }
  }
}

module.exports = SkillgetStatsAction;