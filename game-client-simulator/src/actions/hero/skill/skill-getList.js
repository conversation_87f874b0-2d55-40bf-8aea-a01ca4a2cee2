/**
 * 获取球员技能列表
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.517Z
 */

const BaseAction = require('../../../core/base-action');

class SkillgetListAction extends BaseAction {
  static metadata = {
    name: '获取球员技能列表',
    description: '获取球员技能列表',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.getList',
    prerequisites: ["login","character"],
    params: {
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "activeStatus": {
                        "type": "object",
                        "required": false,
                        "description": "activeStatus参数"
                  },
                  "equippedOnly": {
                        "type": "boolean",
                        "required": false,
                        "description": "equippedOnly参数"
                  },
                  "includeConfig": {
                        "type": "boolean",
                        "required": false,
                        "description": "includeConfig参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "activeStatus": {},
                  "equippedOnly": true,
                  "includeConfig": true
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { query, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      query,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球员技能列表成功'
      };
    } else {
      throw new Error(`获取球员技能列表失败: ${response.message}`);
    }
  }
}

module.exports = SkillgetListAction;