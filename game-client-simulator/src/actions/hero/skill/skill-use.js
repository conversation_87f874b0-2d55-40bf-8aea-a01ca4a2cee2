/**
 * 使用技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.use
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.530Z
 */

const BaseAction = require('../../../core/base-action');

class SkilluseAction extends BaseAction {
  static metadata = {
    name: '使用技能',
    description: '使用技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.use',
    prerequisites: ["login","character"],
    params: {
      "skillId": {
            "type": "string",
            "required": true,
            "description": "skillId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "targetId": {
            "type": "string",
            "required": false,
            "description": "targetId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { skillId, heroId, targetId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      skillId,
      heroId,
      targetId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '使用技能成功'
      };
    } else {
      throw new Error(`使用技能失败: ${response.message}`);
    }
  }
}

module.exports = SkilluseAction;