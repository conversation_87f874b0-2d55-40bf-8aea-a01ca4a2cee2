/**
 * 获取技能配置列表
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.getConfigList
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.509Z
 */

const BaseAction = require('../../../core/base-action');

class SkillgetConfigListAction extends BaseAction {
  static metadata = {
    name: '获取技能配置列表',
    description: '获取技能配置列表',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.getConfigList',
    prerequisites: ["login","character"],
    params: {
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "type": {
                        "type": "object",
                        "required": false,
                        "description": "type参数"
                  },
                  "position": {
                        "type": "object",
                        "required": false,
                        "description": "position参数"
                  },
                  "skillRank": {
                        "type": "string",
                        "required": false,
                        "description": "skillRank参数"
                  },
                  "skillType": {
                        "type": "number",
                        "required": false,
                        "description": "skillType参数"
                  },
                  "minStarValue": {
                        "type": "number",
                        "required": false,
                        "description": "minStarValue参数"
                  },
                  "maxStarValue": {
                        "type": "number",
                        "required": false,
                        "description": "maxStarValue参数"
                  },
                  "rarity": {
                        "type": "number",
                        "required": false,
                        "description": "rarity参数"
                  },
                  "name": {
                        "type": "string",
                        "required": false,
                        "description": "name参数"
                  },
                  "availableOnly": {
                        "type": "boolean",
                        "required": false,
                        "description": "availableOnly参数"
                  },
                  "sortBy": {
                        "type": "object",
                        "required": false,
                        "description": "sortBy参数"
                  },
                  "sortOrder": {
                        "type": "object",
                        "required": false,
                        "description": "sortOrder参数"
                  },
                  "page": {
                        "type": "number",
                        "required": false,
                        "description": "page参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  }
            },
            "example": {
                  "type": {},
                  "position": {},
                  "skillRank": "示例skillRank",
                  "skillType": 1,
                  "minStarValue": 1,
                  "maxStarValue": 1,
                  "rarity": 1,
                  "name": "示例name",
                  "availableOnly": true,
                  "sortBy": {},
                  "sortOrder": {},
                  "page": 1,
                  "limit": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取技能配置列表成功'
      };
    } else {
      throw new Error(`获取技能配置列表失败: ${response.message}`);
    }
  }
}

module.exports = SkillgetConfigListAction;