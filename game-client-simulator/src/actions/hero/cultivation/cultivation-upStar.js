/**
 * 球员升星 对应old项目: heroUpStar
 * 
 * 微服务: hero
 * 模块: cultivation
 * Controller: cultivation
 * Pattern: cultivation.upStar
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.413Z
 */

const BaseAction = require('../../../core/base-action');

class CultivationupStarAction extends BaseAction {
  static metadata = {
    name: '球员升星 对应old项目: heroUpStar',
    description: '球员升星 对应old项目: heroUpStar',
    category: 'hero',
    serviceName: 'hero',
    module: 'cultivation',
    actionName: 'cultivation.upStar',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员升星 对应old项目: heroUpStar成功'
      };
    } else {
      throw new Error(`球员升星 对应old项目: heroUpStar失败: ${response.message}`);
    }
  }
}

module.exports = CultivationupStarAction;