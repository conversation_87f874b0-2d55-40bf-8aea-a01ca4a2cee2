/**
 * 一键养成 对应old项目: oneKeyCultivateHero
 * 
 * 微服务: hero
 * 模块: cultivation
 * Controller: cultivation
 * Pattern: cultivation.oneKeyCultivate
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.415Z
 */

const BaseAction = require('../../../core/base-action');

class CultivationoneKeyCultivateAction extends BaseAction {
  static metadata = {
    name: '一键养成 对应old项目: oneKeyCultivateHero',
    description: '一键养成 对应old项目: oneKeyCultivateHero',
    category: 'hero',
    serviceName: 'hero',
    module: 'cultivation',
    actionName: 'cultivation.oneKeyCultivate',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "type": {
            "type": "number",
            "required": true,
            "description": "type参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, type, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      type,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '一键养成 对应old项目: oneKeyCultivateHero成功'
      };
    } else {
      throw new Error(`一键养成 对应old项目: oneKeyCultivateHero失败: ${response.message}`);
    }
  }
}

module.exports = CultivationoneKeyCultivateAction;