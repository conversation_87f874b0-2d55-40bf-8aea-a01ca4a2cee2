/**
 * 球员养成 对应old项目: cultivateHero
 * 
 * 微服务: hero
 * 模块: cultivation
 * Controller: cultivation
 * Pattern: cultivation.cultivate
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.410Z
 */

const BaseAction = require('../../../core/base-action');

class CultivationcultivateAction extends BaseAction {
  static metadata = {
    name: '球员养成 对应old项目: cultivateHero',
    description: '球员养成 对应old项目: cultivateHero',
    category: 'hero',
    serviceName: 'hero',
    module: 'cultivation',
    actionName: 'cultivation.cultivate',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员养成 对应old项目: cultivateHero成功'
      };
    } else {
      throw new Error(`球员养成 对应old项目: cultivateHero失败: ${response.message}`);
    }
  }
}

module.exports = CultivationcultivateAction;