/**
 * 获取球员特训信息 对应old项目: getTrainInfo
 * 
 * 微服务: hero
 * 模块: training
 * Controller: training
 * Pattern: training.getTrainInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.533Z
 */

const BaseAction = require('../../../core/base-action');

class TraininggetTrainInfoAction extends BaseAction {
  static metadata = {
    name: '获取球员特训信息 对应old项目: getTrainInfo',
    description: '获取球员特训信息 对应old项目: getTrainInfo',
    category: 'hero',
    serviceName: 'hero',
    module: 'training',
    actionName: 'training.getTrainInfo',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球员特训信息 对应old项目: getTrainInfo成功'
      };
    } else {
      throw new Error(`获取球员特训信息 对应old项目: getTrainInfo失败: ${response.message}`);
    }
  }
}

module.exports = TraininggetTrainInfoAction;