/**
 * 设置球员训练状态 从hero模块迁移而来
 * 
 * 微服务: hero
 * 模块: training
 * Controller: training
 * Pattern: training.setTrainStatus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.538Z
 */

const BaseAction = require('../../../core/base-action');

class TrainingsetTrainStatusAction extends BaseAction {
  static metadata = {
    name: '设置球员训练状态 从hero模块迁移而来',
    description: '设置球员训练状态 从hero模块迁移而来',
    category: 'hero',
    serviceName: 'hero',
    module: 'training',
    actionName: 'training.setTrainStatus',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "isTrain": {
            "type": "boolean",
            "required": true,
            "description": "isTrain参数"
      },
      "isLockTrain": {
            "type": "boolean",
            "required": false,
            "description": "isLockTrain参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, isTrain, isLockTrain } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      isTrain,
      isLockTrain
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '设置球员训练状态 从hero模块迁移而来成功'
      };
    } else {
      throw new Error(`设置球员训练状态 从hero模块迁移而来失败: ${response.message}`);
    }
  }
}

module.exports = TrainingsetTrainStatusAction;