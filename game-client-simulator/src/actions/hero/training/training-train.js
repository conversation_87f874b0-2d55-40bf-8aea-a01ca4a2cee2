/**
 * 训练球员 对应old项目: trainHero
 * 
 * 微服务: hero
 * 模块: training
 * Controller: training
 * Pattern: training.train
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.534Z
 */

const BaseAction = require('../../../core/base-action');

class TrainingtrainAction extends BaseAction {
  static metadata = {
    name: '训练球员 对应old项目: trainHero',
    description: '训练球员 对应old项目: trainHero',
    category: 'hero',
    serviceName: 'hero',
    module: 'training',
    actionName: 'training.train',
    prerequisites: ["login","character"],
    params: {
      "trainDto": {
            "type": "object",
            "required": true,
            "description": "trainDto参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "trainingType": {
                        "type": "object",
                        "required": true,
                        "description": "trainingType参数"
                  },
                  "trainingMethod": {
                        "type": "object",
                        "required": true,
                        "description": "trainingMethod参数"
                  },
                  "targetAttributes": {
                        "type": "array",
                        "required": false,
                        "description": "targetAttributes参数"
                  },
                  "count": {
                        "type": "number",
                        "required": false,
                        "description": "count参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "trainingType": {},
                  "trainingMethod": {},
                  "targetAttributes": [],
                  "count": 1,
                  "serverId": "示例serverId"
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { trainDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      trainDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '训练球员 对应old项目: trainHero成功'
      };
    } else {
      throw new Error(`训练球员 对应old项目: trainHero失败: ${response.message}`);
    }
  }
}

module.exports = TrainingtrainAction;