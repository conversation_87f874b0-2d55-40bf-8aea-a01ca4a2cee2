/**
 * 替换特训 对应old项目: replaceHeroTrain
 * 
 * 微服务: hero
 * 模块: training
 * Controller: training
 * Pattern: training.replaceHeroTrain
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.537Z
 */

const BaseAction = require('../../../core/base-action');

class TrainingreplaceHeroTrainAction extends BaseAction {
  static metadata = {
    name: '替换特训 对应old项目: replaceHeroTrain',
    description: '替换特训 对应old项目: replaceHeroTrain',
    category: 'hero',
    serviceName: 'hero',
    module: 'training',
    actionName: 'training.replaceHeroTrain',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, index, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      index,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '替换特训 对应old项目: replaceHeroTrain成功'
      };
    } else {
      throw new Error(`替换特训 对应old项目: replaceHeroTrain失败: ${response.message}`);
    }
  }
}

module.exports = TrainingreplaceHeroTrainAction;