/**
 * 球探搜索 基于old项目扩展的球探搜索功能
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.search
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.502Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutsearchAction extends BaseAction {
  static metadata = {
    name: '球探搜索 基于old项目扩展的球探搜索功能',
    description: '球探搜索 基于old项目扩展的球探搜索功能',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.search',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "scoutType": {
            "type": "string",
            "required": true,
            "description": "scoutType参数"
      },
      "targetQuality": {
            "type": "string",
            "required": false,
            "description": "targetQuality参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, scoutType, targetQuality, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      scoutType,
      targetQuality,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球探搜索 基于old项目扩展的球探搜索功能成功'
      };
    } else {
      throw new Error(`球探搜索 基于old项目扩展的球探搜索功能失败: ${response.message}`);
    }
  }
}

module.exports = ScoutsearchAction;