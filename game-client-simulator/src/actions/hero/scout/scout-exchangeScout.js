/**
 * 球探RP值兑换 基于old项目Scout.exchangeScout方法
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.exchangeScout
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.503Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutexchangeScoutAction extends BaseAction {
  static metadata = {
    name: '球探RP值兑换 基于old项目Scout.exchangeScout方法',
    description: '球探RP值兑换 基于old项目Scout.exchangeScout方法',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.exchangeScout',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "type": {
            "type": "number",
            "required": false,
            "description": "type参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, type, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      type,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球探RP值兑换 基于old项目Scout.exchangeScout方法成功'
      };
    } else {
      throw new Error(`球探RP值兑换 基于old项目Scout.exchangeScout方法失败: ${response.message}`);
    }
  }
}

module.exports = ScoutexchangeScoutAction;