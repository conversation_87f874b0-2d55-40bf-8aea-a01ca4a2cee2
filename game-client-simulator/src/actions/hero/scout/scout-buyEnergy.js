/**
 * 购买球探体力 基于old项目的体力购买机制
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.buyEnergy
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.505Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutbuyEnergyAction extends BaseAction {
  static metadata = {
    name: '购买球探体力 基于old项目的体力购买机制',
    description: '购买球探体力 基于old项目的体力购买机制',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.buyEnergy',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "amount": {
            "type": "number",
            "required": false,
            "description": "amount参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, amount, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      amount,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买球探体力 基于old项目的体力购买机制成功'
      };
    } else {
      throw new Error(`购买球探体力 基于old项目的体力购买机制失败: ${response.message}`);
    }
  }
}

module.exports = ScoutbuyEnergyAction;