/**
 * 球探探索（核心功能） 对应old项目: getScoutReward
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.explore
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.500Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutexploreAction extends BaseAction {
  static metadata = {
    name: '球探探索（核心功能） 对应old项目: getScoutReward',
    description: '球探探索（核心功能） 对应old项目: getScoutReward',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.explore',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "type": {
            "type": "number",
            "required": true,
            "description": "type参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, type, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      type,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球探探索（核心功能） 对应old项目: getScoutReward成功'
      };
    } else {
      throw new Error(`球探探索（核心功能） 对应old项目: getScoutReward失败: ${response.message}`);
    }
  }
}

module.exports = ScoutexploreAction;