/**
 * 删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.deletePackHero
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.498Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutdeletePackHeroAction extends BaseAction {
  static metadata = {
    name: '删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）',
    description: '删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.deletePackHero',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "index": {
            "type": "array",
            "required": true,
            "description": "index参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, index, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      index,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）成功'
      };
    } else {
      throw new Error(`删除球探包中的球员 对应old项目: delScoutPackHero（支持批量删除）失败: ${response.message}`);
    }
  }
}

module.exports = ScoutdeletePackHeroAction;