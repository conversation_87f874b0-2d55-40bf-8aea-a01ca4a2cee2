/**
 * 获取球探包信息 对应old项目: getScoutPackInfo
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.getPackInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.496Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutgetPackInfoAction extends BaseAction {
  static metadata = {
    name: '获取球探包信息 对应old项目: getScoutPackInfo',
    description: '获取球探包信息 对应old项目: getScoutPackInfo',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.getPackInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球探包信息 对应old项目: getScoutPackInfo成功'
      };
    } else {
      throw new Error(`获取球探包信息 对应old项目: getScoutPackInfo失败: ${response.message}`);
    }
  }
}

module.exports = ScoutgetPackInfoAction;