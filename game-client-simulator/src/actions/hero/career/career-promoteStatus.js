/**
 * 提升球员状态 对应old项目: promoteHeroStatus（使用道具）
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.promoteStatus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.399Z
 */

const BaseAction = require('../../../core/base-action');

class CareerpromoteStatusAction extends BaseAction {
  static metadata = {
    name: '提升球员状态 对应old项目: promoteHeroStatus（使用道具）',
    description: '提升球员状态 对应old项目: promoteHeroStatus（使用道具）',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.promoteStatus',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "itemId": {
            "type": "number",
            "required": true,
            "description": "itemId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, itemId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      itemId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '提升球员状态 对应old项目: promoteHeroStatus（使用道具）成功'
      };
    } else {
      throw new Error(`提升球员状态 对应old项目: promoteHeroStatus（使用道具）失败: ${response.message}`);
    }
  }
}

module.exports = CareerpromoteStatusAction;