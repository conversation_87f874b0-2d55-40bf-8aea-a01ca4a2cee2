/**
 * 处理球员退役 检查并处理到期的球员
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.processRetirement
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.405Z
 */

const BaseAction = require('../../../core/base-action');

class CareerprocessRetirementAction extends BaseAction {
  static metadata = {
    name: '处理球员退役 检查并处理到期的球员',
    description: '处理球员退役 检查并处理到期的球员',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.processRetirement',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '处理球员退役 检查并处理到期的球员成功'
      };
    } else {
      throw new Error(`处理球员退役 检查并处理到期的球员失败: ${response.message}`);
    }
  }
}

module.exports = CareerprocessRetirementAction;