/**
 * 加入退役名单 对应old项目: addHeroToRetirementList
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.addToRetirement
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.400Z
 */

const BaseAction = require('../../../core/base-action');

class CareeraddToRetirementAction extends BaseAction {
  static metadata = {
    name: '加入退役名单 对应old项目: addHeroToRetirementList',
    description: '加入退役名单 对应old项目: addHeroToRetirementList',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.addToRetirement',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '加入退役名单 对应old项目: addHeroToRetirementList成功'
      };
    } else {
      throw new Error(`加入退役名单 对应old项目: addHeroToRetirementList失败: ${response.message}`);
    }
  }
}

module.exports = CareeraddToRetirementAction;