/**
 * 获取即将到期的球员列表
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.getExpiringContracts
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.407Z
 */

const BaseAction = require('../../../core/base-action');

class CareergetExpiringContractsAction extends BaseAction {
  static metadata = {
    name: '获取即将到期的球员列表',
    description: '获取即将到期的球员列表',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.getExpiringContracts',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "days": {
            "type": "number",
            "required": true,
            "description": "days参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, days, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      days,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取即将到期的球员列表成功'
      };
    } else {
      throw new Error(`获取即将到期的球员列表失败: ${response.message}`);
    }
  }
}

module.exports = CareergetExpiringContractsAction;