/**
 * 市场操作
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.market.operation
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.454Z
 */

const BaseAction = require('../../../core/base-action');

class HeromarketoperationAction extends BaseAction {
  static metadata = {
    name: '市场操作',
    description: '市场操作',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.market.operation',
    prerequisites: ["login","character"],
    params: {
      "marketDto": {
            "type": "object",
            "required": true,
            "description": "marketDto参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "operation": {
                        "type": "object",
                        "required": true,
                        "description": "operation参数"
                  },
                  "price": {
                        "type": "number",
                        "required": false,
                        "description": "price参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "operation": {},
                  "price": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { marketDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      marketDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '市场操作成功'
      };
    } else {
      throw new Error(`市场操作失败: ${response.message}`);
    }
  }
}

module.exports = HeromarketoperationAction;