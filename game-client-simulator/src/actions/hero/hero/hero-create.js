/**
 * 创建新球员
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.create
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.442Z
 */

const BaseAction = require('../../../core/base-action');

class HerocreateAction extends BaseAction {
  static metadata = {
    name: '创建新球员',
    description: '创建新球员',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.create',
    prerequisites: ["login"],
    params: {
      "createDto": {
            "type": "object",
            "required": true,
            "description": "createDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": true,
                        "description": "serverId参数"
                  },
                  "resId": {
                        "type": "number",
                        "required": true,
                        "description": "resId参数"
                  },
                  "name": {
                        "type": "string",
                        "required": true,
                        "description": "name参数"
                  },
                  "position": {
                        "type": "object",
                        "required": true,
                        "description": "position参数"
                  },
                  "quality": {
                        "type": "object",
                        "required": true,
                        "description": "quality参数"
                  },
                  "level": {
                        "type": "number",
                        "required": false,
                        "description": "level参数"
                  },
                  "nationality": {
                        "type": "string",
                        "required": false,
                        "description": "nationality参数"
                  },
                  "club": {
                        "type": "string",
                        "required": false,
                        "description": "club参数"
                  },
                  "obtainType": {
                        "type": "number",
                        "required": false,
                        "description": "obtainType参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId",
                  "resId": 1,
                  "name": "示例name",
                  "position": {},
                  "quality": {},
                  "level": 1,
                  "nationality": "示例nationality",
                  "club": "示例club",
                  "obtainType": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { createDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      createDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建新球员成功'
      };
    } else {
      throw new Error(`创建新球员失败: ${response.message}`);
    }
  }
}

module.exports = HerocreateAction;