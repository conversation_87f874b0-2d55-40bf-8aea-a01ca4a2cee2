/**
 * 球员升星
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.evolve
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.464Z
 */

const BaseAction = require('../../../core/base-action');

class HeroevolveAction extends BaseAction {
  static metadata = {
    name: '球员升星',
    description: '球员升星',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.evolve',
    prerequisites: ["login","character"],
    params: {
      "evolveDto": {
            "type": "object",
            "required": true,
            "description": "evolveDto参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "materialHeroIds": {
                        "type": "array",
                        "required": true,
                        "description": "materialHeroIds参数"
                  },
                  "useProtection": {
                        "type": "boolean",
                        "required": false,
                        "description": "useProtection参数"
                  },
                  "useUniversalCard": {
                        "type": "boolean",
                        "required": false,
                        "description": "useUniversalCard参数"
                  },
                  "items": {
                        "type": "array",
                        "required": false,
                        "description": "items参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "materialHeroIds": [],
                  "useProtection": true,
                  "useUniversalCard": true,
                  "items": [],
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { evolveDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      evolveDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员升星成功'
      };
    } else {
      throw new Error(`球员升星失败: ${response.message}`);
    }
  }
}

module.exports = HeroevolveAction;