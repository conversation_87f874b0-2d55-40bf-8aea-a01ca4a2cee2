/**
 * 设置球员治疗状态
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.setTreatStatus
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.440Z
 */

const BaseAction = require('../../../core/base-action');

class HerosetTreatStatusAction extends BaseAction {
  static metadata = {
    name: '设置球员治疗状态',
    description: '设置球员治疗状态',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.setTreatStatus',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "isTreat": {
            "type": "boolean",
            "required": true,
            "description": "isTreat参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, isTreat } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      isTreat
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '设置球员治疗状态成功'
      };
    } else {
      throw new Error(`设置球员治疗状态失败: ${response.message}`);
    }
  }
}

module.exports = HerosetTreatStatusAction;