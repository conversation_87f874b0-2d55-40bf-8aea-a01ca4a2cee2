/**
 * 更新球员信息
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.update
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.446Z
 */

const BaseAction = require('../../../core/base-action');

class HeroupdateAction extends BaseAction {
  static metadata = {
    name: '更新球员信息',
    description: '更新球员信息',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.update',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "updateDto": {
            "type": "object",
            "required": true,
            "description": "updateDto参数",
            "properties": {
                  "name": {
                        "type": "string",
                        "required": false,
                        "description": "name参数"
                  },
                  "isLocked": {
                        "type": "boolean",
                        "required": false,
                        "description": "isLocked参数"
                  },
                  "equipments": {
                        "type": "array",
                        "required": false,
                        "description": "equipments参数"
                  },
                  "contractDays": {
                        "type": "number",
                        "required": false,
                        "description": "contractDays参数"
                  },
                  "salary": {
                        "type": "number",
                        "required": false,
                        "description": "salary参数"
                  }
            },
            "example": {
                  "name": "示例name",
                  "isLocked": true,
                  "equipments": [],
                  "contractDays": 1,
                  "salary": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, updateDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      updateDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新球员信息成功'
      };
    } else {
      throw new Error(`更新球员信息失败: ${response.message}`);
    }
  }
}

module.exports = HeroupdateAction;