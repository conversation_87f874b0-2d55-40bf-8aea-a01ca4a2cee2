/**
 * 获取升星需求
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.evolution.requirements
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.462Z
 */

const BaseAction = require('../../../core/base-action');

class HeroevolutionrequirementsAction extends BaseAction {
  static metadata = {
    name: '获取升星需求',
    description: '获取升星需求',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.evolution.requirements',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取升星需求成功'
      };
    } else {
      throw new Error(`获取升星需求失败: ${response.message}`);
    }
  }
}

module.exports = HeroevolutionrequirementsAction;