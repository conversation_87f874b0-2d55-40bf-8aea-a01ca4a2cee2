/**
 * 获取球员列表
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.448Z
 */

const BaseAction = require('../../../core/base-action');

class HerogetListAction extends BaseAction {
  static metadata = {
    name: '获取球员列表',
    description: '获取球员列表',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.getList',
    prerequisites: ["login","character"],
    params: {
      "query": {
            "type": "object",
            "required": true,
            "description": "query参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": false,
                        "description": "characterId参数"
                  },
                  "position": {
                        "type": "object",
                        "required": false,
                        "description": "position参数"
                  },
                  "quality": {
                        "type": "object",
                        "required": false,
                        "description": "quality参数"
                  },
                  "isInFormation": {
                        "type": "boolean",
                        "required": false,
                        "description": "isInFormation参数"
                  },
                  "isOnMarket": {
                        "type": "boolean",
                        "required": false,
                        "description": "isOnMarket参数"
                  },
                  "minLevel": {
                        "type": "number",
                        "required": false,
                        "description": "minLevel参数"
                  },
                  "maxLevel": {
                        "type": "number",
                        "required": false,
                        "description": "maxLevel参数"
                  },
                  "sortBy": {
                        "type": "object",
                        "required": false,
                        "description": "sortBy参数"
                  },
                  "sortOrder": {
                        "type": "object",
                        "required": false,
                        "description": "sortOrder参数"
                  },
                  "page": {
                        "type": "number",
                        "required": false,
                        "description": "page参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "position": {},
                  "quality": {},
                  "isInFormation": true,
                  "isOnMarket": true,
                  "minLevel": 1,
                  "maxLevel": 1,
                  "sortBy": {},
                  "sortOrder": {},
                  "page": 1,
                  "limit": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { query } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      query
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球员列表成功'
      };
    } else {
      throw new Error(`获取球员列表失败: ${response.message}`);
    }
  }
}

module.exports = HerogetListAction;