/**
 * 获取市场球员
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.getMarket
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.459Z
 */

const BaseAction = require('../../../core/base-action');

class HerogetMarketAction extends BaseAction {
  static metadata = {
    name: '获取市场球员',
    description: '获取市场球员',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.getMarket',
    prerequisites: ["login","character"],
    params: {
      "page": {
            "type": "number",
            "required": false,
            "description": "page参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { page, limit, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      page,
      limit,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取市场球员成功'
      };
    } else {
      throw new Error(`获取市场球员失败: ${response.message}`);
    }
  }
}

module.exports = HerogetMarketAction;