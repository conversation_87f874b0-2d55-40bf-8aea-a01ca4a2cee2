/**
 * 恢复球员疲劳
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.fatigue.recover
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.471Z
 */

const BaseAction = require('../../../core/base-action');

class HerofatiguerecoverAction extends BaseAction {
  static metadata = {
    name: '恢复球员疲劳',
    description: '恢复球员疲劳',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.fatigue.recover',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "recoveryValue": {
            "type": "number",
            "required": false,
            "description": "recoveryValue参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, recoveryValue, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      recoveryValue,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '恢复球员疲劳成功'
      };
    } else {
      throw new Error(`恢复球员疲劳失败: ${response.message}`);
    }
  }
}

module.exports = HerofatiguerecoverAction;