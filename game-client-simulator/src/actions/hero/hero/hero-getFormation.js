/**
 * 获取阵容中的球员
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.getFormation
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.457Z
 */

const BaseAction = require('../../../core/base-action');

class HerogetFormationAction extends BaseAction {
  static metadata = {
    name: '获取阵容中的球员',
    description: '获取阵容中的球员',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.getFormation',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取阵容中的球员成功'
      };
    } else {
      throw new Error(`获取阵容中的球员失败: ${response.message}`);
    }
  }
}

module.exports = HerogetFormationAction;