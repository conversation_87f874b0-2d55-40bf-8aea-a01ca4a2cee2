/**
 * 球员续约
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.contract.renew
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.469Z
 */

const BaseAction = require('../../../core/base-action');

class HerocontractrenewAction extends BaseAction {
  static metadata = {
    name: '球员续约',
    description: '球员续约',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.contract.renew',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "contractDays": {
            "type": "number",
            "required": true,
            "description": "contractDays参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, contractDays, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      contractDays,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员续约成功'
      };
    } else {
      throw new Error(`球员续约失败: ${response.message}`);
    }
  }
}

module.exports = HerocontractrenewAction;