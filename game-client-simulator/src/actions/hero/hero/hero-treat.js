/**
 * 治疗球员
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.treat
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.545Z
 */

const BaseAction = require('../../../core/base-action');

class HerotreatAction extends BaseAction {
  static metadata = {
    name: '治疗球员',
    description: '治疗球员',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.treat',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "treatmentType": {
            "type": "string",
            "required": false,
            "description": "treatmentType参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, treatmentType, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      treatmentType,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '治疗球员成功'
      };
    } else {
      throw new Error(`治疗球员失败: ${response.message}`);
    }
  }
}

module.exports = HerotreatAction;