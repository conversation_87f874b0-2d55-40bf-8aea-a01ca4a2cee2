/**
 * 更新球员疲劳值
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.updateFatigue
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.441Z
 */

const BaseAction = require('../../../core/base-action');

class HeroupdateFatigueAction extends BaseAction {
  static metadata = {
    name: '更新球员疲劳值',
    description: '更新球员疲劳值',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.updateFatigue',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "fatigueChange": {
            "type": "number",
            "required": true,
            "description": "fatigueChange参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, fatigueChange } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      fatigueChange
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新球员疲劳值成功'
      };
    } else {
      throw new Error(`更新球员疲劳值失败: ${response.message}`);
    }
  }
}

module.exports = HeroupdateFatigueAction;