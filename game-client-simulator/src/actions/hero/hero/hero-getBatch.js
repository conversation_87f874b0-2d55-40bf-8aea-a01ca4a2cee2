/**
 * 批量获取球员信息
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.getBatch
 * 
 * 使用AST自动生成于 2025-08-16T13:03:08.444Z
 */

const BaseAction = require('../../../core/base-action');

class HerogetBatchAction extends BaseAction {
  static metadata = {
    name: '批量获取球员信息',
    description: '批量获取球员信息',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.getBatch',
    prerequisites: ["login","character"],
    params: {
      "heroIds": {
            "type": "array",
            "required": true,
            "description": "heroIds参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroIds, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroIds,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量获取球员信息成功'
      };
    } else {
      throw new Error(`批量获取球员信息失败: ${response.message}`);
    }
  }
}

module.exports = HerogetBatchAction;