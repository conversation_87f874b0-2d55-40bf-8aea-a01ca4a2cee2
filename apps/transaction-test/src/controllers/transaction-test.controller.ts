import { Controller, Post, Get, Delete, Body, Param } from '@nestjs/common';
import { MicroserviceResponse, MicroserviceResponseUtils } from '@libs/common/types';
import { TransactionTestService, TransferResult } from '../services/transaction-test.service';
import { TestAccountDocument } from '../schemas/test-account.schema';

@Controller('transaction-test')
export class TransactionTestController {
  constructor(
    private readonly transactionTestService: TransactionTestService
  ) {}

  /**
   * 测试成功的转账事务
   */
  @Post('successful-transfer')
  async testSuccessfulTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<MicroserviceResponse<TransferResult>> {
    const result = await this.transactionTestService.testSuccessfulTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );

    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 测试失败的转账事务
   */
  @MessagePattern('transaction-test.failedTransfer')
  async testFailedTransfer(@Payload() payload: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<MicroserviceResponse<TransferResult>> {
    const result = await this.transactionTestService.testFailedTransfer(
      payload.fromUsername,
      payload.toUsername,
      payload.amount
    );
    
    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 创建测试账户
   */
  @MessagePattern('transaction-test.createAccounts')
  async createTestAccounts(): Promise<MicroserviceResponse<TestAccountDocument[]>> {
    const result = await this.transactionTestService.createTestAccounts();
    
    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 查询账户余额
   */
  @MessagePattern('transaction-test.getBalance')
  async getAccountBalance(@Payload() payload: {
    username: string;
  }): Promise<MicroserviceResponse<{ username: string; balance: number }>> {
    const result = await this.transactionTestService.getAccountBalance(payload.username);
    
    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 清理测试数据
   */
  @MessagePattern('transaction-test.cleanup')
  async cleanupTestData(): Promise<MicroserviceResponse<void>> {
    const result = await this.transactionTestService.cleanupTestData();
    
    return MicroserviceResponseUtils.fromResult(result);
  }
}
