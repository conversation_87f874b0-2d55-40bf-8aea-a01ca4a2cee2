import { Controller, Post, Get, Delete, Body, Param } from '@nestjs/common';
import { Result } from '@libs/common/types';
import { TransactionTestService, TransferResult, ComplexPurchaseResult } from '../services/transaction-test.service';
import { TestAccountDocument } from '../schemas/test-account.schema';
import { TestQuestProgressDocument } from '../schemas/test-quest.schema';
import {TestItemDocument} from "../schemas/test-item.schema";

@Controller('transaction-test')
export class TransactionTestController {
  constructor(
    private readonly transactionTestService: TransactionTestService
  ) {}

  /**
   * 测试成功的转账事务
   */
  @Post('successful-transfer')
  async testSuccessfulTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<Result<TransferResult>> {
    return await this.transactionTestService.testSuccessfulTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 测试失败的转账事务
   */
  @Post('failed-transfer')
  async testFailedTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<Result<TransferResult>> {
    return await this.transactionTestService.testFailedTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 创建测试账户
   */
  @Post('create-accounts')
  async createTestAccounts(@Body() body?: {
    usernameA?: string;
    usernameB?: string;
  }): Promise<Result<TestAccountDocument[]>> {
    return await this.transactionTestService.createTestAccounts(
      body?.usernameA,
      body?.usernameB
    );
  }

  /**
   * 查询账户余额
   */
  @Get('balance/:username')
  async getAccountBalance(@Param('username') username: string): Promise<Result<{ username: string; balance: number }>> {
    return await this.transactionTestService.getAccountBalance(username);
  }

  /**
   * 查询账户道具
   */
  @Get('items/:username/:itemId')
  async getAccountItems(
    @Param('username') username: string,
    @Param('itemId') itemId: string
  ): Promise<Result<TestItemDocument>> {
    return await this.transactionTestService.getAccountItem(username,itemId);
  }

  /**
   * 查询任务进度
   */
  @Get('quest/:username/:questId')
  async getQuestProgress(
    @Param('username') username: string,
    @Param('questId') questId: string
  ): Promise<Result<TestQuestProgressDocument | null>> {
    return await this.transactionTestService.getAccountQuest(username, questId);
  }

  /**
   * 复杂事务回滚测试
   */
  @Post('complex-rollback-test')
  async testComplexTransactionRollback(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<Result<{ message: string; rollbackVerified: boolean }>> {
    return await this.transactionTestService.testComplexTransactionRollback(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 初始化购买任务
   */
  @Post('init-purchase-quest')
  async initializePurchaseQuest(@Body() body: {
    username: string;
    questId?: string;
    targetPurchases?: number;
  }): Promise<Result<TestQuestProgressDocument>> {
    return await this.transactionTestService.initializePurchaseQuest(
      body.username,
      body.questId,
      body.targetPurchases
    );
  }

  /**
   * 复杂购买道具事务测试
   */
  @Post('complex-purchase')
  async testComplexItemPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
    questId?: string;
  }): Promise<Result<ComplexPurchaseResult>> {
    return await this.transactionTestService.testComplexItemPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice,
      body.questId
    );
  }

  /**
   * 故意失败的复杂购买事务测试
   */
  @Post('failed-complex-purchase')
  async testFailedComplexPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
  }): Promise<Result<ComplexPurchaseResult>> {
    return await this.transactionTestService.testFailedComplexPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice
    );
  }

  /**
   * 清理测试数据
   */
  @Delete('cleanup')
  async cleanupTestData(): Promise<Result<void>> {
    return await this.transactionTestService.cleanupTestData();
  }
}
