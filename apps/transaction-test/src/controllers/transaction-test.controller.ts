import { Controller, Post, Get, Delete, Body, Param } from '@nestjs/common';
import { XResult } from '@libs/common/types';
import { TransactionTestService, TransferResult, ComplexPurchaseResult } from '../services/transaction-test.service';
import { TestAccountDocument } from '../schemas/test-account.schema';
import { TestQuestProgressDocument } from '../schemas/test-quest.schema';
import {TestItemDocument} from "../schemas/test-item.schema";

@Controller('transaction-test')
export class TransactionTestController {
  constructor(
    private readonly transactionTestService: TransactionTestService
  ) {}

  /**
   * 测试成功的转账事务
   */
  @Post('successful-transfer')
  async testSuccessfulTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<TransferResult>> {
    return await this.transactionTestService.testSuccessfulTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 测试失败的转账事务
   */
  @Post('failed-transfer')
  async testFailedTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<TransferResult>> {
    return await this.transactionTestService.testFailedTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 创建测试账户
   */
  @Post('create-accounts')
  async createTestAccounts(@Body() body?: {
    usernameA?: string;
    usernameB?: string;
  }): Promise<XResult<TestAccountDocument[]>> {
    return await this.transactionTestService.createTestAccounts(
      body?.usernameA,
      body?.usernameB
    );
  }

  /**
   * 查询账户余额
   */
  @Get('balance/:username')
  async getAccountBalance(@Param('username') username: string): Promise<XResult<{ username: string; balance: number }>> {
    return await this.transactionTestService.getAccountBalance(username);
  }

  /**
   * 查询账户道具
   */
  @Get('item/:username/:itemId')
  async getAccountItem(
    @Param('username') username: string,
    @Param('itemId') itemId: string
  ): Promise<XResult<TestItemDocument>> {
    return await this.transactionTestService.getAccountItem(username,itemId);
  }

  @Get('items/:username')
  async getAccountItems(
    @Param('username') username: string
  ): Promise<XResult<TestItemDocument[]>> {
    return await this.transactionTestService.getAccountItems(username);
  }

  /**
   * 查询任务进度
   */
  @Get('quest/:username/:questId')
  async getQuestProgress(
    @Param('username') username: string,
    @Param('questId') questId: string
  ): Promise<XResult<TestQuestProgressDocument | null>> {
    return await this.transactionTestService.getAccountQuest(username, questId);
  }

  /**
   * 复杂事务回滚测试
   */
  @Post('complex-rollback-test')
  async testComplexTransactionRollback(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<XResult<{ message: string; rollbackVerified: boolean }>> {
    return await this.transactionTestService.testComplexTransactionRollback(
      body.fromUsername,
      body.toUsername,
      body.amount
    );
  }

  /**
   * 初始化购买任务
   */
  @Post('init-purchase-quest')
  async initializePurchaseQuest(@Body() body: {
    username: string;
    questId?: string;
    targetPurchases?: number;
  }): Promise<XResult<TestQuestProgressDocument>> {
    return await this.transactionTestService.initializePurchaseQuest(
      body.username,
      body.questId,
      body.targetPurchases
    );
  }

  /**
   * 复杂购买道具事务测试
   */
  @Post('complex-purchase')
  async testComplexItemPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
    questId?: string;
  }): Promise<XResult<ComplexPurchaseResult>> {
    return await this.transactionTestService.testComplexItemPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice,
      body.questId
    );
  }

  /**
   * 故意失败的复杂购买事务测试
   */
  @Post('failed-complex-purchase')
  async testFailedComplexPurchase(@Body() body: {
    username: string;
    itemId: string;
    itemName: string;
    itemPrice: number;
  }): Promise<XResult<ComplexPurchaseResult>> {
    return await this.transactionTestService.testFailedComplexPurchase(
      body.username,
      body.itemId,
      body.itemName,
      body.itemPrice
    );
  }

  /**
   * 清理测试数据
   */
  @Delete('cleanup')
  async cleanupTestData(): Promise<XResult<void>> {
    return await this.transactionTestService.cleanupTestData();
  }
}
