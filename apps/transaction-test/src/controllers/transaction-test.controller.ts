import { Controller, Post, Get, Delete, Body, Param } from '@nestjs/common';
import { Result } from '@libs/common/types';
import { TransactionTestService, TransferResult } from '../services/transaction-test.service';
import { TestAccountDocument } from '../schemas/test-account.schema';

@Controller('transaction-test')
export class TransactionTestController {
  constructor(
    private readonly transactionTestService: TransactionTestService
  ) {}

  /**
   * 测试成功的转账事务
   */
  @Post('successful-transfer')
  async testSuccessfulTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<MicroserviceResponse<TransferResult>> {
    const result = await this.transactionTestService.testSuccessfulTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );

    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 测试失败的转账事务
   */
  @Post('failed-transfer')
  async testFailedTransfer(@Body() body: {
    fromUsername: string;
    toUsername: string;
    amount: number;
  }): Promise<MicroserviceResponse<TransferResult>> {
    const result = await this.transactionTestService.testFailedTransfer(
      body.fromUsername,
      body.toUsername,
      body.amount
    );

    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 创建测试账户
   */
  @Post('create-accounts')
  async createTestAccounts(): Promise<MicroserviceResponse<TestAccountDocument[]>> {
    const result = await this.transactionTestService.createTestAccounts();

    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 查询账户余额
   */
  @Get('balance/:username')
  async getAccountBalance(@Param('username') username: string): Promise<MicroserviceResponse<{ username: string; balance: number }>> {
    const result = await this.transactionTestService.getAccountBalance(username);

    return MicroserviceResponseUtils.fromResult(result);
  }

  /**
   * 清理测试数据
   */
  @Delete('cleanup')
  async cleanupTestData(): Promise<MicroserviceResponse<void>> {
    const result = await this.transactionTestService.cleanupTestData();

    return MicroserviceResponseUtils.fromResult(result);
  }
}
