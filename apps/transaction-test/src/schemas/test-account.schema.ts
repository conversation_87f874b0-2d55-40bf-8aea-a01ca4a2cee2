import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TestAccountDocument = TestAccount & Document;

@Schema({ timestamps: true })
export class TestAccount {
  @Prop({ required: true, unique: true })
  username: string;

  @Prop({ required: true, default: 1000 })
  balance: number;

  @Prop({ default: 0 })
  version: number;
}

export const TestAccountSchema = SchemaFactory.createForClass(TestAccount);

// ==================== 新增：道具Schema ====================

export type TestItemDocument = TestItem & Document;

@Schema({ timestamps: true })
export class TestItem {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  itemId: string;

  @Prop({ required: true })
  itemName: string;

  @Prop({ required: true, default: 1 })
  quantity: number;

  @Prop({ required: true })
  purchasePrice: number;

  @Prop({ default: Date.now })
  purchaseTime: Date;
}

export const TestItemSchema = SchemaFactory.createForClass(TestItem);

// ==================== 新增：任务进度Schema ====================

export type TestQuestProgressDocument = TestQuestProgress & Document;

@Schema({ timestamps: true })
export class TestQuestProgress {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  questId: string;

  @Prop({ required: true })
  questName: string;

  @Prop({ required: true, default: 0 })
  currentProgress: number;

  @Prop({ required: true })
  targetProgress: number;

  @Prop({ default: false })
  completed: boolean;

  @Prop()
  completedAt?: Date;

  @Prop({ default: 0 })
  rewardClaimed: number; // 已领取的奖励金额
}

export const TestQuestProgressSchema = SchemaFactory.createForClass(TestQuestProgress);
