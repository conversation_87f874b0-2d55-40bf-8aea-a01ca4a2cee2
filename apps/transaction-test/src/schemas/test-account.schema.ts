import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TestAccountDocument = TestAccount & Document;

@Schema({ timestamps: true })
export class TestAccount {
  @Prop({ required: true, unique: true })
  username: string;

  @Prop({ required: true, default: 1000 })
  balance: number;

  @Prop({ default: 0 })
  version: number;
}

export const TestAccountSchema = SchemaFactory.createForClass(TestAccount);
