import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TestQuestProgressDocument = TestQuestProgress & Document;

/**
 * 测试任务进度Schema
 * 用于验证事务中的任务进度更新
 */
@Schema({ timestamps: true })
export class TestQuestProgress {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  questId: string;

  @Prop({ required: true })
  questName: string;

  @Prop({ required: true, default: 0 })
  currentProgress: number;

  @Prop({ required: true })
  targetProgress: number;

  @Prop({ default: false })
  completed: boolean;

  @Prop()
  completedAt?: Date;

  @Prop({ default: 0 })
  rewardClaimed: number; // 已领取的奖励金额

  @Prop({ default: 0 })
  version: number;
}

export const TestQuestProgressSchema = SchemaFactory.createForClass(TestQuestProgress);

// 创建复合索引
TestQuestProgressSchema.index({ userId: 1, questId: 1 }, { unique: true });
