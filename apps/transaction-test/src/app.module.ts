import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TestAccountModule } from './modules/test-account.module';
import {ConfigModule} from "@nestjs/config";

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      envFilePath: [
        '.env',
      ],
    }),
    // MongoDB连接
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/transaction-test'),
    
    // 测试模块
    TestAccountModule,
  ],
})
export class AppModule {}
