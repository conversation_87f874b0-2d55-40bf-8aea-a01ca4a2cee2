import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { TestAccount, TestAccountDocument } from '../schemas/test-account.schema';
import { Result } from '@libs/common/types';

@Injectable()
export class TestAccountRepository extends BaseRepository<TestAccountDocument> {
  constructor(
    @InjectModel(TestAccount.name) testAccountModel: Model<TestAccountDocument>
  ) {
    super(testAccountModel, 'TestAccountRepository');
  }

  /**
   * 根据用户名查找账户
   */
  async findByUsername(
    username: string, 
    session?: ClientSession
  ): Promise<Result<TestAccountDocument | null>> {
    return this.findOne({ username }, session);
  }

  /**
   * 转账操作（扣除余额）
   */
  async deductBalance(
    username: string,
    amount: number,
    session?: ClientSession
  ): Promise<Result<TestAccountDocument | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findOneAndUpdate(
        { username, balance: { $gte: amount } }, // 确保余额足够
        { $inc: { balance: -amount, version: 1 } },
        options
      ).exec();
    })(session);
  }

  /**
   * 转账操作（增加余额）
   */
  async addBalance(
    username: string,
    amount: number,
    session?: ClientSession
  ): Promise<Result<TestAccountDocument | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findOneAndUpdate(
        { username },
        { $inc: { balance: amount, version: 1 } },
        options
      ).exec();
    })(session);
  }

  /**
   * 创建测试账户
   */
  async createTestAccount(
    username: string,
    initialBalance = 1000,
    session?: ClientSession
  ): Promise<Result<TestAccountDocument>> {
    return this.create({
      username,
      balance: initialBalance,
      version: 0
    } as Partial<TestAccountDocument>, session);
  }
}
