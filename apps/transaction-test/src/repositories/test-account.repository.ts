import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import {
  TestAccount,
  TestAccountDocument,
} from '../schemas/test-account.schema';
import {PaginatedResult, PaginationResult, XResult, XResultUtils} from '@libs/common/types';

@Injectable()
export class TestAccountRepository extends BaseRepository<TestAccountDocument> {
  constructor(
    @InjectModel(TestAccount.name) testAccountModel: Model<TestAccountDocument>
  ) {
    super(testAccountModel, 'TestAccountRepository');
  }

  /**
   * 根据用户名查找账户
   */
  async findByUsername(
    username: string,
    session?: ClientSession
  ): Promise<XResult<TestAccountDocument | null>> {
    // 现在可以直接传session，无需空的options对象
    return session ? this.findOne({ username }, session) : this.findOne({ username });
  }

  /**
   * 转账操作（扣除余额）
   */
  async deductBalance(
    username: string,
    amount: number,
    session?: ClientSession
  ): Promise<XResult<TestAccountDocument | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findOneAndUpdate(
        { username, balance: { $gte: amount } }, // 确保余额足够
        { $inc: { balance: -amount, version: 1 } },
        options
      ).exec();
    })(session);
  }

  /**
   * 转账操作（增加余额）
   */
  async addBalance(
    username: string,
    amount: number,
    session?: ClientSession
  ): Promise<XResult<TestAccountDocument | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findOneAndUpdate(
        { username },
        { $inc: { balance: amount, version: 1 } },
        options
      ).exec();
    })(session);
  }

  /**
   * 创建测试账户
   */
  async createTestAccount(
    username: string,
    initialBalance = 1000,
    session?: ClientSession
  ): Promise<XResult<TestAccountDocument>> {
    return this.create({
      username,
      balance: initialBalance,
      version: 0
    } as Partial<TestAccountDocument>, session);
  }

  // ========== 高性能查询方法示例 ==========

  /**
   * 获取账户基础信息（性能优化）
   */
  async getAccountInfo(username: string, session?: ClientSession): Promise<XResult<TestAccount | null>> {
    return this.findOne(
      { username },
      {
        lean: true,
        select: 'username balance version'
      },
      session
    );
  }



  /**
   * 获取账户列表（分页，性能优化）
   */
  async getAccountList(
    page: number,
    limit: number,
    session?: ClientSession
  ): Promise<XResult<PaginationResult<any>>> {
    return this.findWithPagination(
      {
        page,
        limit,
        lean: true,
        select: 'username balance createdAt',
        sort: { balance: -1 }
      },
      session
    );
  }

  /**
   * 搜索账户（性能优化）
   */
  async searchAccounts(
    searchTerm: string,
    session?: ClientSession
  ): Promise<XResult<TestAccount[]>> {
    return this.search(
      ['username'],
      searchTerm,
      {
        select: 'username balance',
        limit: 20,
        sort: { balance: -1 }
      },
      session
    );
  }

  /**
   * 获取富豪榜（性能优化）
   */
  async getRichList(limit: number = 10, session?: ClientSession): Promise<XResult<TestAccount[]>> {
    return this.find(
      {},
      {
        lean: true,
        select: 'username balance',
        sort: { balance: -1 },
        limit
      },
      session
    );
  }

  /**
   * 获取账户列表（普通查询，用于性能对比）
   */
  async getAccountListNormal(
    page: number,
    limit: number,
    session?: ClientSession
  ): Promise<XResult<PaginationResult<TestAccountDocument>>> {
    return this.findWithPagination(
      {
        page,
        limit,
        lean: false, // 使用普通查询
        select: 'username balance createdAt',
        sort: { balance: -1 }
      },
      session
    );
  }
}
