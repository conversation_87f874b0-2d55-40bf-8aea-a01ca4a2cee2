import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { TestItem, TestItemDocument } from '../schemas/test-item.schema';
import { Result } from '@libs/common/types';

/**
 * 测试道具Repository
 * 用于验证事务中的道具操作
 */
@Injectable()
export class TestItemRepository extends BaseRepository<TestItemDocument> {
  constructor(
    @InjectModel(TestItem.name) testItemModel: Model<TestItemDocument>
  ) {
    super(testItemModel, 'TestItemRepository');
  }

  /**
   * 添加道具
   */
  async addItem(
    userId: string,
    itemId: string,
    itemName: string,
    quantity: number,
    purchasePrice: number,
    session?: ClientSession
  ): Promise<Result<TestItemDocument>> {
    try {
      this.logger.log(`添加道具: userId=${userId}, itemId=${itemId}, quantity=${quantity}`);

      const itemData = {
        userId,
        itemId,
        itemName,
        quantity,
        purchasePrice,
        purchaseTime: new Date(),
        version: 0
      };

      const [newItem] = await this.model.create([itemData], { session });
      
      this.logger.log(`道具添加成功: ${JSON.stringify(newItem.toObject())}`);
      return this.success(newItem);
    } catch (error: any) {
      this.logger.error(`添加道具失败: ${error.message}`);
      
      if (error.code === 11000) {
        return this.error('道具已存在', 'ITEM_ALREADY_EXISTS');
      }
      
      return this.error('添加道具失败: ' + error.message, 'ADD_ITEM_ERROR');
    }
  }

  /**
   * 获取用户道具
   */
  async getUserItem(
    userId: string, 
    itemId: string, 
    session?: ClientSession
  ): Promise<Result<TestItemDocument | null>> {
    try {
      const item = await this.model.findOne({ userId, itemId }).session(session || null);
      return this.success(item);
    } catch (error: any) {
      return this.error('获取道具失败: ' + error.message, 'GET_ITEM_ERROR');
    }
  }

  /**
   * 获取用户所有道具
   */
  async getUserItems(userId: string, session?: ClientSession): Promise<Result<TestItemDocument[]>> {
    try {
      const items = await this.model.find({ userId }).session(session || null);
      return this.success(items);
    } catch (error: any) {
      return this.error('获取道具列表失败: ' + error.message, 'GET_ITEMS_ERROR');
    }
  }

  /**
   * 更新道具数量
   */
  async updateQuantity(
    userId: string,
    itemId: string,
    quantityChange: number,
    session?: ClientSession
  ): Promise<Result<TestItemDocument>> {
    try {
      const updatedItem = await this.model.findOneAndUpdate(
        { userId, itemId },
        { 
          $inc: { quantity: quantityChange, version: 1 }
        },
        { 
          session, 
          new: true,
          runValidators: true
        }
      );

      if (!updatedItem) {
        return this.error('道具不存在', 'ITEM_NOT_FOUND');
      }

      if (updatedItem.quantity < 0) {
        return this.error('道具数量不足', 'INSUFFICIENT_QUANTITY');
      }

      return this.success(updatedItem);
    } catch (error: any) {
      return this.error('更新道具数量失败: ' + error.message, 'UPDATE_QUANTITY_ERROR');
    }
  }

  /**
   * 移除道具（用于补偿）
   */
  async removeItem(userId: string, itemId: string, session?: ClientSession): Promise<Result<void>> {
    try {
      this.logger.log(`移除道具: userId=${userId}, itemId=${itemId}`);
      
      const result = await this.model.deleteOne({ userId, itemId }, { session });
      
      if (result.deletedCount === 0) {
        this.logger.warn(`道具不存在，无需移除: userId=${userId}, itemId=${itemId}`);
      } else {
        this.logger.log(`道具移除成功: userId=${userId}, itemId=${itemId}`);
      }
      
      return this.empty();
    } catch (error: any) {
      this.logger.error(`移除道具失败: ${error.message}`);
      return this.error('移除道具失败: ' + error.message, 'REMOVE_ITEM_ERROR');
    }
  }

  /**
   * 统计用户道具总数
   */
  async getUserItemCount(userId: string, session?: ClientSession): Promise<Result<number>> {
    try {
      const count = await this.model.countDocuments({ userId }).session(session || null);
      return this.success(count);
    } catch (error: any) {
      return this.error('统计道具数量失败: ' + error.message, 'COUNT_ITEMS_ERROR');
    }
  }
}
