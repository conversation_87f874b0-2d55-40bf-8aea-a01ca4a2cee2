import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { TestItem, TestItemDocument } from '../schemas/test-item.schema';
import { XResult, XResultUtils } from '@libs/common/types';

/**
 * 测试道具Repository
 * 用于验证事务中的道具操作
 */
@Injectable()
export class TestItemRepository extends BaseRepository<TestItemDocument> {
  constructor(
    @InjectModel(TestItem.name) testItemModel: Model<TestItemDocument>
  ) {
    super(testItemModel, 'TestItemRepository');
  }

  /**
   * 添加道具
   */
  async addItem(
    userId: string,
    itemId: string,
    itemName: string,
    quantity: number,
    purchasePrice: number,
    session?: ClientSession
  ): Promise<XResult<TestItemDocument>> {
    this.logger.log(`添加道具: userId=${userId}, itemId=${itemId}, quantity=${quantity}`);

    const itemData = {
      userId,
      itemId,
      itemName,
      quantity,
      purchasePrice,
      purchaseTime: new Date(),
      version: 0
    };

    return this.create(itemData as Partial<TestItemDocument>, session);
  }

  /**
   * 获取用户道具
   */
  async getUserItem(
    userId: string,
    itemId: string,
    session?: ClientSession
  ): Promise<XResult<TestItemDocument | null>> {
    // 使用方法重载，无需空的options对象
    return session ? this.findOne({ userId, itemId }, session) : this.findOne({ userId, itemId });
  }

  /**
   * 获取用户所有道具
   */
  async getUserItems(userId: string, session?: ClientSession): Promise<XResult<TestItemDocument[]>> {
    // 使用方法重载，无需空的options对象
    return session ? this.find({ userId }, session) : this.find({ userId });
  }

  /**
   * 移除道具（用于补偿）
   */
  async removeItem(userId: string, itemId: string, session?: ClientSession): Promise<XResult<void>> {
    try {
      this.logger.log(`移除道具: userId=${userId}, itemId=${itemId}`);
      await this.model.deleteOne({ userId, itemId }, { session });
      return XResultUtils.empty();
    } catch (error: any) {
      return XResultUtils.failure('REMOVE_ITEM_ERROR', '移除道具失败: ' + error.message);
    }
  }
}
