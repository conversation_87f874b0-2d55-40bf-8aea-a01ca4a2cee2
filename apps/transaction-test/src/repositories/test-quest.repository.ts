import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { TestQuestProgress, TestQuestProgressDocument } from '../schemas/test-quest.schema';
import { Result } from '@libs/common/types';

/**
 * 测试任务进度Repository
 * 用于验证事务中的任务进度操作
 */
@Injectable()
export class TestQuestRepository extends BaseRepository<TestQuestProgressDocument> {
  constructor(
    @InjectModel(TestQuestProgress.name) testQuestModel: Model<TestQuestProgressDocument>
  ) {
    super(testQuestModel, 'TestQuestRepository');
  }

  /**
   * 初始化任务进度
   */
  async initializeQuest(
    userId: string,
    questId: string,
    questName: string,
    targetProgress: number,
    session?: ClientSession
  ): Promise<Result<TestQuestProgressDocument>> {
    this.logger.log(`初始化任务: userId=${userId}, questId=${questId}, target=${targetProgress}`);

    const questData = {
      userId,
      questId,
      questName,
      currentProgress: 0,
      targetProgress,
      completed: false,
      rewardClaimed: 0,
      version: 0
    };

    return this.create(questData as Partial<TestQuestProgressDocument>, session);
  }

  /**
   * 获取任务进度
   */
  async getQuest(
    userId: string,
    questId: string,
    session?: ClientSession
  ): Promise<Result<TestQuestProgressDocument | null>> {
    return this.findOne({ userId, questId }, session);
  }

  /**
   * 更新任务进度（自定义逻辑，需要手动实现）
   */
  async updateProgress(
    userId: string,
    questId: string,
    progressIncrement: number,
    session?: ClientSession
  ): Promise<Result<{ quest: TestQuestProgressDocument; justCompleted: boolean; reward: number }>> {
    this.logger.log(`更新任务进度: userId=${userId}, questId=${questId}, increment=${progressIncrement}`);

    // 获取任务
    const questResult = await this.findOne({ userId, questId }, session);
    if (!questResult.success || !questResult.data) {
      return { success: false, code: 'QUEST_NOT_FOUND', message: '任务不存在' } as Result<any>;
    }

    const quest = questResult.data;

    if (quest.completed) {
      this.logger.warn(`任务已完成，无需更新: userId=${userId}, questId=${questId}`);
      return {
        success: true,
        data: { quest, justCompleted: false, reward: 0 }
      } as Result<{ quest: TestQuestProgressDocument; justCompleted: boolean; reward: number }>;
    }

    // 计算新进度
    const oldProgress = quest.currentProgress;
    const newProgress = oldProgress + progressIncrement;
    let justCompleted = false;
    let reward = 0;

    // 检查是否完成
    if (newProgress >= quest.targetProgress && !quest.completed) {
      justCompleted = true;
      reward = 100; // 完成任务奖励100金币
      this.logger.log(`任务完成！userId=${userId}, questId=${questId}, 奖励=${reward}`);
    }

    // 更新任务
    const updateData: any = {
      currentProgress: newProgress,
      $inc: { version: 1 }
    };

    if (justCompleted) {
      updateData.completed = true;
      updateData.completedAt = new Date();
      updateData.rewardClaimed = reward;
    }

    const updateResult = await this.updateOne({ userId, questId }, updateData, session);
    if (!updateResult.success) {
      return updateResult as Result<any>;
    }

    // 重新获取更新后的任务
    const updatedQuestResult = await this.findOne({ userId, questId }, session);
    if (!updatedQuestResult.success || !updatedQuestResult.data) {
      return { success: false, code: 'UPDATE_FAILED', message: '更新后获取任务失败' } as Result<any>;
    }

    this.logger.log(`任务进度更新成功: ${oldProgress} -> ${newProgress}, 完成=${justCompleted}`);

    return {
      success: true,
      data: {
        quest: updatedQuestResult.data,
        justCompleted,
        reward
      }
    } as Result<{ quest: TestQuestProgressDocument; justCompleted: boolean; reward: number }>;
  }

  /**
   * 获取用户所有任务
   */
  async getUserQuests(userId: string, session?: ClientSession): Promise<Result<TestQuestProgressDocument[]>> {
    try {
      const quests = await this.model.find({ userId }).session(session || null);
      return this.success(quests);
    } catch (error: any) {
      return this.error('获取任务列表失败: ' + error.message, 'GET_QUESTS_ERROR');
    }
  }

  /**
   * 回滚任务进度（用于补偿）
   */
  async rollbackProgress(
    userId: string,
    questId: string,
    progressDecrement: number,
    rewardToRevoke: number,
    session?: ClientSession
  ): Promise<Result<TestQuestProgressDocument | null>> {
    try {
      this.logger.log(`回滚任务进度: userId=${userId}, questId=${questId}, decrement=${progressDecrement}, revokeReward=${rewardToRevoke}`);

      const quest = await this.model.findOne({ userId, questId }).session(session || null);
      if (!quest) {
        this.logger.warn(`任务不存在，无需回滚: userId=${userId}, questId=${questId}`);
        return this.success(null);
      }

      // 记录回滚前状态
      const wasCompleted = quest.completed;
      const oldProgress = quest.currentProgress;

      // 回滚进度
      quest.currentProgress = Math.max(0, quest.currentProgress - progressDecrement);
      quest.version += 1;
      
      // 如果之前完成了，现在检查是否需要取消完成状态
      if (wasCompleted && quest.currentProgress < quest.targetProgress) {
        quest.completed = false;
        quest.completedAt = undefined;
        quest.rewardClaimed = Math.max(0, quest.rewardClaimed - rewardToRevoke);
        
        this.logger.log(`任务完成状态已回滚: userId=${userId}, questId=${questId}`);
      }

      await quest.save({ session });

      this.logger.log(`任务进度回滚成功: ${oldProgress} -> ${quest.currentProgress}, 完成=${quest.completed}`);
      return this.success(quest);
    } catch (error: any) {
      this.logger.error(`回滚任务进度失败: ${error.message}`);
      return this.error('回滚任务进度失败: ' + error.message, 'ROLLBACK_QUEST_ERROR');
    }
  }

  /**
   * 重置任务（用于测试清理）
   */
  async resetQuest(
    userId: string,
    questId: string,
    session?: ClientSession
  ): Promise<Result<void>> {
    try {
      await this.model.updateOne(
        { userId, questId },
        {
          currentProgress: 0,
          completed: false,
          completedAt: undefined,
          rewardClaimed: 0,
          $inc: { version: 1 }
        },
        { session }
      );
      
      return this.empty();
    } catch (error: any) {
      return this.error('重置任务失败: ' + error.message, 'RESET_QUEST_ERROR');
    }
  }

  /**
   * 删除任务（用于测试清理）
   */
  async deleteQuest(userId: string, questId: string, session?: ClientSession): Promise<Result<void>> {
    try {
      await this.model.deleteOne({ userId, questId }, { session });
      return this.empty();
    } catch (error: any) {
      return this.error('删除任务失败: ' + error.message, 'DELETE_QUEST_ERROR');
    }
  }
}
