import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { BaseRepository } from '@libs/common/transaction';
import { TestQuestProgress, TestQuestProgressDocument } from '../schemas/test-quest.schema';
import {XResult, XResultUtils} from '@libs/common/types';

/**
 * 测试任务进度Repository
 * 用于验证事务中的任务进度操作
 */
@Injectable()
export class TestQuestRepository extends BaseRepository<TestQuestProgressDocument> {
  constructor(
    @InjectModel(TestQuestProgress.name) testQuestModel: Model<TestQuestProgressDocument>
  ) {
    super(testQuestModel, 'TestQuestRepository');
  }

  /**
   * 初始化任务进度
   */
  async initializeQuest(
    userId: string,
    questId: string,
    questName: string,
    targetProgress: number,
    session?: ClientSession
  ): Promise<XResult<TestQuestProgressDocument>> {
    this.logger.log(`初始化任务: userId=${userId}, questId=${questId}, target=${targetProgress}`);

    const questData = {
      userId,
      questId,
      questName,
      currentProgress: 0,
      targetProgress,
      completed: false,
      rewardClaimed: 0,
      version: 0
    };

    return this.create(questData as Partial<TestQuestProgressDocument>, session);
  }

  /**
   * 获取任务进度
   */
  async getQuest(
    userId: string,
    questId: string,
    session?: ClientSession
  ): Promise<XResult<TestQuestProgressDocument | null>> {
    return this.findOne({ userId, questId }, session);
  }

  /**
   * 更新任务进度（使用Result模式最佳实践）
   */
  async updateProgress(
    userId: string,
    questId: string,
    progressIncrement: number,
    session?: ClientSession
  ): Promise<XResult<{ quest: TestQuestProgressDocument; justCompleted: boolean; reward: number }>> {
    this.logger.log(`更新任务进度: userId=${userId}, questId=${questId}, increment=${progressIncrement}`);

    try {
      // 获取任务
      const quest = await this.model.findOne({ userId, questId }).session(session || null);
      if (!quest) {
        return XResultUtils.failure('QUEST_NOT_FOUND','任务不存在');
      }

      if (quest.completed) {
        this.logger.warn(`任务已完成，无需更新: userId=${userId}, questId=${questId}`);
        return XResultUtils.success({ quest, justCompleted: false, reward: 0 });
      }

      // 计算新进度
      const oldProgress = quest.currentProgress;
      const newProgress = oldProgress + progressIncrement;
      let justCompleted = false;
      let reward = 0;

      // 更新进度
      quest.currentProgress = newProgress;
      quest.version += 1;

      // 检查是否完成
      if (newProgress >= quest.targetProgress && !quest.completed) {
        quest.completed = true;
        quest.completedAt = new Date();
        reward = 100; // 完成任务奖励100金币
        quest.rewardClaimed = reward;
        justCompleted = true;

        this.logger.log(`任务完成！userId=${userId}, questId=${questId}, 奖励=${reward}`);
      }

      // 保存更新
      await quest.save({ session });

      this.logger.log(`任务进度更新成功: ${oldProgress} -> ${newProgress}, 完成=${justCompleted}`);

      return XResultUtils.success({ quest, justCompleted, reward });
    } catch (error: any) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
      return XResultUtils.failure('UPDATE_QUEST_ERROR', '更新任务进度失败: ' + error.message);
    }
  }

  /**
   * 获取用户所有任务
   */
  async getUserQuests(userId: string, session?: ClientSession): Promise<XResult<TestQuestProgressDocument[]>> {
    try {
      const quests = await this.model.find({ userId }).session(session || null);
      return XResultUtils.success(quests);
    } catch (error: any) {
      return XResultUtils.failure('GET_QUESTS_ERROR', '获取任务列表失败: ' + error.message);
    }
  }

  /**
   * 删除任务（用于测试清理）
   */
  async deleteQuest(userId: string, questId: string, session?: ClientSession): Promise<XResult<void>> {
    try {
      await this.model.deleteOne({ userId, questId }, { session });
      return XResultUtils.empty();
    } catch (error: any) {
      return XResultUtils.failure('DELETE_QUEST_ERROR', '删除任务失败: ' + error.message);
    }
  }
}
