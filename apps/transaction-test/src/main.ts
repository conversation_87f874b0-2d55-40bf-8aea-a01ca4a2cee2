import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import mongoose from 'mongoose';
import { TransactionManager } from '@libs/common/transaction';
import { AppModule } from './app.module';

async function bootstrap() {
  console.log('🚀 启动事务测试服务...');

  // 1. 连接MongoDB
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/transaction-test');
    console.log('✅ MongoDB连接成功');
    
    // 2. 初始化事务管理器
    TransactionManager.initialize(mongoose);
    console.log('✅ 事务管理器初始化成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }

  // 3. 创建微服务应用
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: parseInt(process.env.TRANSACTION_TEST_PORT || '3010'),
    },
  });

  // 4. 启动服务
  await app.listen();
  console.log('✅ 事务测试服务启动成功，端口: 3010');
}

bootstrap().catch((error) => {
  console.error('❌ 服务启动失败:', error);
  process.exit(1);
});
