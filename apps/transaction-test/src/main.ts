import { NestFactory } from '@nestjs/core';
import mongoose from 'mongoose';
import { TransactionManager } from '@libs/common/transaction';
import { AppModule } from './app.module';

async function bootstrap() {
  console.log('🚀 启动事务测试服务...');

  // 1. 连接MongoDB
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/transaction-test');
    console.log('✅ MongoDB连接成功');

    // 2. 初始化事务管理器
    TransactionManager.initialize(mongoose);
    console.log('✅ 事务管理器初始化成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }

  // 3. 创建HTTP应用
  const app = await NestFactory.create(AppModule);

  // 4. 启用CORS
  app.enableCors();

  // 5. 设置全局前缀
  app.setGlobalPrefix('api');

  // 6. 启动服务
  const port = parseInt(process.env.TRANSACTION_TEST_PORT || '3010');
  await app.listen(port, '0.0.0.0');
  console.log(`✅ 事务测试服务启动成功，端口: ${port}`);
  console.log(`📡 HTTP接口地址: http://localhost:${port}/api/transaction-test`);
}

bootstrap().catch((error) => {
  console.error('❌ 服务启动失败:', error);
  process.exit(1);
});
