import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TestAccount, TestAccountSchema } from '../schemas/test-account.schema';
import { TestItem, TestItemSchema } from '../schemas/test-item.schema';
import { TestQuestProgress, TestQuestProgressSchema } from '../schemas/test-quest.schema';
import { TestAccountRepository } from '../repositories/test-account.repository';
import { TestItemRepository } from '../repositories/test-item.repository';
import { TestQuestRepository } from '../repositories/test-quest.repository';
import { TransactionTestService } from '../services/transaction-test.service';
import { TransactionTestController } from '../controllers/transaction-test.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TestAccount.name, schema: TestAccountSchema },
      { name: TestItem.name, schema: TestItemSchema },
      { name: TestQuestProgress.name, schema: TestQuestProgressSchema }
    ])
  ],
  controllers: [TransactionTestController],
  providers: [
    TestAccountRepository,
    TestItemRepository,
    TestQuestRepository,
    TransactionTestService
  ],
  exports: [
    TestAccountRepository,
    TestItemRepository,
    TestQuestRepository,
    TransactionTestService
  ]
})
export class TestAccountModule {}
