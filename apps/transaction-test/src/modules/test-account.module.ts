import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TestAccount, TestAccountSchema } from '../schemas/test-account.schema';
import { TestAccountRepository } from '../repositories/test-account.repository';
import { TransactionTestService } from '../services/transaction-test.service';
import { TransactionTestController } from '../controllers/transaction-test.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TestAccount.name, schema: TestAccountSchema }
    ])
  ],
  controllers: [TransactionTestController],
  providers: [
    TestAccountRepository,
    TransactionTestService
  ],
  exports: [
    TestAccountRepository,
    TransactionTestService
  ]
})
export class TestAccountModule {}
