import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/transaction';
import { Result } from '@libs/common/types';
import { TestAccountRepository } from '../repositories/test-account.repository';
import { TestAccountDocument } from '../schemas/test-account.schema';

export interface TransferResult {
  fromAccount: TestAccountDocument;
  toAccount: TestAccountDocument;
  amount: number;
  transferId: string;
}

@Injectable()
export class TransactionTestService extends BaseService {
  constructor(
    private readonly testAccountRepo: TestAccountRepository
  ) {
    super('TransactionTestService');
  }

  /**
   * 测试1：成功的转账事务
   */
  async testSuccessfulTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<Result<TransferResult>> {
    this.logOperation('开始测试成功转账', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as Result<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as Result<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      // 4. 扣除发送方余额
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as Result<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      // 5. 增加接收方余额
      const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
      if (this.isFailure(addResult)) {
        return addResult as Result<TransferResult>;
      }

      const updatedToAccount = addResult.data;
      if (!updatedToAccount) {
        return this.error('增加余额失败', 'ADD_FAILED');
      }

      // 6. 返回成功结果
      return this.success({
        fromAccount: updatedFromAccount,
        toAccount: updatedToAccount,
        amount,
        transferId: `transfer_${Date.now()}`
      });
    });
  }

  /**
   * 测试2：严谨的事务回滚测试
   *
   * 测试场景：
   * 1. 成功扣除发送方余额
   * 2. 在增加接收方余额时故意失败
   * 3. 验证整个事务被回滚，发送方余额恢复
   */
  async testFailedTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<Result<TransferResult>> {
    this.logOperation('开始严谨的事务回滚测试', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as Result<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as Result<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额（确保有足够余额进行扣除）
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      this.logOperation('事务步骤1：扣除发送方余额', {
        username: fromUsername,
        originalBalance: fromAccount.balance,
        deductAmount: amount
      });

      // 4. 【关键步骤1】先扣除发送方余额（这步会成功）
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as Result<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      this.logOperation('事务步骤1完成：发送方余额已扣除', {
        username: fromUsername,
        newBalance: updatedFromAccount.balance
      });

      // 5. 【关键步骤2】故意在增加接收方余额时失败
      this.logOperation('事务步骤2：准备增加接收方余额（将故意失败）');

      // 模拟业务逻辑错误：比如接收方账户被冻结
      if (toAccount.username === toUsername) {
        this.logOperation('模拟业务错误：接收方账户被冻结，事务将回滚');
        return this.error('接收方账户被冻结，转账失败，事务回滚', 'ACCOUNT_FROZEN');
      }

      // 这部分代码不应该执行到
      return this.error('不应该到达这里', 'UNEXPECTED_ERROR');
    });
  }

  /**
   * 测试3：创建测试账户
   */
  async createTestAccounts(): Promise<Result<TestAccountDocument[]>> {
    this.logOperation('创建测试账户');

    return this.executeTransaction(async (session) => {
      const accounts: TestAccountDocument[] = [];

      // 创建账户A
      const accountAResult = await this.testAccountRepo.createTestAccount('testUserA', 1000, session);
      if (this.isFailure(accountAResult)) {
        return accountAResult as Result<TestAccountDocument[]>;
      }
      accounts.push(accountAResult.data);

      // 创建账户B
      const accountBResult = await this.testAccountRepo.createTestAccount('testUserB', 500, session);
      if (this.isFailure(accountBResult)) {
        return accountBResult as Result<TestAccountDocument[]>;
      }
      accounts.push(accountBResult.data);

      return this.success(accounts);
    });
  }

  /**
   * 测试4：查询账户余额
   */
  async getAccountBalance(username: string): Promise<Result<{ username: string; balance: number }>> {
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return accountResult as Result<{ username: string; balance: number }>;
    }

    const account = accountResult.data;
    if (!account) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    return this.success({
      username: account.username,
      balance: account.balance
    });
  }

  /**
   * 测试5：复杂事务回滚测试（多步骤操作）
   *
   * 测试场景：
   * 1. 扣除A账户余额
   * 2. 增加B账户余额
   * 3. 创建转账记录
   * 4. 在最后一步故意失败
   * 5. 验证前面所有操作都被回滚
   */
  async testComplexTransactionRollback(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<Result<{ message: string; rollbackVerified: boolean }>> {
    this.logOperation('开始复杂事务回滚测试', { fromUsername, toUsername, amount });

    // 记录事务前的状态
    const beforeFromResult = await this.testAccountRepo.findByUsername(fromUsername);
    const beforeToResult = await this.testAccountRepo.findByUsername(toUsername);

    if (this.isFailure(beforeFromResult) || this.isFailure(beforeToResult)) {
      return this.error('无法获取账户初始状态', 'INITIAL_STATE_ERROR');
    }

    const beforeFromBalance = beforeFromResult.data?.balance || 0;
    const beforeToBalance = beforeToResult.data?.balance || 0;

    this.logOperation('事务前状态', {
      fromBalance: beforeFromBalance,
      toBalance: beforeToBalance
    });

    // 执行事务（预期失败）
    const transactionResult = await this.executeTransaction(async (session) => {
      // 步骤1：扣除发送方余额
      this.logOperation('复杂事务步骤1：扣除发送方余额');
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as Result<{ message: string; rollbackVerified: boolean }>;
      }

      // 步骤2：增加接收方余额
      this.logOperation('复杂事务步骤2：增加接收方余额');
      const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
      if (this.isFailure(addResult)) {
        return addResult as Result<{ message: string; rollbackVerified: boolean }>;
      }

      // 步骤3：创建转账记录（模拟）
      this.logOperation('复杂事务步骤3：创建转账记录');
      const recordResult = await this.testAccountRepo.mongooseModel.create([{
        username: `transfer_${Date.now()}`,
        balance: amount,
        version: 0
      }], { session });

      // 步骤4：故意失败
      this.logOperation('复杂事务步骤4：故意失败，触发回滚');
      return this.error('故意失败：模拟系统错误，所有操作应该回滚', 'INTENTIONAL_FAILURE');
    });

    // 验证事务确实失败了
    if (this.isSuccess(transactionResult)) {
      return this.error('事务应该失败但却成功了', 'TRANSACTION_SHOULD_FAIL');
    }

    this.logOperation('事务已失败，开始验证回滚效果');

    // 验证回滚效果
    const afterFromResult = await this.testAccountRepo.findByUsername(fromUsername);
    const afterToResult = await this.testAccountRepo.findByUsername(toUsername);

    if (this.isFailure(afterFromResult) || this.isFailure(afterToResult)) {
      return this.error('无法获取账户回滚后状态', 'ROLLBACK_STATE_ERROR');
    }

    const afterFromBalance = afterFromResult.data?.balance || 0;
    const afterToBalance = afterToResult.data?.balance || 0;

    this.logOperation('事务后状态', {
      fromBalance: afterFromBalance,
      toBalance: afterToBalance
    });

    // 验证余额是否完全回滚
    const rollbackVerified = (beforeFromBalance === afterFromBalance) && (beforeToBalance === afterToBalance);

    if (rollbackVerified) {
      this.logOperation('✅ 事务回滚验证成功：所有操作都被正确回滚');
      return this.success({
        message: '复杂事务回滚测试成功：多步骤操作全部回滚',
        rollbackVerified: true
      });
    } else {
      this.logError('❌ 事务回滚验证失败', {
        expected: { from: beforeFromBalance, to: beforeToBalance },
        actual: { from: afterFromBalance, to: afterToBalance }
      });
      return this.error('事务回滚验证失败：数据未完全回滚', 'ROLLBACK_VERIFICATION_FAILED');
    }
  }

  /**
   * 测试6：清理测试数据
   */
  async cleanupTestData(): Promise<Result<void>> {
    this.logOperation('清理测试数据');

    try {
      // 直接使用MongoDB删除操作
      const deleteResult = await this.testAccountRepo.mongooseModel.deleteMany({
        username: { $in: ['testUserA', 'testUserB'] }
      });

      // 清理可能的转账记录
      await this.testAccountRepo.mongooseModel.deleteMany({
        username: { $regex: /^transfer_/ }
      });

      this.logOperation('清理完成', { deletedCount: deleteResult.deletedCount });
      return this.empty();
    } catch (error: any) {
      this.logError('清理数据失败', error);
      return this.error('清理数据失败: ' + error.message, 'CLEANUP_ERROR');
    }
  }
}
