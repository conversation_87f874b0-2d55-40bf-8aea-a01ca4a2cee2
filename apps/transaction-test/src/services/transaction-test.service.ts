import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/transaction';
import { Result } from '@libs/common/types';
import { TestAccountRepository } from '../repositories/test-account.repository';
import { TestAccountDocument } from '../schemas/test-account.schema';

export interface TransferResult {
  fromAccount: TestAccountDocument;
  toAccount: TestAccountDocument;
  amount: number;
  transferId: string;
}

@Injectable()
export class TransactionTestService extends BaseService {
  constructor(
    private readonly testAccountRepo: TestAccountRepository
  ) {
    super('TransactionTestService');
  }

  /**
   * 测试1：成功的转账事务
   */
  async testSuccessfulTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<Result<TransferResult>> {
    this.logOperation('开始测试成功转账', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as Result<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as Result<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      // 4. 扣除发送方余额
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as Result<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      // 5. 增加接收方余额
      const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
      if (this.isFailure(addResult)) {
        return addResult as Result<TransferResult>;
      }

      const updatedToAccount = addResult.data;
      if (!updatedToAccount) {
        return this.error('增加余额失败', 'ADD_FAILED');
      }

      // 6. 返回成功结果
      return this.success({
        fromAccount: updatedFromAccount,
        toAccount: updatedToAccount,
        amount,
        transferId: `transfer_${Date.now()}`
      });
    });
  }

  /**
   * 测试2：严谨的事务回滚测试
   *
   * 测试场景：
   * 1. 成功扣除发送方余额
   * 2. 在增加接收方余额时故意失败
   * 3. 验证整个事务被回滚，发送方余额恢复
   */
  async testFailedTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<Result<TransferResult>> {
    this.logOperation('开始严谨的事务回滚测试', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as Result<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as Result<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额（确保有足够余额进行扣除）
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      this.logOperation('事务步骤1：扣除发送方余额', {
        username: fromUsername,
        originalBalance: fromAccount.balance,
        deductAmount: amount
      });

      // 4. 【关键步骤1】先扣除发送方余额（这步会成功）
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as Result<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      this.logOperation('事务步骤1完成：发送方余额已扣除', {
        username: fromUsername,
        newBalance: updatedFromAccount.balance
      });

      // 5. 【关键步骤2】故意在增加接收方余额时失败
      this.logOperation('事务步骤2：准备增加接收方余额（将故意失败）');

      // 模拟业务逻辑错误：比如接收方账户被冻结
      if (toAccount.username === toUsername) {
        this.logOperation('模拟业务错误：接收方账户被冻结，事务将回滚');
        return this.error('接收方账户被冻结，转账失败，事务回滚', 'ACCOUNT_FROZEN');
      }

      // 这部分代码不应该执行到
      return this.error('不应该到达这里', 'UNEXPECTED_ERROR');
    });
  }

  /**
   * 测试3：创建测试账户
   */
  async createTestAccounts(): Promise<Result<TestAccountDocument[]>> {
    this.logOperation('创建测试账户');

    return this.executeTransaction(async (session) => {
      const accounts: TestAccountDocument[] = [];

      // 创建账户A
      const accountAResult = await this.testAccountRepo.createTestAccount('testUserA', 1000, session);
      if (this.isFailure(accountAResult)) {
        return accountAResult as Result<TestAccountDocument[]>;
      }
      accounts.push(accountAResult.data);

      // 创建账户B
      const accountBResult = await this.testAccountRepo.createTestAccount('testUserB', 500, session);
      if (this.isFailure(accountBResult)) {
        return accountBResult as Result<TestAccountDocument[]>;
      }
      accounts.push(accountBResult.data);

      return this.success(accounts);
    });
  }

  /**
   * 测试4：查询账户余额
   */
  async getAccountBalance(username: string): Promise<Result<{ username: string; balance: number }>> {
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return accountResult as Result<{ username: string; balance: number }>;
    }

    const account = accountResult.data;
    if (!account) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    return this.success({
      username: account.username,
      balance: account.balance
    });
  }

  /**
   * 测试5：清理测试数据
   */
  async cleanupTestData(): Promise<Result<void>> {
    this.logOperation('清理测试数据');

    try {
      // 直接使用MongoDB删除操作
      const deleteResult = await this.testAccountRepo.mongooseModel.deleteMany({
        username: { $in: ['testUserA', 'testUserB'] }
      });

      this.logOperation('清理完成', { deletedCount: deleteResult.deletedCount });
      return this.empty();
    } catch (error: any) {
      this.logError('清理数据失败', error);
      return this.error('清理数据失败: ' + error.message, 'CLEANUP_ERROR');
    }
  }
}
