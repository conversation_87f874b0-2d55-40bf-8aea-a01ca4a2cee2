import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/transaction';
import { XResult } from '@libs/common/types';
import { TestAccountRepository } from '../repositories/test-account.repository';
import { TestItemRepository } from '../repositories/test-item.repository';
import { TestQuestRepository } from '../repositories/test-quest.repository';
import { TestAccountDocument } from '../schemas/test-account.schema';
import { TestItemDocument } from '../schemas/test-item.schema';
import { TestQuestProgressDocument } from '../schemas/test-quest.schema';

export interface TransferResult {
  fromAccount: TestAccountDocument;
  toAccount: TestAccountDocument;
  amount: number;
  transferId: string;
}

export interface ComplexPurchaseResult {
  username: string;
  item: {
    itemId: string;
    itemName: string;
    quantity: number;
    purchasePrice: number;
  };
  transaction: {
    originalBalance: number;
    finalBalance: number;
    amountSpent: number;
    rewardReceived: number;
  };
  quest: {
    questId: string;
    questName: string;
    currentProgress: number;
    targetProgress: number;
    completed: boolean;
    justCompleted: boolean;
    rewardClaimed: number;
  };
  timestamp: Date;
}

@Injectable()
export class TransactionTestService extends BaseService {
  constructor(
    private readonly testAccountRepo: TestAccountRepository,
    private readonly testItemRepo: TestItemRepository,
    private readonly testQuestRepo: TestQuestRepository
  ) {
    super('TransactionTestService');
  }

  /**
   * 测试1：成功的转账事务
   */
  async testSuccessfulTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<XResult<TransferResult>> {
    this.logOperation('开始测试成功转账', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as XResult<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as XResult<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      // 4. 扣除发送方余额
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as XResult<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      // 5. 增加接收方余额
      const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
      if (this.isFailure(addResult)) {
        return addResult as XResult<TransferResult>;
      }

      const updatedToAccount = addResult.data;
      if (!updatedToAccount) {
        return this.error('增加余额失败', 'ADD_FAILED');
      }

      // 6. 返回成功结果
      return this.success({
        fromAccount: updatedFromAccount,
        toAccount: updatedToAccount,
        amount,
        transferId: `transfer_${Date.now()}`
      });
    });
  }

  /**
   * 测试2：严谨的事务回滚测试
   *
   * 测试场景：
   * 1. 成功扣除发送方余额
   * 2. 在增加接收方余额时故意失败
   * 3. 验证整个事务被回滚，发送方余额恢复
   */
  async testFailedTransfer(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<XResult<TransferResult>> {
    this.logOperation('开始严谨的事务回滚测试', { fromUsername, toUsername, amount });

    return this.executeTransaction(async (session) => {
      // 1. 检查发送方账户
      const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
      if (this.isFailure(fromAccountResult)) {
        return fromAccountResult as XResult<TransferResult>;
      }

      const fromAccount = fromAccountResult.data;
      if (!fromAccount) {
        return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
      }

      // 2. 检查接收方账户
      const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
      if (this.isFailure(toAccountResult)) {
        return toAccountResult as XResult<TransferResult>;
      }

      const toAccount = toAccountResult.data;
      if (!toAccount) {
        return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
      }

      // 3. 检查余额（确保有足够余额进行扣除）
      if (fromAccount.balance < amount) {
        return this.error('余额不足', 'INSUFFICIENT_BALANCE');
      }

      this.logOperation('事务步骤1：扣除发送方余额', {
        username: fromUsername,
        originalBalance: fromAccount.balance,
        deductAmount: amount
      });

      // 4. 【关键步骤1】先扣除发送方余额（这步会成功）
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as XResult<TransferResult>;
      }

      const updatedFromAccount = deductResult.data;
      if (!updatedFromAccount) {
        return this.error('扣除余额失败', 'DEDUCT_FAILED');
      }

      this.logOperation('事务步骤1完成：发送方余额已扣除', {
        username: fromUsername,
        newBalance: updatedFromAccount.balance
      });

      // 5. 【关键步骤2】故意在增加接收方余额时失败
      this.logOperation('事务步骤2：准备增加接收方余额（将故意失败）');

      // 模拟业务逻辑错误：比如接收方账户被冻结
      if (toAccount.username === toUsername) {
        this.logOperation('模拟业务错误：接收方账户被冻结，事务将回滚');
        return this.error('接收方账户被冻结，转账失败，事务回滚', 'ACCOUNT_FROZEN');
      }

      // 这部分代码不应该执行到
      return this.error('不应该到达这里', 'UNEXPECTED_ERROR');
    });
  }

  /**
   * 测试3：创建测试账户
   */
  async createTestAccounts(
    usernameA?: string,
    usernameB?: string
  ): Promise<XResult<TestAccountDocument[]>> {
    const userA = usernameA || 'testUserA';
    const userB = usernameB || 'testUserB';

    this.logOperation('创建测试账户', { userA, userB });

    return this.executeTransaction(async (session) => {
      const accounts: TestAccountDocument[] = [];

      // 创建账户A
      const accountAResult = await this.testAccountRepo.createTestAccount(userA, 1000, session);
      if (this.isFailure(accountAResult)) {
        return accountAResult as XResult<TestAccountDocument[]>;
      }
      accounts.push(accountAResult.data);

      // 创建账户B
      const accountBResult = await this.testAccountRepo.createTestAccount(userB, 500, session);
      if (this.isFailure(accountBResult)) {
        return accountBResult as XResult<TestAccountDocument[]>;
      }
      accounts.push(accountBResult.data);

      return this.success(accounts, '测试账户创建成功');
    });
  }

  /**
   * 测试4：查询账户余额
   */
  async getAccountBalance(username: string): Promise<XResult<{ username: string; balance: number }>> {
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    const account = accountResult.data;
    return this.success({
      username: account.username,
      balance: account.balance
    });
  }

  /**
   * 查询账号道具
   */
  async getAccountItem(username: string, itemId: string): Promise<XResult<TestItemDocument>> {
    this.logOperation('查询账号道具', { username });
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    return this.testItemRepo.getUserItem(username,itemId);
  }

  /**
   * 查询账号所有道具
   */
  async getAccountItems(username: string): Promise<XResult<TestItemDocument[]>> {
    this.logOperation('查询账号所有道具', { username });
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    return this.testItemRepo.getUserItems(username);
  }

  /**
   * 查询任务
   */
  async getAccountQuest(username: string, questId: string): Promise<XResult<TestQuestProgressDocument>> {
    this.logOperation('查询任务', { username, questId });
    const accountResult = await this.testAccountRepo.findByUsername(username);
    if (this.isFailure(accountResult)) {
      return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
    }

    return this.testQuestRepo.getQuest(username, questId);
  }

  /**
   * 测试5：复杂事务回滚测试（多步骤操作）
   *
   * 测试场景：
   * 1. 扣除A账户余额
   * 2. 增加B账户余额
   * 3. 创建转账记录
   * 4. 在最后一步故意失败
   * 5. 验证前面所有操作都被回滚
   */
  async testComplexTransactionRollback(
    fromUsername: string,
    toUsername: string,
    amount: number
  ): Promise<XResult<{ message: string; rollbackVerified: boolean }>> {
    this.logOperation('开始复杂事务回滚测试', { fromUsername, toUsername, amount });

    // 记录事务前的状态
    const beforeFromResult = await this.testAccountRepo.findByUsername(fromUsername);
    const beforeToResult = await this.testAccountRepo.findByUsername(toUsername);

    if (this.isFailure(beforeFromResult) || this.isFailure(beforeToResult)) {
      return this.error('无法获取账户初始状态', 'INITIAL_STATE_ERROR');
    }

    const beforeFromBalance = beforeFromResult.data?.balance || 0;
    const beforeToBalance = beforeToResult.data?.balance || 0;

    this.logOperation('事务前状态', {
      fromBalance: beforeFromBalance,
      toBalance: beforeToBalance
    });

    // 执行事务（预期失败）
    const transactionResult = await this.executeTransaction(async (session) => {
      // 步骤1：扣除发送方余额
      this.logOperation('复杂事务步骤1：扣除发送方余额');
      const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
      if (this.isFailure(deductResult)) {
        return deductResult as XResult<{ message: string; rollbackVerified: boolean }>;
      }

      // 步骤2：增加接收方余额
      this.logOperation('复杂事务步骤2：增加接收方余额');
      const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
      if (this.isFailure(addResult)) {
        return addResult as XResult<{ message: string; rollbackVerified: boolean }>;
      }

      // 步骤3：创建转账记录（模拟）
      this.logOperation('复杂事务步骤3：创建转账记录');
      const recordResult = await this.testAccountRepo.mongooseModel.create([{
        username: `transfer_${Date.now()}`,
        balance: amount,
        version: 0
      }], { session });

      // 步骤4：故意失败
      this.logOperation('复杂事务步骤4：故意失败，触发回滚');
      return this.error('故意失败：模拟系统错误，所有操作应该回滚', 'INTENTIONAL_FAILURE');
    });

    // 验证事务确实失败了
    if (this.isSuccess(transactionResult)) {
      return this.error('事务应该失败但却成功了', 'TRANSACTION_SHOULD_FAIL');
    }

    this.logOperation('事务已失败，开始验证回滚效果');

    // 验证回滚效果
    const afterFromResult = await this.testAccountRepo.findByUsername(fromUsername);
    const afterToResult = await this.testAccountRepo.findByUsername(toUsername);

    if (this.isFailure(afterFromResult) || this.isFailure(afterToResult)) {
      return this.error('无法获取账户回滚后状态', 'ROLLBACK_STATE_ERROR');
    }

    const afterFromBalance = afterFromResult.data?.balance || 0;
    const afterToBalance = afterToResult.data?.balance || 0;

    this.logOperation('事务后状态', {
      fromBalance: afterFromBalance,
      toBalance: afterToBalance
    });

    // 验证余额是否完全回滚
    const rollbackVerified = (beforeFromBalance === afterFromBalance) && (beforeToBalance === afterToBalance);

    if (rollbackVerified) {
      this.logOperation('✅ 事务回滚验证成功：所有操作都被正确回滚');
      return this.success({
        message: '复杂事务回滚测试成功：多步骤操作全部回滚',
        rollbackVerified: true
      });
    } else {
      this.logError('❌ 事务回滚验证失败', {
        expected: { from: beforeFromBalance, to: beforeToBalance },
        actual: { from: afterFromBalance, to: afterToBalance }
      });
      return this.error('事务回滚验证失败：数据未完全回滚', 'ROLLBACK_VERIFICATION_FAILED');
    }
  }

  /**
   * 测试6：复杂购买道具事务（扣除货币 + 添加道具 + 更新任务进度）
   *
   * 这是一个严谨的多步骤事务测试：
   * 1. 检查用户余额
   * 2. 扣除购买费用
   * 3. 添加道具到背包
   * 4. 更新购买任务进度
   * 5. 如果任务完成，发放奖励
   */
  async testComplexItemPurchase(
    username: string,
    itemId: string,
    itemName: string,
    itemPrice: number,
    questId: string = 'purchase_quest'
  ): Promise<XResult<ComplexPurchaseResult>> {
    this.logOperation('开始复杂购买道具事务', { username, itemId, itemName, itemPrice, questId });

    return this.executeTransaction(async (session) => {
      // 步骤1：检查用户账户
      const accountResult = await this.testAccountRepo.findByUsername(username, session);
      if (this.isFailure(accountResult)) {
        return accountResult as XResult<ComplexPurchaseResult>;
      }

      const account = accountResult.data;
      if (!account) {
        return this.error('用户账户不存在', 'ACCOUNT_NOT_FOUND');
      }

      // 步骤2：检查余额是否足够
      if (account.balance < itemPrice) {
        return this.error(`余额不足，当前余额: ${account.balance}, 需要: ${itemPrice}`, 'INSUFFICIENT_BALANCE');
      }

      this.logOperation('步骤1完成：账户验证通过', {
        username,
        currentBalance: account.balance,
        itemPrice
      });

      // 步骤3：扣除货币
      const deductResult = await this.testAccountRepo.deductBalance(username, itemPrice, session);
      if (this.isFailure(deductResult)) {
        return deductResult as XResult<ComplexPurchaseResult>;
      }

      this.logOperation('步骤2完成：货币扣除成功', {
        username,
        deductedAmount: itemPrice,
        newBalance: deductResult.data?.balance
      });

      // 步骤4：添加道具
      const addItemResult = await this.testItemRepo.addItem(
        username,
        itemId,
        itemName,
        1,
        itemPrice,
        session
      );
      if (this.isFailure(addItemResult)) {
        return addItemResult as XResult<ComplexPurchaseResult>;
      }

      this.logOperation('步骤3完成：道具添加成功', {
        username,
        itemId,
        itemName
      });

      // 步骤5：更新任务进度
      const questResult = await this.testQuestRepo.updateProgress(
        username,
        questId,
        1, // 购买1个道具，进度+1
        session
      );
      if (this.isFailure(questResult)) {
        return questResult as XResult<ComplexPurchaseResult>;
      }

      const { quest, justCompleted, reward } = questResult.data;

      this.logOperation('步骤4完成：任务进度更新', {
        username,
        questId,
        currentProgress: quest.currentProgress,
        targetProgress: quest.targetProgress,
        justCompleted,
        reward
      });

      // 步骤6：如果任务刚完成，发放奖励
      let finalBalance = deductResult.data?.balance || 0;
      if (justCompleted && reward > 0) {
        const rewardResult = await this.testAccountRepo.addBalance(username, reward, session);
        if (this.isFailure(rewardResult)) {
          return rewardResult as XResult<ComplexPurchaseResult>;
        }

        finalBalance = rewardResult.data?.balance || finalBalance;

        this.logOperation('步骤5完成：任务奖励发放', {
          username,
          reward,
          finalBalance
        });
      }

      // 返回完整的购买结果
      const result: ComplexPurchaseResult = {
        username,
        item: {
          itemId,
          itemName,
          quantity: 1,
          purchasePrice: itemPrice
        },
        transaction: {
          originalBalance: account.balance,
          finalBalance,
          amountSpent: itemPrice,
          rewardReceived: reward
        },
        quest: {
          questId,
          questName: quest.questName,
          currentProgress: quest.currentProgress,
          targetProgress: quest.targetProgress,
          completed: quest.completed,
          justCompleted,
          rewardClaimed: quest.rewardClaimed
        },
        timestamp: new Date()
      };

      this.logOperation('复杂购买事务完成', result);
      return this.success(result);
    });
  }

  /**
   * 测试7：故意失败的复杂购买事务（验证回滚）
   *
   * 测试场景：
   * 1. 成功扣除货币
   * 2. 成功添加道具
   * 3. 在更新任务进度时故意失败
   * 4. 验证前面的操作都被回滚
   */
  async testFailedComplexPurchase(
    username: string,
    itemId: string,
    itemName: string,
    itemPrice: number
  ): Promise<XResult<ComplexPurchaseResult>> {
    this.logOperation('开始故意失败的复杂购买事务', { username, itemId, itemName, itemPrice });

    return this.executeTransaction(async (session) => {
      // 步骤1：扣除货币（成功）
      const deductResult = await this.testAccountRepo.deductBalance(username, itemPrice, session);
      if (this.isFailure(deductResult)) {
        return deductResult;
      }

      this.logOperation('步骤1完成：货币扣除成功', {
        username,
        deductedAmount: itemPrice,
        newBalance: deductResult.data?.balance
      });

      // 步骤2：添加道具（成功）
      const addItemResult = await this.testItemRepo.addItem(
        username,
        itemId,
        itemName,
        1,
        itemPrice,
        session
      );
      if (this.isFailure(addItemResult)) {
        return addItemResult;
      }

      this.logOperation('步骤2完成：道具添加成功', {
        username,
        itemId,
        itemName
      });

      // 步骤3：故意失败 - 模拟任务系统错误
      this.logOperation('步骤3：模拟任务系统错误，事务将回滚');
      return this.error('QUEST_SYSTEM_ERROR', '任务系统暂时不可用，购买失败，所有操作将回滚');
    });
  }

  /**
   * 测试8：清理测试数据
   */
  async cleanupTestData(): Promise<XResult<void>> {
    this.logOperation('清理测试数据');

    try {
      // 清理账户数据
      const deleteAccountResult = await this.testAccountRepo.mongooseModel.deleteMany({
        username: { $in: ['testUserA', 'testUserB'] }
      });

      // 清理道具数据
      const deleteItemResult = await this.testItemRepo.mongooseModel.deleteMany({
        userId: { $in: ['testUserA', 'testUserB'] }
      });

      // 清理任务数据
      const deleteQuestResult = await this.testQuestRepo.mongooseModel.deleteMany({
        userId: { $in: ['testUserA', 'testUserB'] }
      });

      // 清理可能的转账记录
      await this.testAccountRepo.mongooseModel.deleteMany({
        username: { $regex: /^transfer_/ }
      });

      this.logOperation('清理完成', {
        deletedAccounts: deleteAccountResult.deletedCount,
        deletedItems: deleteItemResult.deletedCount,
        deletedQuests: deleteQuestResult.deletedCount
      });

      return this.empty();
    } catch (error: any) {
      this.logError('清理数据失败', error);
      return this.error('清理数据失败: ' + error.message, 'CLEANUP_ERROR');
    }
  }

  /**
   * 测试9：初始化购买任务
   */
  async initializePurchaseQuest(
    username: string,
    questId: string = 'purchase_quest',
    targetPurchases: number = 3
  ): Promise<XResult<TestQuestProgressDocument>> {
    this.logOperation('初始化购买任务', { username, questId, targetPurchases });

    return this.executeTransaction(async (session) => {
      return await this.testQuestRepo.initializeQuest(
        username,
        questId,
        `购买${targetPurchases}个道具`,
        targetPurchases,
        session
      );
    });
  }

  // ========== 高性能查询测试方法 ==========

  /**
   * 测试10：高性能账户查询
   */
  async testHighPerformanceQueries(username: string): Promise<XResult<{
    accountInfo: any;
    accountList: any;
    searchResults: any;
    richList: any;
  }>> {
    this.logOperation('开始高性能查询测试');

    try {
      // 并行执行多个查询以测试性能
      const [accountInfo, accountList, searchResults, richList] = await Promise.all([
        // 获取账户基础信息（lean查询）
        this.testAccountRepo.getAccountInfo(username),

        // 获取账户列表（分页查询）
        this.testAccountRepo.getAccountList(1, 10),

        // 搜索账户
        this.testAccountRepo.searchAccounts('test'),

        // 获取富豪榜
        this.testAccountRepo.getRichList(5)
      ]);

      const result = {
        accountInfo: this.isSuccess(accountInfo) ? accountInfo.data : null,
        accountList: this.isSuccess(accountList) ? accountList.data : null,
        searchResults: this.isSuccess(searchResults) ? searchResults.data : null,
        richList: this.isSuccess(richList) ? richList.data : null
      };

      this.logOperation('高性能查询测试完成', result);
      return this.success(result);
    } catch (error: any) {
      this.logError('高性能查询测试失败', error);
      return this.error('高性能查询测试失败: ' + error.message, 'PERFORMANCE_TEST_ERROR');
    }
  }

  /**
   * 测试11：增强性能对比测试（1000次循环，高精度测量）
   */
  async testPerformanceComparison(username: string): Promise<XResult<{
    normalQuery: { duration: number; averagePerQuery: number; };
    leanQuery: { duration: number; averagePerQuery: number; };
    improvement: string;
    iterations: number;
  }>> {
    this.logOperation('开始增强性能对比测试（1000次循环）', { username });

    try {
      const iterations = 100;

      // 预热查询（避免首次查询的缓存影响）
      await this.testAccountRepo.findByUsername(username);
      await this.testAccountRepo.getAccountInfo(username);

      this.logOperation('开始普通查询测试', { iterations });

      // 测试普通查询（使用高精度时间）
      const normalStart = process.hrtime.bigint();
      for (let i = 0; i < iterations; i++) {
        await this.testAccountRepo.findByUsername(username);

        // 每100次输出进度
        if ((i + 1) % 100 === 0) {
          this.logOperation(`普通查询进度: ${i + 1}/${iterations}`);
        }
      }
      const normalEnd = process.hrtime.bigint();
      const normalDuration = Number(normalEnd - normalStart) / 1000000; // 转换为毫秒

      this.logOperation('开始Lean查询测试', { iterations });

      // 测试lean查询（使用高精度时间）
      const leanStart = process.hrtime.bigint();
      for (let i = 0; i < iterations; i++) {
        await this.testAccountRepo.getAccountInfo(username);

        // 每100次输出进度
        if ((i + 1) % 100 === 0) {
          this.logOperation(`Lean查询进度: ${i + 1}/${iterations}`);
        }
      }
      const leanEnd = process.hrtime.bigint();
      const leanDuration = Number(leanEnd - leanStart) / 1000000; // 转换为毫秒

      // 计算性能提升
      const improvement = normalDuration > 0
        ? `${Math.round((1 - leanDuration / normalDuration) * 100)}%`
        : '0%';

      const result = {
        normalQuery: {
          duration: Math.round(normalDuration * 100) / 100, // 保留2位小数
          averagePerQuery: Math.round((normalDuration / iterations) * 1000) / 1000 // 每次查询平均耗时，保留3位小数
        },
        leanQuery: {
          duration: Math.round(leanDuration * 100) / 100,
          averagePerQuery: Math.round((leanDuration / iterations) * 1000) / 1000
        },
        improvement,
        iterations
      };

      this.logOperation('增强性能对比测试完成', result);
      return this.success(result);
    } catch (error: any) {
      this.logError('增强性能对比测试失败', error);
      return this.error('增强性能对比测试失败: ' + error.message, 'PERFORMANCE_COMPARISON_ERROR');
    }
  }
}
