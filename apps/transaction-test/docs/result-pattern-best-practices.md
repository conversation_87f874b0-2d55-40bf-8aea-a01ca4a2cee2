# Result模式最佳实践指南

## 📋 概述

本文档详细说明了Result模式在事务管理中的最佳实践，包括正确的使用方法、常见错误和性能优化建议。

---

## 🎯 核心原则

### 1. 使用ResultUtils标准方法

#### ✅ 正确使用
```typescript
// 成功返回
return ResultUtils.success(data);

// 失败返回
return ResultUtils.failure('ERROR_CODE', '错误描述');

// 空返回
return ResultUtils.empty();
```

#### ❌ 避免手动构造
```typescript
// 不要手动构造Result对象
return { success: true, data };           // ❌
return { success: false, code, message }; // ❌
```

### 2. 一致的错误处理模式

```typescript
// 标准的Repository方法模式
async createItem(data: ItemData, session?: ClientSession): Promise<Result<ItemDocument>> {
  try {
    const item = await this.model.create([data], { session });
    return ResultUtils.success(item[0]);
  } catch (error: any) {
    this.logger.error('创建道具失败', error);
    
    // 处理特定错误
    if (error.code === 11000) {
      return ResultUtils.failure('ITEM_ALREADY_EXISTS', '道具已存在');
    }
    
    return ResultUtils.failure('CREATE_ITEM_ERROR', '创建道具失败: ' + error.message);
  }
}
```

### 3. 类型安全的结果检查

```typescript
// 使用类型守卫进行检查
const result = await this.someOperation();

if (ResultUtils.isFailure(result)) {
  // TypeScript确保这里是失败分支
  console.log(result.code);     // 类型安全
  console.log(result.message);  // 类型安全
  return result;  // 可以直接传播错误
}

// TypeScript确保这里是成功分支
console.log(result.data);  // 类型安全，data一定存在
```

---

## 🔧 实践模式

### 1. Service层事务模式

```typescript
async complexBusinessOperation(): Promise<Result<BusinessResult>> {
  return this.executeTransaction(async (session) => {
    // 步骤1：验证和准备
    const validationResult = await this.validateInput(input, session);
    if (ResultUtils.isFailure(validationResult)) {
      return validationResult as Result<BusinessResult>;
    }
    
    // 步骤2：执行核心操作
    const coreResult = await this.executeCoreLogic(data, session);
    if (ResultUtils.isFailure(coreResult)) {
      return coreResult as Result<BusinessResult>;
    }
    
    // 步骤3：后续处理
    const postResult = await this.postProcess(coreResult.data, session);
    if (ResultUtils.isFailure(postResult)) {
      return postResult as Result<BusinessResult>;
    }
    
    // 成功返回
    return ResultUtils.success({
      ...coreResult.data,
      ...postResult.data,
      timestamp: new Date()
    });
  });
}
```

### 2. Repository层数据访问模式

```typescript
async updateWithValidation(
  id: string, 
  updateData: UpdateData, 
  session?: ClientSession
): Promise<Result<Document>> {
  try {
    // 1. 检查存在性
    const existing = await this.model.findById(id).session(session || null);
    if (!existing) {
      return ResultUtils.failure('NOT_FOUND', '记录不存在');
    }
    
    // 2. 业务验证
    if (existing.status === 'LOCKED') {
      return ResultUtils.failure('RECORD_LOCKED', '记录已锁定，无法修改');
    }
    
    // 3. 执行更新
    const updated = await this.model.findByIdAndUpdate(
      id, 
      { ...updateData, updatedAt: new Date() },
      { new: true, session }
    );
    
    return ResultUtils.success(updated);
  } catch (error: any) {
    return ResultUtils.failure('UPDATE_ERROR', '更新失败: ' + error.message);
  }
}
```

### 3. 错误传播模式

```typescript
// 简洁的错误传播
async chainedOperations(): Promise<Result<FinalResult>> {
  return this.executeTransaction(async (session) => {
    // 使用短路返回模式
    const step1 = await this.step1(session);
    if (ResultUtils.isFailure(step1)) return step1 as Result<FinalResult>;
    
    const step2 = await this.step2(step1.data, session);
    if (ResultUtils.isFailure(step2)) return step2 as Result<FinalResult>;
    
    const step3 = await this.step3(step2.data, session);
    if (ResultUtils.isFailure(step3)) return step3 as Result<FinalResult>;
    
    return ResultUtils.success(step3.data);
  });
}
```

---

## 🚫 常见错误和解决方案

### 1. 忘记传递session

#### ❌ 错误示例
```typescript
return this.executeTransaction(async (session) => {
  // 忘记传递session，操作不在事务中
  const result = await this.repository.create(data);  // ❌
  return result;
});
```

#### ✅ 正确示例
```typescript
return this.executeTransaction(async (session) => {
  // 正确传递session
  const result = await this.repository.create(data, session);  // ✅
  return result;
});
```

### 2. 混用异常和Result模式

#### ❌ 错误示例
```typescript
return this.executeTransaction(async (session) => {
  const result = await this.operation(session);
  if (ResultUtils.isFailure(result)) {
    throw new Error(result.message);  // ❌ 不要抛出异常
  }
  return result;
});
```

#### ✅ 正确示例
```typescript
return this.executeTransaction(async (session) => {
  const result = await this.operation(session);
  if (ResultUtils.isFailure(result)) {
    return result;  // ✅ 直接返回失败Result
  }
  return result;
});
```

### 3. 不正确的类型转换

#### ❌ 错误示例
```typescript
const result1 = await this.operation1(session);
if (ResultUtils.isFailure(result1)) {
  return result1;  // ❌ 类型不匹配
}
```

#### ✅ 正确示例
```typescript
const result1 = await this.operation1(session);
if (ResultUtils.isFailure(result1)) {
  return result1 as Result<ExpectedType>;  // ✅ 明确类型转换
}
```

---

## ⚡ 性能优化建议

### 1. 避免不必要的类型检查

```typescript
// 如果确定操作会成功，可以直接使用数据
const result = await this.reliableOperation(session);
// 但仍然建议进行检查以保证健壮性
if (ResultUtils.isFailure(result)) {
  return result as Result<FinalType>;
}
```

### 2. 批量操作优化

```typescript
// 批量处理多个操作
async batchProcess(items: Item[]): Promise<Result<ProcessResult[]>> {
  return this.executeTransaction(async (session) => {
    const results: ProcessResult[] = [];
    
    // 使用Promise.all进行并行处理（注意事务限制）
    for (const item of items) {
      const result = await this.processItem(item, session);
      if (ResultUtils.isFailure(result)) {
        return result as Result<ProcessResult[]>;
      }
      results.push(result.data);
    }
    
    return ResultUtils.success(results);
  });
}
```

### 3. 缓存常用验证结果

```typescript
// 缓存验证结果避免重复查询
private validationCache = new Map<string, boolean>();

async validateWithCache(id: string, session?: ClientSession): Promise<Result<boolean>> {
  if (this.validationCache.has(id)) {
    return ResultUtils.success(this.validationCache.get(id)!);
  }
  
  const result = await this.performValidation(id, session);
  if (ResultUtils.isSuccess(result)) {
    this.validationCache.set(id, result.data);
  }
  
  return result;
}
```

---

## 📊 测试最佳实践

### 1. 单元测试模式

```typescript
describe('ComplexBusinessService', () => {
  it('should handle successful transaction', async () => {
    // 准备测试数据
    const testData = { /* ... */ };
    
    // 执行操作
    const result = await service.complexOperation(testData);
    
    // 验证结果
    expect(ResultUtils.isSuccess(result)).toBe(true);
    expect(result.data).toMatchObject(expectedResult);
  });
  
  it('should rollback on failure', async () => {
    // 模拟失败场景
    jest.spyOn(repository, 'operation').mockResolvedValue(
      ResultUtils.failure('TEST_ERROR', 'Test error')
    );
    
    // 执行操作
    const result = await service.complexOperation(testData);
    
    // 验证失败结果
    expect(ResultUtils.isFailure(result)).toBe(true);
    expect(result.code).toBe('TEST_ERROR');
    
    // 验证数据库状态未改变
    const dbState = await checkDatabaseState();
    expect(dbState).toEqual(initialState);
  });
});
```

### 2. 集成测试模式

```typescript
describe('Transaction Integration Tests', () => {
  beforeEach(async () => {
    // 清理测试数据
    await cleanupTestData();
  });
  
  it('should maintain data consistency across multiple operations', async () => {
    // 记录初始状态
    const initialState = await captureSystemState();
    
    // 执行复杂事务
    const result = await service.complexTransaction(testData);
    
    if (ResultUtils.isSuccess(result)) {
      // 验证成功状态
      const finalState = await captureSystemState();
      expect(finalState).toMatchExpectedChanges(initialState, result.data);
    } else {
      // 验证回滚状态
      const finalState = await captureSystemState();
      expect(finalState).toEqual(initialState);
    }
  });
});
```

---

## 🎯 总结

Result模式的核心价值：

1. **类型安全**：编译时错误检查
2. **明确控制流**：没有隐式的异常传播
3. **易于测试**：可预测的行为
4. **优雅错误处理**：统一的错误传播机制
5. **事务友好**：与事务管理完美集成

遵循这些最佳实践，可以构建健壮、可维护的事务处理系统。

---

*文档版本: v1.0*  
*最后更新: 2025-01-18*
