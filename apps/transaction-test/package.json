{"name": "transaction-test", "version": "1.0.0", "description": "Mongoose事务组件测试服务", "main": "dist/main.js", "scripts": {"build": "nest build", "start": "node dist/main", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/mongoose": "^10.0.0", "mongoose": "^7.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/node": "^20.3.1", "typescript": "^5.1.3"}}