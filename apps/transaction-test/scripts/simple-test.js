/**
 * 简化的事务测试脚本
 * 直接调用服务方法进行测试
 */

const mongoose = require('mongoose');
const { TransactionManager } = require('../../../dist/libs/common/transaction/transaction-manager');

// 模拟测试账户Schema
const testAccountSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  balance: { type: Number, required: true, default: 1000 },
  version: { type: Number, default: 0 }
}, { timestamps: true });

const TestAccount = mongoose.model('TestAccount', testAccountSchema);

class DirectTransactionTester {
  constructor() {
    this.isConnected = false;
  }

  async connect() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || '*****************************************************************');
      TransactionManager.initialize(mongoose);
      this.isConnected = true;
      console.log('✅ MongoDB连接成功');
      console.log('✅ 事务管理器初始化成功');
    } catch (error) {
      console.error('❌ 连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.isConnected) {
      await mongoose.disconnect();
      console.log('✅ 数据库连接已关闭');
    }
  }

  /**
   * 清理测试数据
   */
  async cleanup() {
    try {
      await TestAccount.deleteMany({ username: { $in: ['testUserA', 'testUserB'] } });
      console.log('✅ 测试数据清理完成');
    } catch (error) {
      console.log('⚠️ 清理数据时出错:', error.message);
    }
  }

  /**
   * 测试1：成功的转账事务
   */
  async testSuccessfulTransaction() {
    console.log('\n📝 测试1：成功的转账事务');
    
    const result = await TransactionManager.execute(async (session) => {
      // 1. 创建测试账户
      const accountA = await TestAccount.create([{ username: 'testUserA', balance: 1000 }], { session });
      const accountB = await TestAccount.create([{ username: 'testUserB', balance: 500 }], { session });
      
      console.log(`   创建账户A: ${accountA[0].username}, 余额: ${accountA[0].balance}`);
      console.log(`   创建账户B: ${accountB[0].username}, 余额: ${accountB[0].balance}`);

      // 2. 执行转账
      const transferAmount = 200;
      
      // 扣除A的余额
      const updatedA = await TestAccount.findOneAndUpdate(
        { username: 'testUserA', balance: { $gte: transferAmount } },
        { $inc: { balance: -transferAmount, version: 1 } },
        { session, new: true }
      );

      if (!updatedA) {
        return { success: false, message: '余额不足', code: 'INSUFFICIENT_BALANCE' };
      }

      // 增加B的余额
      const updatedB = await TestAccount.findOneAndUpdate(
        { username: 'testUserB' },
        { $inc: { balance: transferAmount, version: 1 } },
        { session, new: true }
      );

      console.log(`   转账后A余额: ${updatedA.balance}`);
      console.log(`   转账后B余额: ${updatedB.balance}`);

      return {
        success: true,
        data: {
          fromAccount: updatedA,
          toAccount: updatedB,
          amount: transferAmount
        }
      };
    });

    if (result.success) {
      console.log('✅ 转账事务成功');
      return true;
    } else {
      console.log('❌ 转账事务失败:', result.message);
      return false;
    }
  }

  /**
   * 测试2：失败的转账事务（验证回滚）
   */
  async testFailedTransaction() {
    console.log('\n📝 测试2：失败的转账事务（验证回滚）');
    
    // 先查询当前余额
    const beforeA = await TestAccount.findOne({ username: 'testUserA' });
    const beforeB = await TestAccount.findOne({ username: 'testUserB' });
    
    console.log(`   转账前A余额: ${beforeA.balance}`);
    console.log(`   转账前B余额: ${beforeB.balance}`);

    const result = await TransactionManager.execute(async (session) => {
      // 尝试转账一个过大的金额
      const transferAmount = 2000;
      
      // 扣除A的余额（这里会失败）
      const updatedA = await TestAccount.findOneAndUpdate(
        { username: 'testUserA', balance: { $gte: transferAmount } },
        { $inc: { balance: -transferAmount, version: 1 } },
        { session, new: true }
      );

      if (!updatedA) {
        console.log('   检测到余额不足，返回失败结果');
        return { success: false, message: '余额不足，事务将回滚', code: 'INSUFFICIENT_BALANCE' };
      }

      // 这部分不应该执行到
      return { success: true, data: {} };
    });

    // 验证回滚效果
    const afterA = await TestAccount.findOne({ username: 'testUserA' });
    const afterB = await TestAccount.findOne({ username: 'testUserB' });
    
    console.log(`   转账后A余额: ${afterA.balance}`);
    console.log(`   转账后B余额: ${afterB.balance}`);

    if (result.success === false) {
      console.log('✅ 事务正确失败:', result.message);
      
      // 验证余额未被修改
      if (beforeA.balance === afterA.balance && beforeB.balance === afterB.balance) {
        console.log('✅ 事务回滚验证成功，余额未被错误修改');
        return true;
      } else {
        console.log('❌ 事务回滚验证失败，余额被错误修改');
        return false;
      }
    } else {
      console.log('❌ 事务应该失败但却成功了');
      return false;
    }
  }

  /**
   * 测试3：并发事务测试
   */
  async testConcurrentTransactions() {
    console.log('\n📝 测试3：并发事务测试');
    
    const promises = [];
    
    // 同时发起多个小额转账
    for (let i = 0; i < 5; i++) {
      const promise = TransactionManager.execute(async (session) => {
        const transferAmount = 10;
        
        const updatedA = await TestAccount.findOneAndUpdate(
          { username: 'testUserA', balance: { $gte: transferAmount } },
          { $inc: { balance: -transferAmount, version: 1 } },
          { session, new: true }
        );

        if (!updatedA) {
          return { success: false, message: '余额不足', code: 'INSUFFICIENT_BALANCE' };
        }

        const updatedB = await TestAccount.findOneAndUpdate(
          { username: 'testUserB' },
          { $inc: { balance: transferAmount, version: 1 } },
          { session, new: true }
        );

        return {
          success: true,
          data: { amount: transferAmount, transactionId: i }
        };
      });
      
      promises.push(promise);
    }

    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;
    
    console.log(`   成功事务: ${successCount}`);
    console.log(`   失败事务: ${failCount}`);
    
    // 验证最终余额
    const finalA = await TestAccount.findOne({ username: 'testUserA' });
    const finalB = await TestAccount.findOne({ username: 'testUserB' });
    
    console.log(`   最终A余额: ${finalA.balance}`);
    console.log(`   最终B余额: ${finalB.balance}`);
    
    console.log('✅ 并发事务测试完成');
    return true;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始直接事务组件测试...');
    
    try {
      await this.connect();
      await this.cleanup();
      
      const test1 = await this.testSuccessfulTransaction();
      const test2 = await this.testFailedTransaction();
      const test3 = await this.testConcurrentTransactions();
      
      await this.cleanup();
      
      console.log('\n📊 测试结果汇总:');
      console.log(`   成功转账事务: ${test1 ? '✅' : '❌'}`);
      console.log(`   失败回滚验证: ${test2 ? '✅' : '❌'}`);
      console.log(`   并发事务测试: ${test3 ? '✅' : '❌'}`);
      
      if (test1 && test2 && test3) {
        console.log('\n🎉 所有测试通过！事务组件工作正常！');
      } else {
        console.log('\n⚠️ 部分测试失败，需要检查事务组件');
      }
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    } finally {
      await this.disconnect();
    }
  }
}

// 运行测试
async function main() {
  const tester = new DirectTransactionTester();
  await tester.runAllTests();
}

main().catch(console.error);
