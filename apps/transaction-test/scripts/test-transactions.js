/**
 * 事务组件测试脚本
 * 
 * 测试场景：
 * 1. 创建测试账户
 * 2. 成功的转账事务
 * 3. 失败的转账事务（验证回滚）
 * 4. 查询余额验证
 * 5. 清理测试数据
 */

const net = require('net');

class TransactionTester {
  constructor(host = '127.0.0.1', port = 3010) {
    this.host = host;
    this.port = port;
  }

  /**
   * 发送微服务请求
   */
  async sendRequest(pattern, data = {}) {
    return new Promise((resolve, reject) => {
      const client = net.createConnection({ host: this.host, port: this.port });
      
      const request = {
        pattern,
        data,
        id: Math.random().toString(36).substr(2, 9)
      };

      client.write(JSON.stringify(request) + '\n');

      let responseData = '';
      client.on('data', (chunk) => {
        responseData += chunk.toString();
      });

      client.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          resolve(response);
        } catch (error) {
          reject(new Error('Invalid JSON response: ' + responseData));
        }
      });

      client.on('error', (error) => {
        reject(error);
      });

      setTimeout(() => {
        client.destroy();
        reject(new Error('Request timeout'));
      }, 10000);
    });
  }

  /**
   * 运行完整测试套件
   */
  async runTests() {
    console.log('🚀 开始事务组件测试...\n');

    try {
      // 测试1：创建测试账户
      console.log('📝 测试1：创建测试账户');
      const createResult = await this.sendRequest('transaction-test.createAccounts');
      console.log('结果:', JSON.stringify(createResult, null, 2));
      
      if (!createResult.success) {
        console.log('❌ 创建账户失败，跳过后续测试');
        return;
      }
      console.log('✅ 创建账户成功\n');

      // 测试2：查询初始余额
      console.log('📝 测试2：查询初始余额');
      const balanceA = await this.sendRequest('transaction-test.getBalance', { username: 'testUserA' });
      const balanceB = await this.sendRequest('transaction-test.getBalance', { username: 'testUserB' });
      console.log('账户A余额:', balanceA.data);
      console.log('账户B余额:', balanceB.data);
      console.log('✅ 查询余额成功\n');

      // 测试3：成功的转账事务
      console.log('📝 测试3：成功的转账事务（A转给B 200元）');
      const successTransfer = await this.sendRequest('transaction-test.successfulTransfer', {
        fromUsername: 'testUserA',
        toUsername: 'testUserB',
        amount: 200
      });
      console.log('转账结果:', JSON.stringify(successTransfer, null, 2));
      
      if (successTransfer.success) {
        console.log('✅ 转账成功');
        console.log(`   A账户余额: ${successTransfer.data.fromAccount.balance}`);
        console.log(`   B账户余额: ${successTransfer.data.toAccount.balance}`);
      } else {
        console.log('❌ 转账失败:', successTransfer.message);
      }
      console.log('');

      // 测试4：失败的转账事务（余额不足）
      console.log('📝 测试4：失败的转账事务（A转给B 2000元，余额不足）');
      const failedTransfer = await this.sendRequest('transaction-test.failedTransfer', {
        fromUsername: 'testUserA',
        toUsername: 'testUserB',
        amount: 2000
      });
      console.log('转账结果:', JSON.stringify(failedTransfer, null, 2));
      
      if (!failedTransfer.success) {
        console.log('✅ 转账失败（预期结果）:', failedTransfer.message);
      } else {
        console.log('❌ 转账成功（不应该成功）');
      }
      console.log('');

      // 测试5：验证余额未被错误修改
      console.log('📝 测试5：验证余额未被错误修改');
      const finalBalanceA = await this.sendRequest('transaction-test.getBalance', { username: 'testUserA' });
      const finalBalanceB = await this.sendRequest('transaction-test.getBalance', { username: 'testUserB' });
      console.log('最终账户A余额:', finalBalanceA.data);
      console.log('最终账户B余额:', finalBalanceB.data);
      
      // 验证余额正确性
      if (finalBalanceA.data.balance === 800 && finalBalanceB.data.balance === 700) {
        console.log('✅ 余额验证成功，事务回滚正常工作');
      } else {
        console.log('❌ 余额验证失败，事务回滚可能有问题');
      }
      console.log('');

      // 测试6：清理测试数据
      console.log('📝 测试6：清理测试数据');
      const cleanupResult = await this.sendRequest('transaction-test.cleanup');
      console.log('清理结果:', JSON.stringify(cleanupResult, null, 2));
      
      if (cleanupResult.success) {
        console.log('✅ 清理成功');
      } else {
        console.log('❌ 清理失败:', cleanupResult.message);
      }

      console.log('\n🎉 所有测试完成！');

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    }
  }

  /**
   * 单独测试转账
   */
  async testTransfer(fromUsername, toUsername, amount) {
    console.log(`🔄 测试转账: ${fromUsername} -> ${toUsername}, 金额: ${amount}`);
    
    try {
      const result = await this.sendRequest('transaction-test.successfulTransfer', {
        fromUsername,
        toUsername,
        amount
      });
      
      console.log('转账结果:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('转账测试失败:', error.message);
      return null;
    }
  }
}

// 运行测试
async function main() {
  const tester = new TransactionTester();
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 运行完整测试套件
    await tester.runTests();
  } else if (args[0] === 'transfer' && args.length === 4) {
    // 单独测试转账
    const [, fromUsername, toUsername, amount] = args;
    await tester.testTransfer(fromUsername, toUsername, parseInt(amount));
  } else {
    console.log('用法:');
    console.log('  node test-transactions.js                    # 运行完整测试');
    console.log('  node test-transactions.js transfer A B 100   # 测试转账');
  }
}

main().catch(console.error);
