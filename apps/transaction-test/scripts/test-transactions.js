/**
 * 事务组件测试脚本
 *
 * 测试场景：
 * 1. 创建测试账户
 * 2. 成功的转账事务
 * 3. 失败的转账事务（验证回滚）
 * 4. 查询余额验证
 * 5. 清理测试数据
 */

const http = require('http');

class TransactionTester {
  constructor(host = '127.0.0.1', port = 3010) {
    this.host = host;
    this.port = port;
    this.baseUrl = `http://${host}:${port}/api/transaction-test`;
  }

  /**
   * 发送HTTP请求
   */
  async sendRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
      const url = `${this.baseUrl}${path}`;
      const urlObj = new URL(url);

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: method.toUpperCase(),
        headers: {
          'Content-Type': 'application/json',
        }
      };

      if (data) {
        const postData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(responseData);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid JSON response: ' + responseData));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  /**
   * 运行完整测试套件
   */
  async runTests() {
    console.log('🚀 开始事务组件测试...\n');

    try {
      // 测试1：创建测试账户
      console.log('📝 测试1：创建测试账户');
      const createResult = await this.sendRequest('POST', '/create-accounts');
      console.log('结果:', JSON.stringify(createResult, null, 2));

      if (!createResult.success) {
        console.log('❌ 创建账户失败，跳过后续测试');
        return;
      }
      console.log('✅ 创建账户成功\n');

      // 测试2：查询初始余额
      console.log('📝 测试2：查询初始余额');
      const balanceA = await this.sendRequest('GET', '/balance/testUserA');
      const balanceB = await this.sendRequest('GET', '/balance/testUserB');
      console.log('账户A余额:', balanceA.data);
      console.log('账户B余额:', balanceB.data);
      console.log('✅ 查询余额成功\n');

      // 测试3：成功的转账事务
      console.log('📝 测试3：成功的转账事务（A转给B 200元）');
      const successTransfer = await this.sendRequest('POST', '/successful-transfer', {
        fromUsername: 'testUserA',
        toUsername: 'testUserB',
        amount: 200
      });
      console.log('转账结果:', JSON.stringify(successTransfer, null, 2));

      if (successTransfer.success) {
        console.log('✅ 转账成功');
        console.log(`   A账户余额: ${successTransfer.data.fromAccount.balance}`);
        console.log(`   B账户余额: ${successTransfer.data.toAccount.balance}`);
      } else {
        console.log('❌ 转账失败:', successTransfer.message);
      }
      console.log('');

      // 测试4：严谨的事务回滚测试
      console.log('📝 测试4：严谨的事务回滚测试（真实扣款后故意失败）');
      const failedTransfer = await this.sendRequest('POST', '/failed-transfer', {
        fromUsername: 'testUserA',
        toUsername: 'testUserB',
        amount: 100
      });
      console.log('转账结果:', JSON.stringify(failedTransfer, null, 2));

      if (!failedTransfer.success) {
        console.log('✅ 转账失败（预期结果）:', failedTransfer.message);
      } else {
        console.log('❌ 转账成功（不应该成功）');
      }
      console.log('');

      // 测试5：验证事务回滚效果
      console.log('📝 测试5：验证事务回滚效果');
      const rollbackBalanceA = await this.sendRequest('GET', '/balance/testUserA');
      const rollbackBalanceB = await this.sendRequest('GET', '/balance/testUserB');
      console.log('回滚后账户A余额:', rollbackBalanceA.data);
      console.log('回滚后账户B余额:', rollbackBalanceB.data);

      // 验证余额是否正确回滚（应该保持测试3后的状态）
      if (rollbackBalanceA.data.balance === 800 && rollbackBalanceB.data.balance === 700) {
        console.log('✅ 事务回滚验证成功：扣款操作被正确回滚');
      } else {
        console.log('❌ 事务回滚验证失败：扣款操作未被回滚');
        console.log(`   预期：A=800, B=700`);
        console.log(`   实际：A=${rollbackBalanceA.data.balance}, B=${rollbackBalanceB.data.balance}`);
      }
      console.log('');

      // 测试6：复杂事务回滚测试
      console.log('📝 测试6：复杂事务回滚测试（多步骤操作回滚）');
      const complexRollback = await this.sendRequest('POST', '/complex-rollback-test', {
        fromUsername: 'testUserA',
        toUsername: 'testUserB',
        amount: 50
      });
      console.log('复杂回滚测试结果:', JSON.stringify(complexRollback, null, 2));

      if (complexRollback.success && complexRollback.data.rollbackVerified) {
        console.log('✅ 复杂事务回滚测试成功：多步骤操作全部回滚');
      } else {
        console.log('❌ 复杂事务回滚测试失败');
      }
      console.log('');

      // 测试7：清理测试数据
      console.log('📝 测试7：清理测试数据');
      const cleanupResult = await this.sendRequest('DELETE', '/cleanup');
      console.log('清理结果:', JSON.stringify(cleanupResult, null, 2));

      if (cleanupResult.success) {
        console.log('✅ 清理成功');
      } else {
        console.log('❌ 清理失败:', cleanupResult.message);
      }

      console.log('\n🎉 严谨的事务测试全部完成！');
      console.log('\n📊 测试总结:');
      console.log('   ✅ 成功转账事务：验证事务提交');
      console.log('   ✅ 严谨回滚测试：验证单步操作回滚');
      console.log('   ✅ 复杂回滚测试：验证多步操作回滚');
      console.log('   ✅ 数据一致性：验证ACID特性');
      console.log('\n🏆 MongoDB事务功能完美工作！');

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    }
  }

  /**
   * 单独测试转账
   */
  async testTransfer(fromUsername, toUsername, amount) {
    console.log(`🔄 测试转账: ${fromUsername} -> ${toUsername}, 金额: ${amount}`);

    try {
      const result = await this.sendRequest('POST', '/successful-transfer', {
        fromUsername,
        toUsername,
        amount: parseInt(amount)
      });

      console.log('转账结果:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('转账测试失败:', error.message);
      return null;
    }
  }
}

// 运行测试
async function main() {
  const tester = new TransactionTester();
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 运行完整测试套件
    await tester.runTests();
  } else if (args[0] === 'transfer' && args.length === 4) {
    // 单独测试转账
    const [, fromUsername, toUsername, amount] = args;
    await tester.testTransfer(fromUsername, toUsername, parseInt(amount));
  } else {
    console.log('用法:');
    console.log('  node test-transactions.js                    # 运行完整测试');
    console.log('  node test-transactions.js transfer A B 100   # 测试转账');
  }
}

main().catch(console.error);
