/**
 * 事务组件测试脚本
 *
 * 测试场景：
 * 1. 创建测试账户
 * 2. 成功的转账事务
 * 3. 失败的转账事务（验证回滚）
 * 4. 查询余额验证
 * 5. 清理测试数据
 */

const http = require('http');

class TransactionTester {
  constructor(host = '127.0.0.1', port = 3010) {
    this.host = host;
    this.port = port;
    this.baseUrl = `http://${host}:${port}/api/transaction-test`;

    // 生成唯一的测试账号，避免冲突
    this.timestamp = Date.now();
    this.testUserA = `testUser_A_${this.timestamp}`;
    this.testUserB = `testUser_B_${this.timestamp}`;

    console.log(`🔧 本次测试使用账号: ${this.testUserA}, ${this.testUserB}`);
  }

  /**
   * 发送HTTP请求
   */
  async sendRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
      const url = `${this.baseUrl}${path}`;
      const urlObj = new URL(url);

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: method.toUpperCase(),
        headers: {
          'Content-Type': 'application/json',
        }
      };

      if (data) {
        const postData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(responseData);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid JSON response: ' + responseData));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  /**
   * 运行完整测试套件
   */
  async runTests() {
    console.log('🚀 开始事务组件测试...\n');

    try {
      // 测试1：创建测试账户
      console.log('📝 测试1：创建测试账户');
      const createResult = await this.sendRequest('POST', '/create-accounts',{
        usernameA: this.testUserA,
        usernameB: this.testUserB
      });
      console.log('结果:', JSON.stringify(createResult, null, 2));

      if (!createResult.success) {
        console.log('❌ 创建账户失败，跳过后续测试');
        return;
      }
      console.log('✅ 创建账户成功\n');

      // 测试2：查询初始余额
      console.log('📝 测试2：查询初始余额');
      const balanceA = await this.sendRequest('GET', `/balance/${this.testUserA}`);
      const balanceB = await this.sendRequest('GET', `/balance/${this.testUserB}`);
      console.log('账户A余额:', balanceA.data);
      console.log('账户B余额:', balanceB.data);
      console.log('✅ 查询余额成功\n');

      // 测试3：成功的转账事务
      console.log('📝 测试3：成功的转账事务（A转给B 200元）');
      const successTransfer = await this.sendRequest('POST', '/successful-transfer', {
        fromUsername: this.testUserA,
        toUsername: this.testUserB,
        amount: 200
      });
      console.log('转账结果:', JSON.stringify(successTransfer, null, 2));

      if (successTransfer.success) {
        console.log('✅ 转账成功');
        console.log(`   A账户余额: ${successTransfer.data.fromAccount.balance}`);
        console.log(`   B账户余额: ${successTransfer.data.toAccount.balance}`);
      } else {
        console.log('❌ 转账失败:', successTransfer.message);
      }
      console.log('');

      // 测试4：严谨的事务回滚测试
      console.log('📝 测试4：严谨的事务回滚测试（真实扣款后故意失败）');
      const failedTransfer = await this.sendRequest('POST', '/failed-transfer', {
        fromUsername: this.testUserA,
        toUsername: this.testUserB,
        amount: 100
      });
      console.log('转账结果:', JSON.stringify(failedTransfer, null, 2));

      if (!failedTransfer.success) {
        console.log('✅ 转账失败（预期结果）:', failedTransfer.message);
      } else {
        console.log('❌ 转账成功（不应该成功）');
      }
      console.log('');

      // 测试5：验证事务回滚效果
      console.log('📝 测试5：验证事务回滚效果');
      const rollbackBalanceA = await this.sendRequest('GET', `/balance/${this.testUserA}`);
      const rollbackBalanceB = await this.sendRequest('GET', `/balance/${this.testUserB}`);
      console.log('回滚后账户A余额:', rollbackBalanceA.data);
      console.log('回滚后账户B余额:', rollbackBalanceB.data);

      // 验证余额是否正确回滚（应该保持测试3后的状态）
      if (rollbackBalanceA.data.balance === 800 && rollbackBalanceB.data.balance === 700) {
        console.log('✅ 事务回滚验证成功：扣款操作被正确回滚');
      } else {
        console.log('❌ 事务回滚验证失败：扣款操作未被回滚');
        console.log(`   预期：A=800, B=700`);
        console.log(`   实际：A=${rollbackBalanceA.data.balance}, B=${rollbackBalanceB.data.balance}`);
      }
      console.log('');

      // 测试6：复杂事务回滚测试
      console.log('📝 测试6：复杂事务回滚测试（多步骤操作回滚）');
      const complexRollback = await this.sendRequest('POST', '/complex-rollback-test', {
        fromUsername: this.testUserA,
        toUsername: this.testUserB,
        amount: 50
      });
      console.log('复杂回滚测试结果:', JSON.stringify(complexRollback, null, 2));

      if (complexRollback.success && complexRollback.data.rollbackVerified) {
        console.log('✅ 复杂事务回滚测试成功：多步骤操作全部回滚');
      } else {
        console.log('❌ 复杂事务回滚测试失败');
      }
      console.log('');

      // 测试7：清理测试数据
      // console.log('📝 测试7：清理测试数据');
      // const cleanupResult = await this.sendRequest('DELETE', '/cleanup');
      // console.log('清理结果:', JSON.stringify(cleanupResult, null, 2));
      //
      // if (cleanupResult.success) {
      //   console.log('✅ 清理成功');
      // } else {
      //   console.log('❌ 清理失败:', cleanupResult.message);
      // }

      // 测试8：初始化购买任务
      console.log('📝 测试8：初始化购买任务');
      const initQuestResult = await this.sendRequest('POST', '/init-purchase-quest', {
        username: this.testUserA,
        questId: 'purchase_quest',
        targetPurchases: 3
      });
      console.log('初始化任务结果:', JSON.stringify(initQuestResult, null, 2));

      if (initQuestResult.success) {
        console.log('✅ 购买任务初始化成功');
      } else {
        console.log('❌ 购买任务初始化失败:', initQuestResult.message);
      }
      console.log('');

      // 测试9：复杂购买道具事务（成功案例）
      console.log('📝 测试9：复杂购买道具事务（扣除货币+添加道具+更新任务进度）');
      const complexPurchaseResult = await this.sendRequest('POST', '/complex-purchase', {
        username: this.testUserA,
        itemId: 'sword_001',
        itemName: '钢铁长剑',
        itemPrice: 150,
        questId: 'purchase_quest'
      });
      console.log('复杂购买结果:', JSON.stringify(complexPurchaseResult, null, 2));

      if (complexPurchaseResult.success) {
        console.log('✅ 复杂购买事务成功');
        const data = complexPurchaseResult.data;
        console.log(`   货币变化: ${data.transaction.originalBalance} -> ${data.transaction.finalBalance}`);
        console.log(`   道具获得: ${data.item.itemName} x${data.item.quantity}`);
        console.log(`   任务进度: ${data.quest.currentProgress}/${data.quest.targetProgress}`);
        if (data.quest.justCompleted) {
          console.log(`   🎉 任务完成！获得奖励: ${data.transaction.rewardReceived}金币`);
        }
      } else {
        console.log('❌ 复杂购买事务失败:', complexPurchaseResult.message);
      }
      console.log('');

      // 测试10：再次购买道具（测试任务进度累积）
      console.log('📝 测试10：再次购买道具（测试任务进度累积）');
      const secondPurchaseResult = await this.sendRequest('POST', '/complex-purchase', {
        username: this.testUserA,
        itemId: 'shield_001',
        itemName: '铁制盾牌',
        itemPrice: 100,
        questId: 'purchase_quest'
      });
      console.log('第二次购买结果:', JSON.stringify(secondPurchaseResult, null, 2));

      if (secondPurchaseResult.success) {
        console.log('✅ 第二次购买成功');
        const data = secondPurchaseResult.data;
        console.log(`   任务进度: ${data.quest.currentProgress}/${data.quest.targetProgress}`);
        if (data.quest.justCompleted) {
          console.log(`   🎉 任务完成！获得奖励: ${data.transaction.rewardReceived}金币`);
        }
      } else {
        console.log('❌ 第二次购买失败:', secondPurchaseResult.message);
      }
      console.log('');

      // 测试11：故意失败的复杂购买事务（验证回滚）
      console.log('📝 测试11：故意失败的复杂购买事务（验证多步骤回滚）');

      // 先记录当前状态
      const beforeFailedPurchase = await this.sendRequest('GET', `/balance/${this.testUserA}`);
      console.log('失败测试前余额:', beforeFailedPurchase.data);
      const beforeFailedItem = await this.sendRequest('GET', `/item/${this.testUserA}/potion_001`);
      console.log('失败测试前道具数量:', beforeFailedItem.data);
      const beforeFailedQuest = await this.sendRequest('GET', `/quest/${this.testUserA}/purchase_quest`);
      console.log('失败测试前任务进度:', beforeFailedQuest.data);

      const failedComplexResult = await this.sendRequest('POST', '/failed-complex-purchase', {
        username: this.testUserA,
        itemId: 'potion_001',
        itemName: '治疗药水',
        itemPrice: 50
      });
      console.log('故意失败购买结果:', JSON.stringify(failedComplexResult, null, 2));

      if (!failedComplexResult.success) {
        console.log('✅ 购买正确失败（预期结果）:', failedComplexResult.message);

        // 验证回滚效果
        const afterFailedPurchase = await this.sendRequest('GET', `/balance/${this.testUserA}`);
        console.log('失败测试后余额:', afterFailedPurchase.data);
        const afterFailedItem = await this.sendRequest('GET', `/item/${this.testUserA}/potion_001`);
        console.log('失败测试后道具数量:', afterFailedItem.data);
        const afterFailedQuest = await this.sendRequest('GET', `/quest/${this.testUserA}/purchase_quest`);
        console.log('失败测试后任务进度:', afterFailedQuest.data);

        if (beforeFailedPurchase.data.balance === afterFailedPurchase.data.balance) {
          console.log('✅ 事务回滚验证成功：余额未被错误扣除');
        } else {
          console.log('❌ 事务回滚验证失败：余额被错误修改');
        }
      } else {
        console.log('❌ 购买应该失败但却成功了');
      }
      console.log('');

      console.log('\n🎉 严谨的事务测试全部完成！');
      console.log('\n📊 测试总结:');
      console.log('   ✅ 成功转账事务：验证事务提交');
      console.log('   ✅ 严谨回滚测试：验证单步操作回滚');
      console.log('   ✅ 复杂回滚测试：验证多步操作回滚');
      console.log('   ✅ 复杂购买事务：验证多表操作的ACID特性');
      console.log('   ✅ 任务系统集成：验证业务逻辑完整性');
      console.log('   ✅ 数据一致性：验证所有ACID特性');
      console.log('\n🏆 MongoDB事务功能完美工作！同一数据库多表事务完全支持！');

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    }
  }

  /**
   * 单独测试转账
   */
  async testTransfer(fromUsername, toUsername, amount) {
    console.log(`🔄 测试转账: ${fromUsername} -> ${toUsername}, 金额: ${amount}`);

    try {
      const result = await this.sendRequest('POST', '/successful-transfer', {
        fromUsername,
        toUsername,
        amount: parseInt(amount)
      });

      console.log('转账结果:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('转账测试失败:', error.message);
      return null;
    }
  }
}

// 运行测试
async function main() {
  const tester = new TransactionTester();
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 运行完整测试套件
    await tester.runTests();
  } else if (args[0] === 'transfer' && args.length === 4) {
    // 单独测试转账
    const [, fromUsername, toUsername, amount] = args;
    await tester.testTransfer(fromUsername, toUsername, parseInt(amount));
  } else {
    console.log('用法:');
    console.log('  node test-transactions.js                    # 运行完整测试');
    console.log('  node test-transactions.js transfer A B 100   # 测试转账');
  }
}

main().catch(console.error);
