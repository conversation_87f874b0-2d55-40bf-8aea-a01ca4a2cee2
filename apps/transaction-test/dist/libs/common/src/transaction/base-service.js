"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
const common_1 = require("@nestjs/common");
const result_type_1 = require("../types/result.type");
const transaction_manager_1 = require("./transaction-manager");
class BaseService {
    constructor(loggerContext) {
        this.logger = new common_1.Logger(loggerContext);
    }
    async executeTransaction(operation, options) {
        try {
            this.logger.log('开始执行事务');
            const result = await transaction_manager_1.TransactionManager.execute(operation, options);
            if (result_type_1.ResultUtils.isSuccess(result)) {
                this.logger.log('事务执行成功');
            }
            else {
                this.logger.warn('事务执行失败', { code: result.code, message: result.message });
            }
            return result;
        }
        catch (error) {
            this.logger.error('事务执行异常', error);
            return result_type_1.ResultUtils.error(error.message || '事务执行异常', 'TRANSACTION_EXCEPTION');
        }
    }
    isFailure(result) {
        return result_type_1.ResultUtils.isFailure(result);
    }
    isSuccess(result) {
        return result_type_1.ResultUtils.isSuccess(result);
    }
    propagateError(result) {
        return result_type_1.ResultUtils.isFailure(result) ? result : null;
    }
    checkResults(results) {
        for (const result of results) {
            if (result_type_1.ResultUtils.isFailure(result)) {
                return result;
            }
        }
        return null;
    }
    validateCondition(condition, errorMessage, errorCode = 'VALIDATION_ERROR') {
        return condition ? null : result_type_1.ResultUtils.error(errorMessage, errorCode);
    }
    validateExists(data, errorMessage, errorCode = 'NOT_FOUND') {
        return data ? null : result_type_1.ResultUtils.error(errorMessage, errorCode);
    }
    logOperation(operation, data) {
        this.logger.log(`业务操作: ${operation}`, data);
    }
    logError(operation, error) {
        this.logger.error(`业务操作失败: ${operation}`, error);
    }
    logWarning(operation, message, data) {
        this.logger.warn(`业务警告: ${operation} - ${message}`, data);
    }
    success(data, message) {
        return result_type_1.ResultUtils.ok(data);
    }
    error(message, code = 'BUSINESS_ERROR') {
        return result_type_1.ResultUtils.error(message, code);
    }
    empty() {
        return result_type_1.ResultUtils.empty();
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base-service.js.map