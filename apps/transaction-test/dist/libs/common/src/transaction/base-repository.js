"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
const common_1 = require("@nestjs/common");
const transaction_manager_1 = require("./transaction-manager");
class BaseRepository {
    constructor(model, loggerContext) {
        this.model = model;
        this.logger = new common_1.Logger(loggerContext);
    }
    async findById(id, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.findById(id, null, options).exec();
        })(session);
    }
    async find(filter = {}, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.find(filter, null, options).exec();
        })(session);
    }
    async findOne(filter, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.findOne(filter, null, options).exec();
        })(session);
    }
    async create(data, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            const docs = await this.model.create([data], options);
            return docs[0];
        })(session);
    }
    async updateById(id, update, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session, new: true } : { new: true };
            return await this.model.findByIdAndUpdate(id, { $set: update }, options).exec();
        })(session);
    }
    async deleteById(id, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            const result = await this.model.deleteOne({ _id: id }, options).exec();
            return result.deletedCount > 0;
        })(session);
    }
    async exists(filter, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            const count = await this.model.countDocuments(filter, options).exec();
            return count > 0;
        })(session);
    }
    async count(filter = {}, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.countDocuments(filter, options).exec();
        })(session);
    }
    async createMany(dataList, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.create(dataList, options);
        })(session);
    }
    async bulkWrite(operations, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session } : {};
            return await this.model.bulkWrite(operations, options);
        })(session);
    }
    wrapOperation(operation) {
        return transaction_manager_1.TransactionUtils.wrapOperation(operation);
    }
    logOperation(operation, data) {
        this.logger.log(`${operation}`, data);
    }
    logError(operation, error) {
        this.logger.error(`${operation} failed`, error);
    }
}
exports.BaseRepository = BaseRepository;
//# sourceMappingURL=base-repository.js.map