{"version": 3, "file": "transaction-manager.js", "sourceRoot": "", "sources": ["../../../../../../../libs/common/src/transaction/transaction-manager.ts"], "names": [], "mappings": ";;;AACA,sDAA2D;AA4B3D,MAAM,qBAAqB,GAAG;IAC5B,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;CACJ,CAAC;AAsBF,MAAa,kBAAkB;IAM7B,MAAM,CAAC,UAAU,CAAC,gBAAqB;QACrC,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;IACnC,CAAC;IASD,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,SAAkC,EAClC,UAA8B,EAAE;QAEhC,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEjD,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC9D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAGzD,IAAI,yBAAW,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpE,OAAO,MAAM,CAAC;YAChB,CAAC;YAGD,IAAI,OAAO,KAAK,MAAM,CAAC,UAAU,EAAE,CAAC;gBAClC,OAAO,MAAM,CAAC;YAChB,CAAC;YAGD,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,yBAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;IAC3D,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,WAAW,CAC9B,SAAkC,EAClC,OAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,yBAAW,CAAC,KAAK,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;QAEnD,IAAI,CAAC;YAEH,OAAO,CAAC,gBAAgB,CAAC;gBACvB,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,WAAW,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE;gBAC3C,YAAY,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE;aAC1C,CAAC,CAAC;YAGH,MAAM,cAAc,GAAG,IAAI,OAAO,CAAY,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBAC1D,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAGH,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC;YAGtE,IAAI,yBAAW,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC;YAChB,CAAC;YAGD,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACjC,OAAO,yBAAW,CAAC,KAAK,CACtB,KAAK,CAAC,OAAO,IAAI,QAAQ,EACzB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CACzB,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,OAAO,CAAC,UAAU,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,mBAAmB,CAAC,OAA2B;QAC5D,OAAO;YACL,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC;YACnC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,IAAI;YACtC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;YACjC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,SAAS;YACnD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO;YAC3C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,UAAU;SACjD,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAAC,MAAmB;QACjD,IAAI,yBAAW,CAAC,SAAS,CAAC,MAAM,CAAC;YAAE,OAAO,KAAK,CAAC;QAEhD,MAAM,cAAc,GAAG;YACrB,sBAAsB;YACtB,gBAAgB;YAChB,eAAe;YACf,eAAe;SAChB,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAKO,MAAM,CAAC,YAAY,CAAC,KAAU;QACpC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;gBACnC,KAAK,GAAG,CAAC,CAAC,OAAO,gBAAgB,CAAC;gBAClC,KAAK,GAAG,CAAC,CAAC,OAAO,sBAAsB,CAAC;gBACxC,KAAK,GAAG,CAAC,CAAC,OAAO,qBAAqB,CAAC;gBACvC,OAAO,CAAC,CAAC,OAAO,iBAAiB,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,EAAU;QAC7B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAnJD,gDAmJC;AAKD,MAAa,gBAAgB;IAK3B,MAAM,CAAC,cAAc,CAAO,MAAiB;QAC3C,OAAO,yBAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAE,MAAoB,CAAC,CAAC,CAAC,IAAI,CAAC;IACtE,CAAC;IAMD,MAAM,CAAC,YAAY,CAAI,OAAsB;QAC3C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,yBAAW,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,MAAmB,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAMD,MAAM,CAAC,aAAa,CAClB,SAAkD;QAElD,OAAO,KAAK,EAAE,OAAuB,EAAE,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;gBACxC,OAAO,yBAAW,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,yBAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,EAAE,iBAAiB,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF;AAtCD,4CAsCC"}