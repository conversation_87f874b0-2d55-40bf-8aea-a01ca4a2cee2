"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionUtils = exports.TransactionManager = void 0;
const result_type_1 = require("../types/result.type");
const RETRYABLE_ERROR_CODES = [
    11000,
    112,
    244,
    251
];
class TransactionManager {
    static initialize(mongooseInstance) {
        this.mongoose = mongooseInstance;
    }
    static async execute(operation, options = {}) {
        const config = this.mergeDefaultOptions(options);
        for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
            const result = await this.executeOnce(operation, config);
            if (result_type_1.ResultUtils.isSuccess(result) || !this.isRetryableError(result)) {
                return result;
            }
            if (attempt === config.maxRetries) {
                return result;
            }
            await this.delay(config.retryDelay * attempt);
        }
        return result_type_1.ResultUtils.error('事务执行失败', 'TRANSACTION_FAILED');
    }
    static async executeOnce(operation, options) {
        if (!this.mongoose) {
            return result_type_1.ResultUtils.error('Mongoose未初始化', 'MONGOOSE_NOT_INITIALIZED');
        }
        const session = await this.mongoose.startSession();
        try {
            session.startTransaction({
                readPreference: options.readPreference,
                readConcern: { level: options.readConcern },
                writeConcern: { w: options.writeConcern }
            });
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('事务超时')), options.timeout);
            });
            const operationPromise = operation(session);
            const result = await Promise.race([operationPromise, timeoutPromise]);
            if (result_type_1.ResultUtils.isFailure(result)) {
                await session.abortTransaction();
                return result;
            }
            await session.commitTransaction();
            return result;
        }
        catch (error) {
            await session.abortTransaction();
            return result_type_1.ResultUtils.error(error.message || '事务执行异常', this.getErrorCode(error));
        }
        finally {
            session.endSession();
        }
    }
    static mergeDefaultOptions(options) {
        return {
            maxRetries: options.maxRetries ?? 3,
            retryDelay: options.retryDelay ?? 1000,
            timeout: options.timeout ?? 30000,
            readPreference: options.readPreference ?? 'primary',
            readConcern: options.readConcern ?? 'local',
            writeConcern: options.writeConcern ?? 'majority'
        };
    }
    static isRetryableError(result) {
        if (result_type_1.ResultUtils.isSuccess(result))
            return false;
        const retryableCodes = [
            'TRANSACTION_CONFLICT',
            'WRITE_CONFLICT',
            'DUPLICATE_KEY',
            'NETWORK_ERROR'
        ];
        return retryableCodes.includes(result.code);
    }
    static getErrorCode(error) {
        if (RETRYABLE_ERROR_CODES.includes(error.code)) {
            switch (error.code) {
                case 11000: return 'DUPLICATE_KEY';
                case 112: return 'WRITE_CONFLICT';
                case 244: return 'TRANSACTION_CONFLICT';
                case 251: return 'TRANSACTION_ABORTED';
                default: return 'RETRYABLE_ERROR';
            }
        }
        return 'TRANSACTION_ERROR';
    }
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.TransactionManager = TransactionManager;
class TransactionUtils {
    static propagateError(result) {
        return result_type_1.ResultUtils.isFailure(result) ? result : null;
    }
    static checkResults(results) {
        for (const result of results) {
            if (result_type_1.ResultUtils.isFailure(result)) {
                return result;
            }
        }
        return null;
    }
    static wrapOperation(operation) {
        return async (session) => {
            try {
                const result = await operation(session);
                return result_type_1.ResultUtils.ok(result);
            }
            catch (error) {
                return result_type_1.ResultUtils.error(error.message || '操作失败', 'OPERATION_ERROR');
            }
        };
    }
}
exports.TransactionUtils = TransactionUtils;
//# sourceMappingURL=transaction-manager.js.map