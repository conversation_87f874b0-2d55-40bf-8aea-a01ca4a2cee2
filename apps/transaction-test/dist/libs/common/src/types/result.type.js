"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MicroserviceResponseUtils = exports.ExceptionToResultUtils = exports.TransactionResultManager = exports.ServiceResultHandler = exports.RepositoryResultWrapper = exports.CommonErrorCodes = exports.ResultUtils = void 0;
class ResultUtils {
    static success(data, message, code) {
        return {
            success: true,
            data,
            message,
            code: code || 'SUCCESS'
        };
    }
    static failure(code, message, details) {
        return {
            success: false,
            code,
            message,
            data: null,
            details
        };
    }
    static ok(data) {
        return this.success(data);
    }
    static error(message, code = 'ERROR') {
        return this.failure(code, message);
    }
    static empty() {
        return this.success(undefined);
    }
    static paginatedSuccess(list, total, page, limit, message) {
        const pages = Math.ceil(total / limit);
        return this.success({
            list,
            total,
            page,
            limit,
            pages,
            hasNext: page < pages,
            hasPrev: page > 1
        }, message);
    }
    static isSuccess(result) {
        return result.success === true;
    }
    static isFailure(result) {
        return result.success === false;
    }
    static getDataOrDefault(result, defaultValue) {
        return this.isSuccess(result) ? result.data : defaultValue;
    }
    static getDataOrNull(result) {
        return this.isSuccess(result) ? result.data : null;
    }
    static unwrap(result) {
        if (this.isFailure(result)) {
            throw new Error(`Result unwrap failed: ${result.message} (${result.code})`);
        }
        return result.data;
    }
    static unwrapOr(result, onError) {
        return this.isSuccess(result) ? result.data : onError(result);
    }
    static unwrapOrThrow(result, errorMessage) {
        if (this.isFailure(result)) {
            throw new Error(errorMessage || result.message);
        }
        return result.data;
    }
    static async chain(result, fn) {
        if (this.isFailure(result)) {
            return result;
        }
        return await fn(result.data);
    }
    static map(result, fn) {
        if (this.isFailure(result)) {
            return result;
        }
        return this.success(fn(result.data), result.message, result.code);
    }
}
exports.ResultUtils = ResultUtils;
exports.CommonErrorCodes = {
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    INVALID_PARAMETER: 'INVALID_PARAMETER',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
    RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
    RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
    BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
    INSUFFICIENT_RESOURCES: 'INSUFFICIENT_RESOURCES',
    OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
    EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    TIMEOUT_ERROR: 'TIMEOUT_ERROR',
};
class RepositoryResultWrapper {
    static async wrap(operation) {
        try {
            const data = await operation();
            return ResultUtils.ok(data);
        }
        catch (error) {
            return ResultUtils.error(error.message || '数据库操作失败', 'DATABASE_ERROR');
        }
    }
    static async wrapNullable(operation) {
        return this.wrap(operation);
    }
    static async wrapArray(operation) {
        return this.wrap(operation);
    }
    static async wrapCount(operation) {
        return this.wrap(operation);
    }
    static async wrapBoolean(operation) {
        return this.wrap(operation);
    }
    static async wrapWithSession(operation) {
        return this.wrap(operation);
    }
}
exports.RepositoryResultWrapper = RepositoryResultWrapper;
class ServiceResultHandler {
    static checkAndExtract(result) {
        if (ResultUtils.isFailure(result)) {
            return null;
        }
        return result.data;
    }
    static checkOrFail(result, failureCode, failureMessage) {
        if (ResultUtils.isFailure(result)) {
            return ResultUtils.failure(failureCode, failureMessage);
        }
        return result;
    }
    static async chainChecks(checks, finalOperation) {
        for (const check of checks) {
            const result = await check();
            if (ResultUtils.isFailure(result)) {
                return result;
            }
        }
        return await finalOperation();
    }
    static propagateError(result) {
        return ResultUtils.isFailure(result) ? result : null;
    }
}
exports.ServiceResultHandler = ServiceResultHandler;
class TransactionResultManager {
    constructor() {
        this.logger = console;
    }
    static async executeTransaction(operation) {
        let mongoose;
        try {
            mongoose = require('mongoose');
        }
        catch (error) {
            return ResultUtils.error('Mongoose not available', 'MONGOOSE_NOT_FOUND');
        }
        const session = await mongoose.startSession();
        try {
            session.startTransaction();
            const result = await operation(session);
            if (ResultUtils.isFailure(result)) {
                await session.abortTransaction();
                return result;
            }
            await session.commitTransaction();
            return result;
        }
        catch (error) {
            await session.abortTransaction();
            console.error('Transaction failed:', error);
            return ResultUtils.error(error.message || '事务执行失败', 'TRANSACTION_ERROR');
        }
        finally {
            session.endSession();
        }
    }
    static async executeWithRetry(operation, maxRetries = 3) {
        let lastResult;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            lastResult = await this.executeTransaction(operation);
            if (ResultUtils.isSuccess(lastResult)) {
                return lastResult;
            }
            if (this.isRetryableError(lastResult) && attempt < maxRetries) {
                await this.delay(attempt * 1000);
                continue;
            }
            break;
        }
        return lastResult;
    }
    static isRetryableError(result) {
        if (ResultUtils.isSuccess(result))
            return false;
        const retryableCodes = [
            'TRANSACTION_ERROR',
            'DATABASE_ERROR',
            'NETWORK_ERROR'
        ];
        return retryableCodes.includes(result.code);
    }
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.TransactionResultManager = TransactionResultManager;
class ExceptionToResultUtils {
    static async wrapAsync(fn, errorCodeMap) {
        try {
            const data = await fn();
            return ResultUtils.success(data);
        }
        catch (error) {
            return this.handleError(error, errorCodeMap);
        }
    }
    static wrap(fn, errorCodeMap) {
        try {
            const data = fn();
            return ResultUtils.success(data);
        }
        catch (error) {
            return this.handleError(error, errorCodeMap);
        }
    }
    static handleError(error, errorCodeMap) {
        if (error && typeof error === 'object' && 'success' in error && error.success === false) {
            return error;
        }
        if (error && typeof error === 'object' && error.response) {
            const response = error.response;
            return ResultUtils.failure(response.code || error.name || 'HTTP_EXCEPTION', response.message || error.message || 'HTTP异常', {
                statusCode: error.status,
                originalError: error.name,
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
            });
        }
        if (error && error.name === 'MongoError') {
            return this.handleMongoError(error);
        }
        if (error && error.name === 'ValidationError') {
            return ResultUtils.failure('VALIDATION_ERROR', '数据验证失败', { validationErrors: error.errors });
        }
        if (errorCodeMap && error.message && errorCodeMap[error.message]) {
            return ResultUtils.failure(errorCodeMap[error.message], error.message, { originalError: error.name });
        }
        return ResultUtils.failure(error.code || error.name || exports.CommonErrorCodes.UNKNOWN_ERROR, error.message || '未知错误', {
            originalError: error.name,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
    static handleMongoError(error) {
        switch (error.code) {
            case 11000:
                return ResultUtils.failure(exports.CommonErrorCodes.RESOURCE_ALREADY_EXISTS, '数据已存在', { duplicateKey: error.keyValue });
            case 121:
                return ResultUtils.failure('DOCUMENT_VALIDATION_ERROR', '文档验证失败', { validationError: error.errInfo });
            default:
                return ResultUtils.failure('DATABASE_ERROR', error.message || '数据库操作失败', { mongoErrorCode: error.code });
        }
    }
}
exports.ExceptionToResultUtils = ExceptionToResultUtils;
class MicroserviceResponseUtils {
    static fromResult(result, requestId) {
        if (ResultUtils.isSuccess(result)) {
            return {
                code: 0,
                message: result.message || '操作成功',
                data: result.data,
                timestamp: Date.now(),
                requestId
            };
        }
        else {
            return {
                code: this.getErrorCode(result.code),
                message: result.message,
                data: null,
                timestamp: Date.now(),
                requestId
            };
        }
    }
    static success(data, message = '操作成功', requestId) {
        return {
            code: 0,
            message,
            data,
            timestamp: Date.now(),
            requestId
        };
    }
    static error(code, message, requestId) {
        return {
            code,
            message,
            data: null,
            timestamp: Date.now(),
            requestId
        };
    }
    static getErrorCode(code) {
        const errorCodeMap = {
            [exports.CommonErrorCodes.UNKNOWN_ERROR]: 1000,
            [exports.CommonErrorCodes.INVALID_PARAMETER]: 1001,
            [exports.CommonErrorCodes.PERMISSION_DENIED]: 1002,
            [exports.CommonErrorCodes.RESOURCE_NOT_FOUND]: 2000,
            [exports.CommonErrorCodes.RESOURCE_ALREADY_EXISTS]: 2001,
            [exports.CommonErrorCodes.RESOURCE_CONFLICT]: 2002,
            [exports.CommonErrorCodes.BUSINESS_RULE_VIOLATION]: 3000,
            [exports.CommonErrorCodes.INSUFFICIENT_RESOURCES]: 3001,
            [exports.CommonErrorCodes.OPERATION_NOT_ALLOWED]: 3002,
            [exports.CommonErrorCodes.EXTERNAL_SERVICE_ERROR]: 4000,
            [exports.CommonErrorCodes.NETWORK_ERROR]: 4001,
            [exports.CommonErrorCodes.TIMEOUT_ERROR]: 4002,
            'CHARACTER_NOT_FOUND': 5000,
            'CHARACTER_NAME_TAKEN': 5001,
            'CHARACTER_LEVEL_MAX': 5002,
            'CHARACTER_ENERGY_INSUFFICIENT': 5003,
            'CHARACTER_CASH_INSUFFICIENT': 5004,
            'CHARACTER_GOLD_INSUFFICIENT': 5005,
            'DATABASE_ERROR': 9000,
            'DOCUMENT_VALIDATION_ERROR': 9001,
            'VALIDATION_ERROR': 9002,
        };
        return errorCodeMap[code] || 1000;
    }
}
exports.MicroserviceResponseUtils = MicroserviceResponseUtils;
//# sourceMappingURL=result.type.js.map