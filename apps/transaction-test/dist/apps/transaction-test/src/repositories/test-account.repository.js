"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestAccountRepository = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const transaction_1 = require("../../../../libs/common/src/transaction");
const test_account_schema_1 = require("../schemas/test-account.schema");
let TestAccountRepository = class TestAccountRepository extends transaction_1.BaseRepository {
    constructor(testAccountModel) {
        super(testAccountModel, 'TestAccountRepository');
    }
    async findByUsername(username, session) {
        return this.findOne({ username }, session);
    }
    async deductBalance(username, amount, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session, new: true } : { new: true };
            return await this.model.findOneAndUpdate({ username, balance: { $gte: amount } }, { $inc: { balance: -amount, version: 1 } }, options).exec();
        })(session);
    }
    async addBalance(username, amount, session) {
        return this.wrapOperation(async (session) => {
            const options = session ? { session, new: true } : { new: true };
            return await this.model.findOneAndUpdate({ username }, { $inc: { balance: amount, version: 1 } }, options).exec();
        })(session);
    }
    async createTestAccount(username, initialBalance = 1000, session) {
        return this.create({
            username,
            balance: initialBalance,
            version: 0
        }, session);
    }
};
exports.TestAccountRepository = TestAccountRepository;
exports.TestAccountRepository = TestAccountRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(test_account_schema_1.TestAccount.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], TestAccountRepository);
//# sourceMappingURL=test-account.repository.js.map