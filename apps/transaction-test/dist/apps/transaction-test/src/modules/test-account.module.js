"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestAccountModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const test_account_schema_1 = require("../schemas/test-account.schema");
const test_account_repository_1 = require("../repositories/test-account.repository");
const transaction_test_service_1 = require("../services/transaction-test.service");
const transaction_test_controller_1 = require("../controllers/transaction-test.controller");
let TestAccountModule = class TestAccountModule {
};
exports.TestAccountModule = TestAccountModule;
exports.TestAccountModule = TestAccountModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: test_account_schema_1.TestAccount.name, schema: test_account_schema_1.TestAccountSchema }
            ])
        ],
        controllers: [transaction_test_controller_1.TransactionTestController],
        providers: [
            test_account_repository_1.TestAccountRepository,
            transaction_test_service_1.TransactionTestService
        ],
        exports: [
            test_account_repository_1.TestAccountRepository,
            transaction_test_service_1.TransactionTestService
        ]
    })
], TestAccountModule);
//# sourceMappingURL=test-account.module.js.map