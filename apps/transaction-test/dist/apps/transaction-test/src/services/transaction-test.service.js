"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionTestService = void 0;
const common_1 = require("@nestjs/common");
const transaction_1 = require("../../../../libs/common/src/transaction");
const test_account_repository_1 = require("../repositories/test-account.repository");
let TransactionTestService = class TransactionTestService extends transaction_1.BaseService {
    constructor(testAccountRepo) {
        super('TransactionTestService');
        this.testAccountRepo = testAccountRepo;
    }
    async testSuccessfulTransfer(fromUsername, toUsername, amount) {
        this.logOperation('开始测试成功转账', { fromUsername, toUsername, amount });
        return this.executeTransaction(async (session) => {
            const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
            if (this.isFailure(fromAccountResult)) {
                return fromAccountResult;
            }
            const fromAccount = fromAccountResult.data;
            if (!fromAccount) {
                return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
            }
            const toAccountResult = await this.testAccountRepo.findByUsername(toUsername, session);
            if (this.isFailure(toAccountResult)) {
                return toAccountResult;
            }
            const toAccount = toAccountResult.data;
            if (!toAccount) {
                return this.error('接收方账户不存在', 'TO_ACCOUNT_NOT_FOUND');
            }
            if (fromAccount.balance < amount) {
                return this.error('余额不足', 'INSUFFICIENT_BALANCE');
            }
            const deductResult = await this.testAccountRepo.deductBalance(fromUsername, amount, session);
            if (this.isFailure(deductResult)) {
                return deductResult;
            }
            const updatedFromAccount = deductResult.data;
            if (!updatedFromAccount) {
                return this.error('扣除余额失败', 'DEDUCT_FAILED');
            }
            const addResult = await this.testAccountRepo.addBalance(toUsername, amount, session);
            if (this.isFailure(addResult)) {
                return addResult;
            }
            const updatedToAccount = addResult.data;
            if (!updatedToAccount) {
                return this.error('增加余额失败', 'ADD_FAILED');
            }
            return this.success({
                fromAccount: updatedFromAccount,
                toAccount: updatedToAccount,
                amount,
                transferId: `transfer_${Date.now()}`
            });
        });
    }
    async testFailedTransfer(fromUsername, toUsername, amount) {
        this.logOperation('开始测试失败转账（余额不足）', { fromUsername, toUsername, amount });
        return this.executeTransaction(async (session) => {
            const fromAccountResult = await this.testAccountRepo.findByUsername(fromUsername, session);
            if (this.isFailure(fromAccountResult)) {
                return fromAccountResult;
            }
            const fromAccount = fromAccountResult.data;
            if (!fromAccount) {
                return this.error('发送方账户不存在', 'FROM_ACCOUNT_NOT_FOUND');
            }
            if (fromAccount.balance < amount) {
                return this.error('余额不足，事务将回滚', 'INSUFFICIENT_BALANCE');
            }
            return this.error('不应该到达这里', 'UNEXPECTED_ERROR');
        });
    }
    async createTestAccounts() {
        this.logOperation('创建测试账户');
        return this.executeTransaction(async (session) => {
            const accounts = [];
            const accountAResult = await this.testAccountRepo.createTestAccount('testUserA', 1000, session);
            if (this.isFailure(accountAResult)) {
                return accountAResult;
            }
            accounts.push(accountAResult.data);
            const accountBResult = await this.testAccountRepo.createTestAccount('testUserB', 500, session);
            if (this.isFailure(accountBResult)) {
                return accountBResult;
            }
            accounts.push(accountBResult.data);
            return this.success(accounts);
        });
    }
    async getAccountBalance(username) {
        const accountResult = await this.testAccountRepo.findByUsername(username);
        if (this.isFailure(accountResult)) {
            return accountResult;
        }
        const account = accountResult.data;
        if (!account) {
            return this.error('账户不存在', 'ACCOUNT_NOT_FOUND');
        }
        return this.success({
            username: account.username,
            balance: account.balance
        });
    }
    async cleanupTestData() {
        this.logOperation('清理测试数据');
        const accountAResult = await this.testAccountRepo.deleteById('testUserA');
        const accountBResult = await this.testAccountRepo.deleteById('testUserB');
        return this.empty();
    }
};
exports.TransactionTestService = TransactionTestService;
exports.TransactionTestService = TransactionTestService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [test_account_repository_1.TestAccountRepository])
], TransactionTestService);
//# sourceMappingURL=transaction-test.service.js.map