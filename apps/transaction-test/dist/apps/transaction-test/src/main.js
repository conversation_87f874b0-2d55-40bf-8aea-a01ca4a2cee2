"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const microservices_1 = require("@nestjs/microservices");
const mongoose_1 = require("mongoose");
const transaction_1 = require("../../../libs/common/src/transaction");
const app_module_1 = require("./app.module");
async function bootstrap() {
    console.log('🚀 启动事务测试服务...');
    try {
        await mongoose_1.default.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/transaction-test');
        console.log('✅ MongoDB连接成功');
        transaction_1.TransactionManager.initialize(mongoose_1.default);
        console.log('✅ 事务管理器初始化成功');
    }
    catch (error) {
        console.error('❌ MongoDB连接失败:', error);
        process.exit(1);
    }
    const app = await core_1.NestFactory.createMicroservice(app_module_1.AppModule, {
        transport: microservices_1.Transport.TCP,
        options: {
            host: '0.0.0.0',
            port: parseInt(process.env.TRANSACTION_TEST_PORT || '3010'),
        },
    });
    await app.listen();
    console.log('✅ 事务测试服务启动成功，端口: 3010');
}
bootstrap().catch((error) => {
    console.error('❌ 服务启动失败:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map