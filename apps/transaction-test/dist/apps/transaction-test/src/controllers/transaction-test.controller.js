"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionTestController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const types_1 = require("../../../../libs/common/src/types");
const transaction_test_service_1 = require("../services/transaction-test.service");
let TransactionTestController = class TransactionTestController {
    constructor(transactionTestService) {
        this.transactionTestService = transactionTestService;
    }
    async testSuccessfulTransfer(payload) {
        const result = await this.transactionTestService.testSuccessfulTransfer(payload.fromUsername, payload.toUsername, payload.amount);
        return types_1.MicroserviceResponseUtils.fromResult(result);
    }
    async testFailedTransfer(payload) {
        const result = await this.transactionTestService.testFailedTransfer(payload.fromUsername, payload.toUsername, payload.amount);
        return types_1.MicroserviceResponseUtils.fromResult(result);
    }
    async createTestAccounts() {
        const result = await this.transactionTestService.createTestAccounts();
        return types_1.MicroserviceResponseUtils.fromResult(result);
    }
    async getAccountBalance(payload) {
        const result = await this.transactionTestService.getAccountBalance(payload.username);
        return types_1.MicroserviceResponseUtils.fromResult(result);
    }
    async cleanupTestData() {
        const result = await this.transactionTestService.cleanupTestData();
        return types_1.MicroserviceResponseUtils.fromResult(result);
    }
};
exports.TransactionTestController = TransactionTestController;
__decorate([
    (0, microservices_1.MessagePattern)('transaction-test.successfulTransfer'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TransactionTestController.prototype, "testSuccessfulTransfer", null);
__decorate([
    (0, microservices_1.MessagePattern)('transaction-test.failedTransfer'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TransactionTestController.prototype, "testFailedTransfer", null);
__decorate([
    (0, microservices_1.MessagePattern)('transaction-test.createAccounts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TransactionTestController.prototype, "createTestAccounts", null);
__decorate([
    (0, microservices_1.MessagePattern)('transaction-test.getBalance'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TransactionTestController.prototype, "getAccountBalance", null);
__decorate([
    (0, microservices_1.MessagePattern)('transaction-test.cleanup'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TransactionTestController.prototype, "cleanupTestData", null);
exports.TransactionTestController = TransactionTestController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [transaction_test_service_1.TransactionTestService])
], TransactionTestController);
//# sourceMappingURL=transaction-test.controller.js.map