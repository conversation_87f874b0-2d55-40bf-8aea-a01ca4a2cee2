import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { GuildService } from './guild.service';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class GuildController {
  private readonly logger = new Logger(GuildController.name);

  constructor(private readonly guildService: GuildService) {}

  /**
   * 创建公会
   */
  @MessagePattern('guild.create')
  async createGuild(@Payload() payload: { characterId: string; characterName: string; guildName: string; faceId: number; strength: number; gid: string; faceUrl: string; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }) {
    this.logger.log('创建公会');
    const result = await this.guildService.createGuild(
      payload.characterId,
      payload.characterName,
      payload.guildName,
      payload.faceId,
      payload.strength,
      payload.gid,
      payload.faceUrl,
      payload.frontendId,
      payload.sessionId
    );
    return {
      code: 0,
      message: '公会创建成功',
      data: result,
    };
  }

  /**
   * 申请加入公会
   */
  @MessagePattern('guild.apply')
  async applyJoinGuild(@Payload() payload: { guildId: string; characterId: string; characterName: string; gid: string; faceUrl: string; strength: number; frontendId?: string; sessionId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`申请加入公会: ${payload.characterId} -> ${payload.guildId}`);
    const result = await this.guildService.applyJoinGuild(
      payload.guildId,
      payload.characterId,
      payload.characterName,
      payload.gid,
      payload.faceUrl,
      payload.strength,
      payload.frontendId,
      payload.sessionId
    );
    return {
      code: 0,
      message: '申请已提交',
      data: result,
    };
  }

  /**
   * 离开公会
   */
  @MessagePattern('guild.leave')
  async leaveGuild(@Payload() payload: { characterId: string; guildId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`离开公会: ${payload.characterId} -> ${payload.guildId}`);
    const result = await this.guildService.leaveGuild(payload.characterId, 1);
    return {
      code: 0,
      message: '离开公会成功',
      data: result,
    };
  }

  /**
   * 获取公会信息
   */
  @MessagePattern('guild.getInfo')
  async getGuildInfo(@Payload() payload: { guildId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取公会信息: ${payload.guildId}`);
    const result = await this.guildService.getGuildInfo(payload.guildId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 获取申请列表
   */
  @MessagePattern('guild.getApplicationList')
  async getApplicationList(@Payload() payload: { characterId: string; guildId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取申请列表: ${payload.guildId}`);
    const result = await this.guildService.getApplicationList(payload.characterId, payload.guildId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 同意加入申请
   */
  @MessagePattern('guild.approveApplication')
  async approveApplication(@Payload() payload: { characterId: string; guildId: string; agreeId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`同意申请: ${payload.guildId}, 申请者: ${payload.agreeId}`);
    const result = await this.guildService.approveApplication(payload.characterId, payload.guildId, payload.agreeId);
    return {
      code: 0,
      message: '申请已同意',
      data: result,
    };
  }

  /**
   * 拒绝加入申请
   */
  @MessagePattern('guild.rejectApplication')
  async rejectApplication(@Payload() payload: { characterId: string; guildId: string; rejectId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`拒绝申请: ${payload.guildId}, 申请者: ${payload.rejectId}`);
    const result = await this.guildService.rejectApplication(payload.characterId, payload.guildId, payload.rejectId);
    return {
      code: 0,
      message: '申请已拒绝',
      data: result,
    };
  }

  /**
   * 职位变更
   */
  @MessagePattern('guild.changePosition')
  async changePosition(@Payload() payload: { characterId: string; guildId: string; targetCharacterId: string; newPosition: number; injectedContext?: InjectedContext }) {
    this.logger.log(`职位变更: ${payload.guildId}, ${payload.targetCharacterId} -> 职位${payload.newPosition}`);
    const result = await this.guildService.changePosition(
      payload.characterId,
      payload.guildId,
      payload.targetCharacterId,
      payload.newPosition
    );
    return {
      code: 0,
      message: '职位变更成功',
      data: result,
    };
  }

  /**
   * 转让会长
   */
  @MessagePattern('guild.transferPresidency')
  async transferPresidency(@Payload() payload: { characterId: string; guildId: string; newPresidentId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`转让会长: ${payload.guildId}, 新会长: ${payload.newPresidentId}`);
    const result = await this.guildService.transferPresidency(
      payload.characterId,
      payload.guildId,
      payload.newPresidentId
    );
    return {
      code: 0,
      message: '会长转让成功',
      data: result,
    };
  }

  /**
   * 搜索公会
   */
  @MessagePattern('guild.search')
  async searchGuilds(@Payload() payload: { keyword: string; page?: number; limit?: number; injectedContext?: InjectedContext }) {
    this.logger.log(`搜索公会: ${payload.keyword}`);
    const result = await this.guildService.searchGuilds(
      payload.keyword,
      payload.page || 1,
      payload.limit || 20
    );
    return {
      code: 0,
      message: '搜索完成',
      data: result,
    };
  }

  /**
   * 获取公会排行榜
   */
  @MessagePattern('guild.getRanking')
  async getGuildRanking(@Payload() payload: { page?: number; limit?: number; injectedContext?: InjectedContext }) {
    this.logger.log(`获取公会排行榜: 第${payload.page || 1}页`);
    const result = await this.guildService.getGuildRanking(
      payload.page || 1,
      payload.limit || 50
    );
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }
}
