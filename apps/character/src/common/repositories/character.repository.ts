import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Character, CharacterDocument } from '../schemas/character.schema';
import { CreateCharacterDto } from '../dto/character.dto';

import { Result, RepositoryResultWrapper } from '@libs/common/types/result.type';
export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class CharacterRepository {
  private readonly logger = new Logger(CharacterRepository.name);

  constructor(
    @InjectModel(Character.name) private characterModel: Model<CharacterDocument>,
  ) {}

  /**
   * 创建角色
   */
  async create(createCharacterDto: CreateCharacterDto): Promise<Result<CharacterDocument>> { {
    return await RepositoryResultWrapper.wrap(async () => {
      const character = new this.characterModel(createCharacterDto);
            return await character.save();
    });
  }

  /**
   * 根据ID查找角色
   */
  async findById(characterId: string): Promise<Result<CharacterDocument | null>> { {
    return await RepositoryResultWrapper.wrapNullable(async () => {
      return await this.characterModel.findOne({ characterId }).exec();
    });
  }

  /**
   * 根据角色ID查找角色（别名方法，兼容old项目）
   */
  async findByCharacterId(characterId: string): Promise<Result<CharacterDocument | null>> { {
    return await RepositoryResultWrapper.wrapNullable(async () => {
      return this.findById(characterId);
    });
  }

  /**
   * 根据用户ID查找角色列表
   */
  async findByUserId(userId: string, serverId?: string): Promise<Result<CharacterDocument[]>> { {
    return await RepositoryResultWrapper.wrapArray(async () => {
      const filter: FilterQuery<CharacterDocument> = { userId };
            if (serverId) {
              filter.serverId = serverId;
            }
            return await this.characterModel.find(filter).exec();
    });
  }

  /**
   * 根据角色名查找角色
   * 增强版：支持可选serverId和软删除过滤
   * 基于old项目的全局搜索功能
   */
  async findByName(name: string, serverId?: string): Promise<Result<CharacterDocument | null>> { {
    return await RepositoryResultWrapper.wrapNullable(async () => {
      const filter: any = {
              name: name.trim(),
              deletedAt: { $exists: false } // 排除已删除的角色
            };

            if (serverId) {
              filter.serverId = serverId;
            }

            return await this.characterModel.findOne(filter).exec();
    });
  }

  /**
   * 根据OpenID查找角色
   */
  async findByOpenId(openId: string, serverId?: string): Promise<Result<CharacterDocument[]>> { {
    return await RepositoryResultWrapper.wrapArray(async () => {
      const filter: FilterQuery<CharacterDocument> = { openId };
            if (serverId) {
              filter.serverId = serverId;
            }
            return await this.characterModel.find(filter).exec();
    });
  }

  /**
   * 更新角色
   */
  async update(
    characterId: string, 
    updateData: UpdateQuery<CharacterDocument>,
    options?: QueryOptions
  ): Promise<Result<CharacterDocument | null>> { {
    return await RepositoryResultWrapper.wrapNullable(async () => {
      return await this.characterModel.findOneAndUpdate(
              { characterId },
              updateData,
              { new: true, ...options }
            ).exec();
    });
  }

  /**
   * 删除角色（软删除）
   */
  async softDelete(characterId: string): Promise<Result<CharacterDocument | null>> { {
    return await RepositoryResultWrapper.wrapNullable(async () => {
      return await this.characterModel.findOneAndUpdate(
              { characterId },
              { 
                $set: { 
                  deletedAt: new Date(),
                  'loginInfo.sessionId': null,
                  'loginInfo.frontendId': null
                }
              },
              { new: true }
            ).exec();
    });
  }

  /**
   * 检查角色名是否存在
   */
  async existsByName(name: string, serverId: string, excludeCharacterId?: string): Promise<Result<boolean>> { {
    return await RepositoryResultWrapper.wrapBoolean(async () => {
      const filter: FilterQuery<CharacterDocument> = { 
              name, 
              serverId,
              deletedAt: { $exists: false }
            };
      
            if (excludeCharacterId) {
              filter.characterId = { $ne: excludeCharacterId };
            }

            const count = await this.characterModel.countDocuments(filter);
            return count > 0;
    });
  }

  /**
   * 分页查询角色
   */
  async findWithPagination(
    filter: FilterQuery<CharacterDocument> = {},
    page: number = 1,
    limit: number = 20,
    sort: any = { 'loginInfo.createTime': -1 }
  ): Promise<PaginationResult<CharacterDocument>> {
    try {
      const skip = (page - 1) * limit;
      
      // 添加软删除过滤
      const finalFilter = {
        ...filter,
        deletedAt: { $exists: false }
      };

      const [data, total] = await Promise.all([
        this.characterModel
          .find(finalFilter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.characterModel.countDocuments(finalFilter)
      ]);

      const pages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error('分页查询角色失败', error);
      throw error;
    }
  }

  /**
   * 根据等级范围查找角色
   */
  async findByLevelRange(
    minLevel: number, 
    maxLevel: number, 
    serverId?: string,
    limit: number = 100
  ): Promise<Result<CharacterDocument[]>> { {
    return await RepositoryResultWrapper.wrapArray(async () => {
      const filter: FilterQuery<CharacterDocument> = {
              level: { $gte: minLevel, $lte: maxLevel },
              deletedAt: { $exists: false }
            };
      
            if (serverId) {
              filter.serverId = serverId;
            }

            return await this.characterModel
              .find(filter)
              .sort({ level: -1, fame: -1 })
              .limit(limit)
              .exec();
    });
  }

  /**
   * 获取在线角色数量
   */
  async getOnlineCount(serverId?: string): Promise<Result<number>> { {
    return await RepositoryResultWrapper.wrapCount(async () => {
      const filter: FilterQuery<CharacterDocument> = {
              'loginInfo.frontendId': { $exists: true, $ne: null },
              'loginInfo.sessionId': { $exists: true, $ne: null },
              deletedAt: { $exists: false }
            };
      
            if (serverId) {
              filter.serverId = serverId;
            }

            return await this.characterModel.countDocuments(filter);
    });
  }

  /**
   * 批量更新角色
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<CharacterDocument>;
    update: UpdateQuery<CharacterDocument>;
  }>): Promise<Result<any>> { {
    return await RepositoryResultWrapper.wrap(async () => {
      const bulkOps = updates.map(({ filter, update }) => ({
              updateMany: {
                filter,
                update: update as any, // 类型转换
              }
            }));

            return await this.characterModel.bulkWrite(bulkOps);
    });
  }

  /**
   * 获取服务器统计信息
   */
  async getServerStats(serverId: string): Promise<Result<any>> { {
    return await RepositoryResultWrapper.wrap(async () => {
      const stats = await this.characterModel.aggregate([
              {
                $match: {
                  serverId,
                  deletedAt: { $exists: false }
                }
              },
              {
                $group: {
                  _id: null,
                  totalCharacters: { $sum: 1 },
                  averageLevel: { $avg: '$level' },
                  maxLevel: { $max: '$level' },
                  totalCash: { $sum: '$cash' },
                  totalGold: { $sum: '$gold' },
                  onlineCount: {
                    $sum: {
                      $cond: [
                        {
                          $and: [
                            { $ne: ['$loginInfo.frontendId', null] },
                            { $ne: ['$loginInfo.sessionId', null] }
                          ]
                        },
                        1,
                        0
                      ]
                    }
                  }
                }
              }
            ]);

            return stats[0] || {
              totalCharacters: 0,
              averageLevel: 0,
              maxLevel: 0,
              totalCash: 0,
              totalGold: 0,
              onlineCount: 0
            };
    });
  }
}
