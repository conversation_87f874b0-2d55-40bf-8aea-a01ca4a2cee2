import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Inventory, InventoryDocument, BookMarkType, BookMarkEntry, InventoryType, InventoryItem } from '../schemas/inventory.schema';
import { AddItemToInventoryDto, GetInventoryListDto } from '../dto/inventory.dto';
import { Result, ResultUtils, ExceptionToResultUtils } from "@libs/common/types/result.type";

@Injectable()
export class InventoryRepository {
  private readonly logger = new Logger(InventoryRepository.name);

  constructor(
    @InjectModel(Inventory.name) private inventoryModel: Model<InventoryDocument>,
  ) {}

  /**
   * 创建背包（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   */
  async create(inventoryData: Partial<Inventory>): Promise<Result<InventoryDocument>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const safeData = this.sanitizeInventoryData(inventoryData);
            const inventory = new this.inventoryModel(safeData);
            return await inventory.save();
          });
        }
  }

  /**
   * 🛡️ 数据清理：仅允许安全的背包字段
   */
  private sanitizeInventoryData(data: any): Partial<Inventory> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'bag', 'itemUidToBookMarkId',
      'createTime', 'updateTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 根据ID查找背包
   */
  async findById(inventoryId: string): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOne({ inventoryId }).exec();
          });
        }
  }

  /**
   * 根据角色ID和类型查找背包
   */
  async findByCharacterAndType(
    characterId: string, 
    type: InventoryType
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOne({ characterId, type }).exec();
          });
        }
  }

  /**
   * 根据角色ID查找背包（支持单个或多个）
   * @param characterId 角色ID
   * @param findOne 是否只查找一个（默认false返回数组）
   */
  async findByCharacterId(
    characterId: string,
    findOne: boolean = false
  ): Promise<Result<InventoryDocument[] | InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            if (findOne) {
              return await this.inventoryModel.findOne({ characterId }).exec();
            } else {
              return await this.inventoryModel.find({ characterId }).sort({ type: 1 }).exec();
            }
          });
        }
  }

  /**
   * 更新背包
   */
  async update(
    inventoryId: string, 
    updateData: UpdateQuery<InventoryDocument>,
    options?: QueryOptions
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOneAndUpdate(
              { inventoryId },
              updateData,
              { new: true, ...options }
            ).exec();
          });
        }
  }

  /**
   * 删除背包
   */
  async delete(inventoryId: string): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const result = await this.inventoryModel.findOneAndDelete({ inventoryId }).exec();
            return result as unknown as InventoryDocument | null;
          });
        }
  }

  /**
   * 添加物品到背包
   */
  async addItem(
    characterId: string,
    type: InventoryType,
    item: InventoryItem
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOneAndUpdate(
              { characterId, type },
              { 
                $push: { items: item },
                $inc: { usedSlots: 1 }
              },
              { new: true }
            ).exec();
          });
        }
  }

  /**
   * 从背包移除物品
   */
  async removeItem(
    characterId: string,
    type: InventoryType,
    itemId: string
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOneAndUpdate(
              { characterId, type },
              { 
                $pull: { items: { itemId } },
                $inc: { usedSlots: -1 }
              },
              { new: true }
            ).exec();
          });
        }
  }

  /**
   * 更新背包中的物品
   */
  async updateItem(
    characterId: string,
    type: InventoryType,
    itemId: string,
    updateData: Partial<InventoryItem>
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const updateFields: any = {};
            Object.keys(updateData).forEach(key => {
              updateFields[`items.$.${key}`] = updateData[key];
            });
            return await this.inventoryModel.findOneAndUpdate(
              { characterId, type, 'items.itemId': itemId },
              { $set: updateFields },
              { new: true }
            ).exec();
          });
        }
  }

  /**
   * 查找背包中的物品
   */
  async findItemInInventory(
    characterId: string,
    type: InventoryType,
    itemId: string
  ): Promise<Result<InventoryItem | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const inventory = await this.inventoryModel.findOne(
              { characterId, type, 'items.itemId': itemId },
              { 'items.$': 1 }
            ).exec();
            return inventory?.items?.[0] || null;
          });
        }
  }

  /**
   * 查找背包中指定配置ID的物品
   */
  async findItemsByConfigId(
    characterId: string,
    type: InventoryType,
    configId: number
  ): Promise<Result<InventoryItem[]>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const inventory = await this.inventoryModel.findOne(
              { characterId, type },
              { items: { $elemMatch: { configId } } }
            ).exec();
            return inventory?.items || [];
          });
        }
  }

  /**
   * 扩展背包容量
   */
  async expandCapacity(
    characterId: string,
    type: InventoryType,
    additionalSlots: number
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            return await this.inventoryModel.findOneAndUpdate(
              { characterId, type },
              { 
                $inc: { 
                  capacity: additionalSlots,
                  expandCount: 1
                }
              },
              { new: true }
            ).exec();
          });
        }
  }

  /**
   * 整理背包
   */
  async sortInventory(
    characterId: string,
    type: InventoryType,
    sortBy: string = 'slot',
    sortOrder: string = 'asc'
  ): Promise<Result<InventoryDocument | null>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const inventory = await this.findByCharacterAndType(characterId, type);
            if (!inventory) return null;
            const sortedItems = inventory.items.sort((a, b) => {
              let aValue: any, bValue: any;
              
              switch (sortBy) {
                case 'configId':
                  aValue = a.configId;
                  bValue = b.configId;
                  break;
                case 'quantity':
                  aValue = a.quantity;
                  bValue = b.quantity;
                  break;
                case 'obtainTime':
                  aValue = a.obtainTime;
                  bValue = b.obtainTime;
                  break;
                default:
                  aValue = a.slot;
                  bValue = b.slot;
              }

              if (sortOrder === 'desc') {
                return bValue - aValue;
              }
              return aValue - bValue;
            });
            sortedItems.forEach((item, index) => {
              item.slot = index + 1;
            });
            return await this.inventoryModel.findOneAndUpdate(
              { characterId, type },
              { 
                items: sortedItems,
                sortBy,
                sortOrder,
                lastSortTime: Date.now()
              },
              { new: true }
            ).exec();
          });
        }
  }

  /**
   * 清理过期物品
   */
  async cleanExpiredItems(
    characterId: string,
    type?: InventoryType
  ): Promise<Result<number>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const now = Date.now();
            const filter: FilterQuery<InventoryDocument> = { characterId };
            if (type !== undefined) {
              filter.type = type;
            }
            const result = await this.inventoryModel.updateMany(
              filter,
              { 
                $pull: { 
                  items: { 
                    expireTime: { $gt: 0, $lt: now }
                  }
                },
                $set: {
                  lastCleanTime: now
                }
              }
            );
            const inventories = await this.inventoryModel.find(filter);
            for (const inventory of inventories) {
              await this.inventoryModel.updateOne(
                { _id: inventory._id },
                { usedSlots: inventory.items.length }
              );
            }
            return result.modifiedCount;
          });
        }
  }

  /**
   * 获取背包统计信息
   */
  async getInventoryStats(characterId: string): Promise<Result<any>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const stats = await this.inventoryModel.aggregate([
              {
                $match: { characterId }
              },
              {
                $group: {
                  _id: '$type',
                  capacity: { $first: '$capacity' },
                  usedSlots: { $first: '$usedSlots' },
                  totalItems: { $sum: { $size: '$items' } },
                  expiredItems: {
                    $sum: {
                      $size: {
                        $filter: {
                          input: '$items',
                          cond: {
                            $and: [
                              { $gt: ['$$this.expireTime', 0] },
                              { $lt: ['$$this.expireTime', Date.now()] }
                            ]
                          }
                        }
                      }
                    }
                  }
                }
              }
            ]);
            return stats.reduce((acc, stat) => {
              acc[stat._id] = {
                capacity: stat.capacity,
                usedSlots: stat.usedSlots,
                totalItems: stat.totalItems,
                expiredItems: stat.expiredItems,
                usageRate: stat.capacity > 0 ? (stat.usedSlots / stat.capacity) * 100 : 0
              };
              return acc;
            }, {});
          });
        }
  }

  /**
   * 批量更新背包
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<InventoryDocument>;
    update: UpdateQuery<InventoryDocument>;
  }>): Promise<Result<any>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const bulkOps = updates.map(({ filter, update }) => ({
              updateMany: {
                filter,
                update: update as any,
              }
            }));
            return await this.inventoryModel.bulkWrite(bulkOps);
          });
        }
  }

  /**
   * 检查背包是否存在
   */
  async exists(characterId: string, type: InventoryType): Promise<Result<boolean>> {
      {
          return await ExceptionToResultUtils.wrapAsync(async () => {
            const count = await this.inventoryModel.countDocuments({ characterId, type });
            return count > 0;
          });
        }
  }
}
