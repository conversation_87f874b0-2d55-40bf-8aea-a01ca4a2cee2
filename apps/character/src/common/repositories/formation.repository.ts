import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { TeamFormations, TeamFormationsDocument, FormationType } from '../schemas/formation.schema';
import {CreateFormationDto, GetFormationListDto, SwapHerosDto} from '../dto/formation.dto';

import { XResult, PaginationResult, XResultUtils, ExceptionToResultUtils } from '@libs/common/types/result.type';

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class FormationRepository {
  private readonly logger = new Logger(FormationRepository.name);

  constructor(
    @InjectModel(TeamFormations.name) private formationModel: Model<TeamFormationsDocument>,
  ) {}

  /**
   * 创建阵容（仅接受验证过的DTO）
   */
  async create(createFormationDto: CreateFormationDto): Promise<XResult<TeamFormationsDocument>> {
    try {
      const formation = new this.formationModel(createFormationDto);
      return await formation.save();
    } catch (error) {
      this.logger.error('创建阵容失败', error);
      throw error;
    }
  }

  /**
   * 内部创建方法（仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   */
  async createInternal(teamFormationsData: Partial<TeamFormations>): Promise<XResult<TeamFormationsDocument>> {
    try {
      // 🛡️ 安全措施：白名单字段过滤
      const safeData = this.sanitizeInternalData(teamFormationsData);
      const teamFormations = new this.formationModel(safeData);
      return await teamFormations.save();
    } catch (error) {
      this.logger.error('内部创建TeamFormations失败', error);
      throw error;
    }
  }

  /**
   * 🛡️ 数据清理：仅允许安全的内部字段
   */
  private sanitizeInternalData(data: any): Partial<TeamFormations> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'teamFormations',
      'currTeamFormationId', 'leagueTeamFormationId', 'warOfFaithTeamFormationId',
      'allTactics', 'allDefTactics', 'allFormations', 'fixId'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 根据阵容ID查找阵容
   */
  async findFormation(formationId: string, characterId: string): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      // 添加角色ID过滤，确保数据隔离
      return await this.formationModel.findOne({
        characterId,
        'teamFormations.uid': formationId
      }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找阵容失败: ${formationId}`, error);
      throw error;
    }
  }

  /**
   * 根据ID查找阵容
   * TODO 实现错误，待修正
   */
  async findById(formationId: string): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      return await this.formationModel.findOne({ formationId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找阵容失败: ${formationId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找阵容（支持单个或多个）
   * @param characterId 角色ID
   * @param serverId 服务器ID（可选）
   * @param findOne 是否只查找一个（默认false返回数组）
   */
  async findByCharacterId(
    characterId: string,
    serverId?: string,
    findOne: boolean = false
  ): Promise<XResult<TeamFormationsDocument[] | TeamFormationsDocument | null>> {
    try {
      const filter: FilterQuery<TeamFormationsDocument> = { characterId };
      if (serverId) {
        filter.serverId = serverId;
      }

      if (findOne) {
        return await this.formationModel.findOne(filter).exec();
      } else {
        return await this.formationModel.find(filter).sort({ createTime: -1 }).exec();
      }
    } catch (error) {
      this.logger.error(`根据角色ID查找阵容失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 获取当前使用的阵容
   */
  async findActiveFormation(characterId: string): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      return await this.formationModel.findOne({
        characterId,
        isActive: true
      }).exec();
    } catch (error) {
      this.logger.error(`获取当前阵容失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新阵容
   */
  async updateFormation(characterId: string, formationId: string, updateData: any): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      return await this.formationModel.findOneAndUpdate(
        {
          characterId,
          'teamFormations.uid': formationId
        },
        {
          $set: {
            'teamFormations.$': updateData
          }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error(`更新阵容失败: ${characterId}, ${formationId}`, error);
      throw error;
    }
  }

  /**
   * 更新阵容
   * TODO 实现错误，待修正,不应该是formationId，而应该是characterId
   */
  async update(
    characterId: string,
    updateData: UpdateQuery<TeamFormationsDocument>,
    options?: QueryOptions
  ): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      return await this.formationModel.findOneAndUpdate(
        { characterId: characterId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新阵容失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 删除阵容
   */
  async delete(formationId: string): Promise<XResult<TeamFormationsDocument | null>> {
    try {
      const result = await this.formationModel.findOneAndDelete({ formationId }).exec();
      return result as unknown as TeamFormationsDocument | null;
    } catch (error) {
      this.logger.error(`删除阵容失败: ${formationId}`, error);
      throw error;
    }
  }

  /**
   * 设置当前使用阵容
   */
  async setActiveFormation(characterId: string, formationId: string): Promise<XResult<void>> {
    try {
      // 先将所有阵容设为非激活状态
      await this.formationModel.updateMany(
        { characterId },
        { $set: { isActive: false } }
      );

      // 再将指定阵容设为激活状态
      await this.formationModel.updateOne(
        { formationId },
        { 
          $set: { 
            isActive: true,
            lastUsedTime: Date.now()
          }
        }
      );
    } catch (error) {
      this.logger.error(`设置当前阵容失败: ${formationId}`, error);
      throw error;
    }
  }

  /**
   * 分页查询阵容
   */
  async findWithPagination(
    query: GetFormationListDto,
    characterId?: string
  ): Promise<PaginationResult<TeamFormationsDocument>> {
    try {
      const filter: FilterQuery<TeamFormationsDocument> = {};

      // 添加角色ID过滤
      if (characterId) {
        filter.characterId = characterId;
      }
      if (query.characterId) {
        filter.characterId = query.characterId;
      }

      // 添加其他过滤条件
      if (query.formationType !== undefined) {
        filter.formationType = query.formationType;
      }
      if (query.activeOnly) {
        filter.isActive = true;
      }

      // 排序
      const sortField = query.sortBy || 'createTime';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortField]: sortOrder };

      // 分页
      const page = query.page || 1;
      const limit = query.limit || 20;
      const skip = (page - 1) * limit;

      const [data, total] = await Promise.all([
        this.formationModel
          .find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.formationModel.countDocuments(filter)
      ]);

      const pages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error('分页查询阵容失败', error);
      throw error;
    }
  }

  /**
   * 根据阵型查找阵容
   */
  async findByFormationType(
    formationType: FormationType, 
    characterId?: string,
    limit: number = 10
  ): Promise<XResult<TeamFormationsDocument[]>> {
    try {
      const filter: FilterQuery<TeamFormationsDocument> = { formationType };
      
      if (characterId) {
        filter.characterId = characterId;
      }

      return await this.formationModel
        .find(filter)
        .sort({ lastUsedTime: -1, createTime: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(`根据阵型查找阵容失败: ${formationType}`, error);
      throw error;
    }
  }

  /**
   * 检查球员是否在阵容中
   */
  async findFormationsWithHero(heroId: string, characterId?: string): Promise<XResult<TeamFormationsDocument[]>> {
    try {
      const filter: FilterQuery<TeamFormationsDocument> = {
        $or: [
          { 'heros.heroId': heroId },
          { 'substitutes.heroId': heroId }
        ]
      };
      
      if (characterId) {
        filter.characterId = characterId;
      }

      return await this.formationModel.find(filter).exec();
    } catch (error) {
      this.logger.error(`查找包含球员的阵容失败: ${heroId}`, error);
      throw error;
    }
  }

  /**
   * 获取角色阵容统计
   */
  async getCharacterFormationStats(characterId: string): Promise<XResult<any>> {
    try {
      const stats = await this.formationModel.aggregate([
        {
          $match: { characterId }
        },
        {
          $group: {
            _id: null,
            totalFormations: { $sum: 1 },
            activeFormations: {
              $sum: { $cond: ['$isActive', 1, 0] }
            },
            totalMatches: { $sum: '$matchesPlayed' },
            totalWins: { $sum: '$wins' },
            totalDraws: { $sum: '$draws' },
            totalLosses: { $sum: '$losses' },
            totalGoalsFor: { $sum: '$goalsFor' },
            totalGoalsAgainst: { $sum: '$goalsAgainst' },
            formationTypes: { $push: '$formationType' },
            averageRating: { $avg: '$stats.averageRating' },
            maxRating: { $max: '$stats.totalRating' }
          }
        }
      ]);

      const result = stats[0] || {
        totalFormations: 0,
        activeFormations: 0,
        totalMatches: 0,
        totalWins: 0,
        totalDraws: 0,
        totalLosses: 0,
        totalGoalsFor: 0,
        totalGoalsAgainst: 0,
        formationTypes: [],
        averageRating: 0,
        maxRating: 0
      };

      // 计算胜率
      result.winRate = result.totalMatches > 0 
        ? Math.round((result.totalWins / result.totalMatches) * 100) 
        : 0;

      // 计算净胜球
      result.goalDifference = result.totalGoalsFor - result.totalGoalsAgainst;

      return result;
    } catch (error) {
      this.logger.error(`获取角色阵容统计失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 批量更新阵容
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<TeamFormationsDocument>;
    update: UpdateQuery<TeamFormationsDocument>;
  }>): Promise<XResult<any>> {
    try {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
        }
      }));

      return await this.formationModel.bulkWrite(bulkOps);
    } catch (error) {
      this.logger.error('批量更新阵容失败', error);
      throw error;
    }
  }

  /**
   * 复制阵容
   */
  async copyFormation(sourceFormationId: string, newFormationData: Partial<TeamFormationsDocument>): Promise<XResult<TeamFormationsDocument>> {
    try {
      const sourceFormation = await this.findById(sourceFormationId);
      if (!sourceFormation) {
        throw new Error('源阵容不存在');
      }

      // 创建新阵容数据
      const newFormation = new this.formationModel({
        ...sourceFormation.toObject(),
        _id: undefined,
        formationId: undefined,
        isActive: false,
        isDefault: false,
        createTime: Date.now(),
        lastUsedTime: 0,
        lastModifiedTime: Date.now(),
        matchesPlayed: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        ...newFormationData
      });

      return await newFormation.save();
    } catch (error) {
      this.logger.error(`复制阵容失败: ${sourceFormationId}`, error);
      throw error;
    }
  }

  /**
   * 检查阵容名称是否存在
   */
  async existsByName(name: string, characterId: string, excludeFormationId?: string): Promise<XResult<boolean>> {
    try {
      const filter: FilterQuery<TeamFormationsDocument> = { 
        name, 
        characterId
      };
      
      if (excludeFormationId) {
        filter.formationId = { $ne: excludeFormationId };
      }

      const count = await this.formationModel.countDocuments(filter);
      return count > 0;
    } catch (error) {
      this.logger.error(`检查阵容名称是否存在失败: ${name}`, error);
      throw error;
    }
  }
}
