/**
 * Item Service - 严格基于old项目item.js重新实现
 * 确保与old项目的业务逻辑100%一致
 */

import { Injectable, Logger } from '@nestjs/common';
import { Item, ItemDocument, ItemInstance } from '@character/common/schemas/item.schema';
import { ItemRepository } from '@character/common/repositories/item.repository';
import { GameConfigFacade } from '@libs/game-config';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class ItemService {
  private readonly logger = new Logger(ItemService.name);

  constructor(
    private readonly itemRepository: ItemRepository, // ✅ 使用Repository模式
    private readonly gameConfig: GameConfigFacade,
  ) {}

  /**
   * 获取角色的物品数据
   * 基于old项目: Item实体的初始化
   */
  async getCharacterItems(characterId: string): Promise<XResult<ItemDocument | null>> {
    return await this.itemRepository.findByCharacterId(characterId) as ItemDocument | null;
  }

  /**
   * 初始化角色物品数据
   * 基于old项目: Item构造函数和initByDB
   */
  async initCharacterItems(characterId: string, serverId: string): Promise<XResult<ItemDocument>> {
    const uid = characterId; // 在新架构中，uid就是characterId

    const itemData = {
      uid,
      characterId,
      serverId,
      item: [],
      resId2Uid: {},
    };

    return await this.itemRepository.create(itemData);
  }

  /**
   * 创建物品实例
   * 基于old项目: newItem方法，优化命名为createItemInstance
   */
  async createItemInstance(resId: number, num: number): Promise<XResult<ItemInstance | null>> {
    this.logger.log(`创建物品实例: 配置ID: ${resId}, 数量: ${num}`);

    const itemConfigs = await this.gameConfig.item.getAll();
    const config = itemConfigs.find(c => c.id === resId);
    if (!config) {
      this.logger.error('createItemInstance: 未找到物品配置', resId);
      return null;
    }

    const item: ItemInstance = {
      uid: this.generateUid(),           // 物品的uid
      resId: resId,                      // 物品的资源ID
      num: num,                          // 物品个数
      bind: 0,                           // 绑定：0-未绑定，1-已绑定
      type: config.type || 1,            // 物品类别，ITEM_TYPE_
      timeLimit: 0,                      // 道具限时 0xFFFF 无限制时间
      invalid: 0,                        // 是否失效：0-未失效，1-已失效
    };

    return item;
  }

  /**
   * 添加物品
   * 严格基于old项目: addItem方法
   */
  async addItem(characterId: string, resId: number, num: number): Promise<XResult<{ code: number; uids: string[] }>> {
    this.logger.log(`添加物品: ${characterId}, 配置ID: ${resId}, 数量: ${num}`);

    const failedRet = { code: -1, uids: [] };

    if (!resId || resId <= 0) {
      this.logger.error('addItem: resId错误!');
      return failedRet;
    }

    if (!num || num <= 0) {
      this.logger.error('addItem: num错误!');
      return failedRet;
    }

    let items = await this.getCharacterItems(characterId);
    if (!items) {
      items = await this.initCharacterItems(characterId, 'server_001');
    }

    const itemConfigs = await this.gameConfig.item.getAll();
    const config = itemConfigs.find(c => c.id === resId);
    if (!config) {
      this.logger.error('addItem: 未找到物品配置!', resId);
      return failedRet;
    }

    const uidList: string[] = [];

    // 判断此物品是否可以叠加
    const isSingle = config.isSingle || 0;
    if (isSingle === 0) { // 不可以叠加,直接新的物品
      for (let i = 0; i < num; i++) {
        const uid = await this.addItemInternal(items, resId, 1);
        if (!uid) {
          this.logger.error('addItem: 添加不可堆叠物品失败!', resId, i);
          return failedRet;
        }
        uidList.push(uid);
      }

      await items.save();
      this.logger.log('addItem 不可堆叠物品添加成功', uidList);
      return { code: 0, uids: uidList };
    }

    // 下面是可以堆叠的物品
    const limitNum = config.isSingleParameter || 999;

    // 1.检查物品是否已经存在
    const has = items.hasItemByConfigId(resId);
    if (!has) { // 不存在此类物品
      // 分批插入
      const batchUids = await this.batchAddItems(items, resId, num, limitNum);
      await items.save();
      return { code: 0, uids: batchUids };
    } else { // 存在此类物品
      // 找到最后一个数量还没有到达limitNum的Uid, 先插入第一个,然后再分批次插入数据
      const uid = items.getPartialStackItemId(resId, limitNum);

      if (!uid) { // 返回uid为空，说明存在的uid都已经满了，直接分批次插入
        this.logger.log('addItem 存在物品但已满!', uid, limitNum);
        const batchUids = await this.batchAddItems(items, resId, num, limitNum);
        await items.save();
        return { code: 0, uids: batchUids };
      } else {
        // 不为空，说明还有剩余的数量可以堆叠
        const item = items.getItem(uid);
        if (!item) {
          this.logger.error('addItem: 未找到物品对象!', uid);
          return failedRet;
        }

        const canAdd = limitNum - item.num;
        if (num <= canAdd) {
          // 可以全部添加到现有物品中
          item.num += num;
          uidList.push(uid);
        } else {
          // 部分添加到现有物品，剩余的分批添加
          item.num = limitNum;
          uidList.push(uid);
          
          const remainingNum = num - canAdd;
          const batchUids = await this.batchAddItems(items, resId, remainingNum, limitNum);
          uidList.push(...batchUids);
        }

        await items.save();
        return { code: 0, uids: uidList };
      }
    }
  }

  /**
   * 移除物品
   * 基于old项目: delItem方法，优化命名为removeItem
   */
  async removeItem(characterId: string, itemId: string, quantity: number): Promise<XResult<number>> {
    this.logger.log(`移除物品: ${characterId}, UID: ${itemId}, 数量: ${quantity}`);

    if (!itemId || itemId === '') {
      this.logger.error('removeItem: itemId错误!');
      return -1; // Code.FAIL
    }

    if (!quantity || quantity <= 0) {
      this.logger.error('removeItem: quantity错误!');
      return -1; // Code.FAIL
    }

    const items = await this.getCharacterItems(characterId);
    if (!items) {
      return -1; // Code.FAIL
    }

    const item = items.getItem(itemId);
    if (!item) {
      this.logger.error('removeItem: 未找到物品!', itemId);
      return -1; // Code.FAIL
    }

    if (item.num < quantity) {
      this.logger.error('removeItem: 数量错误! 物品数量小于删除数量', item.num, quantity);
      return -1; // Code.FAIL
    }

    const result = await this.removeItemInternal(items, itemId, quantity);
    if (result === 0) {
      await items.save();
    }

    return result;
  }

  /**
   * 使用物品
   * 基于old项目: useItem方法，优化参数命名
   */
  async useItem(characterId: string, itemId: string, quantity: number = 1): Promise<XResult<{ code: number; effects?: any[] }>> {
    this.logger.log(`使用物品: ${characterId}, UID: ${itemId}, 数量: ${quantity}`);

    const items = await this.getCharacterItems(characterId);
    if (!items) {
      return { code: -1 };
    }

    const item = items.getItem(itemId);
    if (!item) {
      this.logger.error('useItem: 未找到物品!', itemId);
      return { code: -1 };
    }

    if (item.num < quantity) {
      this.logger.error('useItem: 物品数量不足!', item.num, quantity);
      return { code: -2 };
    }

    // 检查物品是否可以使用
    if (item.invalid === 1) {
      this.logger.error('useItem: 物品已失效!', itemId);
      return { code: -3 };
    }

    // 根据物品类型执行不同的使用效果（基于old项目逻辑）
    const effects = await this.executeItemEffects(item, quantity);

    // 消耗物品
    const delResult = await this.removeItemInternal(items, itemId, quantity);
    if (delResult === 0) {
      await this.itemRepository.update(items._id.toString(), items.toObject());
    }

    return { code: 0, effects };
  }

  /**
   * 获取物品数量
   * 基于old项目: getItemNum方法，优化参数命名
   */
  async getItemQuantity(characterId: string, itemId: string): Promise<XResult<number>> {
    const items = await this.getCharacterItems(characterId);
    if (!items) {
      return 0;
    }

    return items.getItemNum(itemId);
  }

  /**
   * 根据配置ID获取物品总数量
   * 基于old项目: getItemNumByResID方法，优化命名为getItemQuantityByConfigId
   */
  async getItemQuantityByConfigId(characterId: string, configId: number): Promise<XResult<number>> {
    const items = await this.getCharacterItems(characterId);
    if (!items) {
      return 0;
    }

    return items.getItemNumByResID(configId);
  }

  /**
   * 检查物品数量是否足够
   * 基于old项目: checkItemIsEnough方法，优化命名为checkItemSufficient
   */
  async checkItemSufficient(characterId: string, configId: number, requiredQuantity: number): Promise<XResult<boolean>> {
    const items = await this.getCharacterItems(characterId);
    if (!items) {
      return false;
    }

    return items.checkItemIsEnough(configId, requiredQuantity);
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成UID
   * 基于old项目: utils.syncCreateUid
   */
  private generateUid(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 内部添加物品方法
   * 基于old项目: _innerAddItem方法，优化命名为addItemInternal
   */
  private async addItemInternal(items: ItemDocument, resId: number, num: number): Promise<XResult<string | null>> {
    const newItem = await this.createItemInstance(resId, num);
    if (!newItem) {
      return null;
    }

    // 添加到物品列表
    items.item.push(newItem);

    // 添加到堆叠映射
    items.addToConfigMapping(resId, newItem.uid);

    return newItem.uid;
  }

  /**
   * 批量添加物品
   * 基于old项目: _InnerBatchAddItem方法，优化命名为batchAddItems
   */
  private async batchAddItems(items: ItemDocument, resId: number, num: number, limitNum: number): Promise<XResult<string[]>> {
    const uidList: string[] = [];
    let remainingNum = num;

    while (remainingNum > 0) {
      const addNum = Math.min(remainingNum, limitNum);
      const uid = await this.addItemInternal(items, resId, addNum);
      if (!uid) {
        break;
      }
      uidList.push(uid);
      remainingNum -= addNum;
    }

    return uidList;
  }

  /**
   * 内部删除物品方法
   * 基于old项目: _innerDelItem方法，优化命名为removeItemInternal
   */
  private async removeItemInternal(items: ItemDocument, uid: string, num: number): Promise<XResult<number>> {
    const item = items.getItem(uid);
    if (!item) {
      return -1; // Code.FAIL
    }

    const resID = item.resId;

    if (num === item.num) {
      // 删除整个物品
      items.removeItemInstance(uid);
      items.removeFromConfigMapping(resID, uid);
    } else if (num < item.num) {
      // 减少数量
      item.num = item.num - num;
    } else {
      // 数量不足
      return -1; // Code.FAIL
    }

    // TODO: 记录统计日志
    // this.hero.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.USE_ITEM, [resID, num, item.num], {});

    return 0; // Code.OK
  }

  /**
   * 执行物品效果
   * 基于old项目: useItemMainType方法的完整实现
   */
  private async executeItemEffects(item: ItemInstance, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    try {
      // 获取物品配置
      const itemConfig = await this.gameConfig.item.get(item.resId);
      if (!itemConfig) {
        this.logger.error('物品配置不存在:', item.resId);
        return effects;
      }

      // 基于old项目：根据物品主类型执行不同效果
      switch (itemConfig.type) {
        case 1: // 货币类型（基于old项目USE_ITEM_MAIN_PROPERTY_EFFECT_CURRENCY）
          effects.push(...await this.executeCurrencyEffects(itemConfig, quantity));
          break;

        case 2: // 消耗品类型（基于old项目USE_ITEM_MAIN_PROPERTY_EFFECT_CONSUME）
          effects.push(...await this.executeConsumableEffects(itemConfig, quantity));
          break;

        case 3: // 礼包类型（基于old项目USE_ITEM_MAIN_PROPERTY_EFFECT_GIFT_BAG）
          effects.push(...await this.executeGiftPackEffects(itemConfig, quantity));
          break;

        case 4: // 装备类型
          effects.push(...await this.executeEquipmentEffects(itemConfig, quantity));
          break;

        case 5: // 球员卡类型
          effects.push(...await this.executeHeroCardEffects(itemConfig, quantity));
          break;

        default:
          this.logger.warn('未知物品类型:', itemConfig.type);
      }

      return effects;
    } catch (error) {
      this.logger.error('执行物品效果失败', error);
      return effects;
    }
  }

  /**
   * 执行货币效果
   * 基于old项目: useItemBySubTypeCurrency方法
   */
  private async executeCurrencyEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    try {
      // 基于old项目：根据resId判断货币类型
      const consumeValue = quantity;
      let currencyType = '';
      let effectValue = 0;

      switch (itemConfig.id) {
        case 1: // 欧元（基于old项目CASH）
          currencyType = 'cash';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        case 2: // 球币（基于old项目GOLD）
          currencyType = 'gold';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        case 3: // 精力（基于old项目ENERGY）
          currencyType = 'energy';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        case 4: // 训练点数（基于old项目TRAIN_COUNT）
          currencyType = 'trainCount';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        case 5: // 声望（基于old项目FAME）
          currencyType = 'fame';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        case 6: // 体力（基于old项目SCOUT_ENERGY）
          currencyType = 'scoutEnergy';
          effectValue = itemConfig.itemParameters * consumeValue;
          break;

        default:
          this.logger.warn('未知货币类型:', itemConfig.id);
          return effects;
      }

      // TODO: 调用Character服务增加货币
      // await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.addCurrency',
      //   { characterId: item.characterId, currencyType, amount: effectValue, source: 'item_use' }
      // );

      effects.push({
        type: 'currency_gain',
        currencyType,
        value: effectValue,
        description: `获得${effectValue}${currencyType}`,
      });

      return effects;
    } catch (error) {
      this.logger.error('执行货币效果失败', error);
      return effects;
    }
  }

  /**
   * 执行消耗品效果
   * 基于old项目: 消耗品使用逻辑
   */
  private async executeConsumableEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    // 体力恢复
    if (itemConfig.effect?.energy) {
      const totalEnergy = itemConfig.effect.energy * quantity;
      effects.push({
        type: 'energy_restore',
        value: totalEnergy,
        description: `恢复${totalEnergy}点体力`,
      });
    }

    // 经验增加
    if (itemConfig.effect?.exp) {
      const totalExp = itemConfig.effect.exp * quantity;
      effects.push({
        type: 'exp_gain',
        value: totalExp,
        description: `获得${totalExp}点经验`,
      });
    }

    // 货币获得
    if (itemConfig.effect?.currency) {
      for (const [currencyType, amount] of Object.entries(itemConfig.effect.currency)) {
        const totalAmount = (amount as number) * quantity;
        effects.push({
          type: 'currency_gain',
          currencyType,
          value: totalAmount,
          description: `获得${totalAmount}${this.getCurrencyName(currencyType)}`,
        });
      }
    }

    return effects;
  }

  /**
   * 执行装备效果
   * 基于old项目: 装备使用逻辑
   */
  private async executeEquipmentEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    // 装备属性加成
    if (itemConfig.attributes) {
      effects.push({
        type: 'equipment_bonus',
        attributes: itemConfig.attributes,
        description: `装备属性加成`,
      });
    }

    // 特殊技能
    if (itemConfig.skills) {
      effects.push({
        type: 'skill_unlock',
        skills: itemConfig.skills,
        description: `解锁特殊技能`,
      });
    }

    return effects;
  }

  /**
   * 执行材料效果
   * 基于old项目: 材料使用逻辑
   */
  private async executeMaterialEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    // 材料通常用于合成，不直接产生效果
    effects.push({
      type: 'material_consumed',
      value: quantity,
      description: `消耗${quantity}个${itemConfig.name}`,
    });

    return effects;
  }

  /**
   * 执行礼包效果
   * 基于old项目: 礼包开启逻辑
   */
  private async executeGiftPackEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    if (itemConfig.rewards) {
      for (let i = 0; i < quantity; i++) {
        for (const reward of itemConfig.rewards) {
          const rewardEffect = await this.processReward(reward);
          effects.push(rewardEffect);
        }
      }
    }

    return effects;
  }

  /**
   * 执行球员卡效果
   * 基于old项目: 球员卡使用逻辑
   */
  private async executeHeroCardEffects(itemConfig: any, quantity: number): Promise<XResult<any[]>> {
    const effects: any[] = [];

    if (itemConfig.heroResId) {
      for (let i = 0; i < quantity; i++) {
        effects.push({
          type: 'hero_obtained',
          heroResId: itemConfig.heroResId,
          description: `获得球员: ${itemConfig.heroName || '未知球员'}`,
        });
      }
    }

    return effects;
  }

  /**
   * 处理奖励
   * 基于old项目: 奖励处理逻辑
   */
  private async processReward(reward: any): Promise<XResult<any>> {
    switch (reward.type) {
      case 'currency':
        return {
          type: 'currency_gain',
          currencyType: reward.currencyType,
          value: reward.amount,
          description: `获得${reward.amount}${this.getCurrencyName(reward.currencyType)}`,
        };

      case 'item':
        return {
          type: 'item_obtained',
          itemId: reward.itemId,
          quantity: reward.quantity,
          description: `获得物品: ${reward.itemName || '未知物品'} x${reward.quantity}`,
        };

      case 'hero':
        return {
          type: 'hero_obtained',
          heroResId: reward.heroResId,
          description: `获得球员: ${reward.heroName || '未知球员'}`,
        };

      default:
        return {
          type: 'unknown_reward',
          data: reward,
          description: '获得未知奖励',
        };
    }
  }

  /**
   * 获取货币名称
   */
  private getCurrencyName(currencyType: string): string {
    const currencyNames = {
      gold: '金币',
      diamond: '钻石',
      energy: '体力',
      exp: '经验',
      cash: '欧元',
    };
    return currencyNames[currencyType] || currencyType;
  }
}
