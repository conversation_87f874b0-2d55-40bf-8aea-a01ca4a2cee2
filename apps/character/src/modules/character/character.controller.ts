import { Controller, Logger } from '@nestjs/common';
import {MessagePattern, Payload } from '@nestjs/microservices';
import { MessagePatternInternal } from '@libs/common/decorators';
import { CharacterService } from './character.service';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  LoginCharacterDto,
  CurrencyOperationDto,
  BuyEnergyDto,
  LevelUpDto,
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto,
  GetCharacterListDto
} from '@character/common/dto/character.dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';
import { MicroserviceResponse, MicroserviceResponseUtils, ResultUtils } from '@libs/common/types/result.type';
import { InjectedContext } from '@libs/common/types';

@Controller()
export class CharacterController {
  private readonly logger = new Logger(CharacterController.name);

  constructor(private readonly characterService: CharacterService) {}

  /**
   * 创建新角色
   */
  @MessagePattern('character.create')
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(@Payload() payload: { createDto: CreateCharacterDto; injectedContext?: InjectedContext }) {
    this.logger.log(`创建角色请求: ${JSON.stringify(payload.createDto)}`);
    const character = await this.characterService.createCharacter(payload.createDto);
    return {
      code: 0,
      message: '角色创建成功',
      data: character,
    };
  }

  /**
   * 角色登录
   */
  @MessagePattern('character.login')
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: { loginDto: LoginCharacterDto; injectedContext?: InjectedContext }) {
    this.logger.log(`角色登录请求: ${JSON.stringify(payload.loginDto)}`);
    const result = await this.characterService.loginCharacter(payload.loginDto);
    return {
      code: 0,
      message: '登录成功',
      data: result,
    };
  }

  /**
   * 角色登出
   */
  @MessagePattern('character.logout')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server'
  })
  async logoutCharacter(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`角色登出请求: ${payload.characterId}`);
    await this.characterService.logoutCharacter(payload.characterId);
    return {
      code: 0,
      message: '登出成功',
    };
  }

  /**
   * 获取角色信息
   */
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    // 🔍 详细日志：验证payload注入机制
    this.logger.log(`=== Payload注入验证 - getCharacterInfo ===`);
    this.logger.log(`📋 原始字段:`);
    this.logger.log(`  - characterId: ${payload.characterId}`);
    this.logger.log(`  - serverId: ${payload.serverId}`);

    this.logger.log(`🔧 注入字段:`);
    this.logger.log(`  - userId: ${(payload as any).userId || 'N/A'}`);
    this.logger.log(`  - wsContext: ${JSON.stringify((payload as any).wsContext || 'N/A')}`);
    this.logger.log(`  - serverContext: ${JSON.stringify((payload as any).serverContext || 'N/A')}`);

    this.logger.log(`📊 完整payload结构:`);
    this.logger.log(JSON.stringify(payload, null, 2));
    this.logger.log(`=== Payload注入验证结束 ===`);

    const character = await this.characterService.getCharacterInfo(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: character,
    };
  }

  /**
   * 更新角色信息
   */
  @MessagePattern('character.update')
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateCharacter(@Payload() payload: { characterId: string; updateDto: UpdateCharacterDto; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`更新角色信息: ${payload.characterId}`);
    const character = await this.characterService.updateCharacter(payload.characterId, payload.updateDto);
    return {
      code: 0,
      message: '更新成功',
      data: character,
    };
  }

  /**
   * 获取角色列表
   */
  @MessagePattern('character.getList')
  @Cacheable({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getCharacterList(@Payload() payload: {getCharacterListDto: GetCharacterListDto, injectedContext?: InjectedContext}) {
    this.logger.log(`获取角色列表: ${JSON.stringify(payload)}`);
    const result = await this.characterService.getCharacterList(payload.getCharacterListDto);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 增加货币 - 仅内部服务调用
   */
  @MessagePatternInternal('character.currency.add')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addCurrency(@Payload() payload: {
    characterId: string;
    currencyDto: CurrencyOperationDto;
    serverId?: string
  }) {
    this.logger.log(`增加货币: ${payload.characterId}, ${JSON.stringify(payload.currencyDto)}`);
    const result = await this.characterService.addCurrency(payload.characterId, payload.currencyDto);
    return {
      code: 0,
      message: '货币增加成功',
      data: result,
    };
  }

  /**
   * 扣除货币 - 仅内部服务调用
   */
  @MessagePatternInternal('character.currency.subtract')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async subtractCurrency(@Payload() payload: {
    characterId: string;
    currencyDto: CurrencyOperationDto;
    serverId?: string
  }) {
    this.logger.log(`扣除货币: ${payload.characterId}, ${JSON.stringify(payload.currencyDto)}`);
    const result = await this.characterService.subtractCurrency(payload.characterId, payload.currencyDto);
    return {
      code: 0,
      message: '货币扣除成功',
      data: result,
    };
  }

  /**
   * 购买体力
   */
  @MessagePattern('character.energy.buy')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyEnergy(@Payload() payload: { characterId: string; buyDto: BuyEnergyDto; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`购买体力: ${payload.characterId}, ${JSON.stringify(payload.buyDto)}`);
    const result = await this.characterService.buyEnergy(payload.characterId, payload.buyDto);
    return {
      code: 0,
      message: '购买体力成功',
      data: result,
    };
  }

  /**
   * 角色升级
   */
  @MessagePattern('character.levelup')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUp(@Payload() payload: { characterId: string; levelUpDto: LevelUpDto; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`角色升级: ${payload.characterId}, ${JSON.stringify(payload.levelUpDto)}`);
    const result = await this.characterService.levelUp(payload.characterId, payload.levelUpDto);
    return {
      code: 0,
      message: '升级成功',
      data: result,
    };
  }

  /**
   * 完成创角步骤
   */
  @MessagePattern('character.progress.completeStep')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async completeCreateStep(@Payload() payload: { characterId: string; step: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`完成创角步骤: ${payload.characterId}, step: ${payload.step}`);
    const result = await this.characterService.completeCreateStep(payload.characterId, payload.step);
    return {
      code: 0,
      message: '步骤完成',
      data: result,
    };
  }

  /**
   * 完成新手引导
   */
  @MessagePattern('character.progress.finishGuide')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async finishGuide(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`完成新手引导: ${payload.characterId}`);
    const result = await this.characterService.finishGuide(payload.characterId);
    return {
      code: 0,
      message: '新手引导完成',
      data: result,
    };
  }

  /**
   * 设置角色信仰
   */
  @MessagePattern('character.setBelief')
  async setCharacterBelief(@Payload() payload: { characterId: string; beliefId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`设置角色信仰: ${payload.characterId}, 信仰ID: ${payload.beliefId}`);
    const character = await this.characterService.setCharacterBelief(payload.characterId, payload.beliefId);
    return {
      code: 0,
      message: '设置成功',
      data: character,
    };
  }

  /**
   * 使用兑换码
   */
  @MessagePattern('character.useRedeemCode')
  async useRedeemCode(@Payload() payload: { characterId: string; group: string; codeId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`使用兑换码: ${payload.characterId}, 组: ${payload.group}, 码: ${payload.codeId}`);
    const result = await this.characterService.useRedeemCode(payload.characterId, payload.group, payload.codeId);
    return {
      code: 0,
      message: '兑换成功',
      data: result,
    };
  }

  /**
   * 更新持续buff
   */
  @MessagePattern('character.updateBuff')
  async updateContinuedBuff(@Payload() payload: { characterId: string; buffDuration: number; injectedContext?: InjectedContext }) {
    this.logger.log(`更新持续buff: ${payload.characterId}, 持续时间: ${payload.buffDuration}秒`);
    const character = await this.characterService.updateContinuedBuff(payload.characterId, payload.buffDuration);
    return {
      code: 0,
      message: '更新成功',
      data: character,
    };
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName
   * 用于商业赛等功能的对手搜索
   */
  @MessagePattern('character.searchByName')
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: { name: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`根据名称搜索角色: ${payload.name}`);
    const character = await this.characterService.searchByName(payload.name, payload.serverId);
    return {
      code: 0,
      message: '搜索成功',
      data: character,
    };
  }

  // ==================== old项目核心API ====================

  /**
   * 获取个人信息
   * 对应old项目: game.player.getPersonInfo
   */
  @MessagePattern('character.getPersonInfo')
  @Cacheable({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getPersonInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取个人信息: ${payload.characterId}`);
    const result = await this.characterService.getPersonInfo(payload.characterId);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 创建角色
   * 对应old项目: game.player.createRole
   */
  @MessagePattern('character.createRole')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createRole(@Payload() payload: { characterId: string; qualified: number; name: string; faceIcon: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`创建角色: ${payload.characterId}, 参数: ${JSON.stringify(payload)}`);
    const result = await this.characterService.createRole(payload.characterId, {
      qualified: payload.qualified,
      name: payload.name,
      faceIcon: payload.faceIcon,
    });
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 修改玩家名称
   * 对应old项目: game.player.modifyPlayerName
   */
  @MessagePattern('character.modifyCharacterName')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async modifyCharacterName(@Payload() payload: { characterId: string; name: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`修改玩家名称: ${payload.characterId}, 新名称: ${payload.name}`);
    const result = await this.characterService.modifyCharacterName(payload.characterId, {
      name: payload.name,
    });
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 添加资源 - 仅内部服务调用
   * 对应old项目: game.player.addResource
   */
  @MessagePatternInternal('character.addResource')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addResource(@Payload() payload: {
    characterId: string;
    resourceType: string;
    amount: number;
    reason?: string;
    serverId?: string;
  }) {
    this.logger.log(`添加资源: ${payload.characterId}, 类型: ${payload.resourceType}, 数量: ${payload.amount}`);
    const result = await this.characterService.addResource(
      payload.characterId,
      payload.resourceType,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取体力奖励
   * 对应old项目: game.player.getEnergyReward
   */
  @MessagePattern('character.getEnergyReward')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getEnergyReward(@Payload() payload: { characterId: string; type: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取体力奖励: ${payload.characterId}, 类型: ${payload.type}`);
    const result = await this.characterService.getEnergyReward(payload.characterId, payload.type);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 消耗金币任务
   * 对应old项目: game.player.costCashTask
   */
  @MessagePattern('character.costCashTask')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async costCashTask(@Payload() payload: { characterId: string; amount: number; reason?: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`消耗金币: ${payload.characterId}, 数量: ${payload.amount}`);
    const result = await this.characterService.costCashTask(
      payload.characterId,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 扣除资源 - 仅内部服务调用
   * 对应old项目: game.player.deductResource
   */
  @MessagePatternInternal('character.deductResource')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deductResource(@Payload() payload: {
    characterId: string;
    resourceType: string;
    amount: number;
    reason?: string;
    serverId?: string;
  }) {
    this.logger.log(`扣除资源: ${payload.characterId}, 类型: ${payload.resourceType}, 数量: ${payload.amount}`);
    const result = await this.characterService.deductResource(
      payload.characterId,
      payload.resourceType,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  // ==================== 球探系统API ====================

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   */
  @MessagePattern('character.getScoutData')
  @Cacheable({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutData(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取球探数据: ${payload.characterId}`);

    try {
      const scoutData = await this.characterService.getScoutData(payload.characterId);
      return {
        code: 0,
        message: '获取球探数据成功',
        data: scoutData,
      };
    } catch (error) {
      this.logger.error('获取球探数据失败', error);
      return {
        code: -1,
        message: error.message || '获取球探数据失败',
        data: null,
      };
    }
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   */
  @MessagePattern('character.updateScoutData')
  @CacheEvict({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateScoutData(@Payload() payload: { characterId: string; scoutData: any; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`更新球探数据: ${payload.characterId}`);

    try {
      await this.characterService.updateScoutData(payload.characterId, payload.scoutData);
      return {
        code: 0,
        message: '更新球探数据成功',
        data: null,
      };
    } catch (error) {
      this.logger.error('更新球探数据失败', error);
      return {
        code: -1,
        message: error.message || '更新球探数据失败',
        data: null,
      };
    }
  }

  /**
   * 接收Auth服务的角色初始化通知
   */
  @MessagePattern('character.initializeFromAuth')
  async initializeFromAuth(@Payload() payload: { characterId: string; userId: string; serverId: string; characterName: string; initialData?: any; injectedContext?: InjectedContext }) {
    this.logger.log(`📨 收到Auth服务角色初始化通知: ${payload.characterId}`);

    try {
      const character = await this.characterService.initializeFromAuth(payload);

      return {
        code: 0,
        message: '角色游戏数据初始化成功',
        data: {
          characterId: character.characterId,
          success: true,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 角色游戏数据初始化失败: ${payload.characterId}`, error);
      return {
        code: -1,
        message: '角色游戏数据初始化失败',
        error: error.message,
      };
    }
  }
}
