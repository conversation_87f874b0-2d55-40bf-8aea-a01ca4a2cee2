/**
 * Inventory Controller - 严格基于old项目API设计
 * 确保与old项目的API接口100%一致
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { InventoryService } from './inventory.service';
import { Cacheable, CacheEvict } from '@libs/redis';

import { InjectedContext } from '@libs/common/types';

import { XResponse } from '@libs/common/types/result.type';

@Controller()
export class InventoryController {
  private readonly logger = new Logger(InventoryController.name);

  constructor(private readonly inventoryService: InventoryService) {}

  /**
   * 获取角色背包数据
   * 对应old项目: Bag实体的toJSONforClient
   */
  @MessagePattern('inventory.getBag')
  @Cacheable({
    key: 'character:bag:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterBag(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取角色背包数据: ${payload.characterId}`);
    
    const bag = await this.inventoryService.getCharacterBag(payload.characterId);
    if (!bag) {
      // 如果不存在，初始化背包数据
      const newBag = await this.inventoryService.initCharacterBag(
        payload.characterId, 
        payload.serverId || 'server_001'
      );
      return { code: 0, data: newBag };
    }

    return { code: 0, data: bag };
  }

  /**
   * 获取背包列表（客户端格式）
   * 对应old项目: makeClientBagList方法
   */
  @MessagePattern('inventory.getBagList')
  @Cacheable({
    key: 'character:bagList:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getBagList(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取背包列表: ${payload.characterId}`);
    
    const bagList = await this.inventoryService.getBagList(payload.characterId);
    return { code: 0, data: bagList };
  }

  /**
   * 获取指定页签数据
   * 对应old项目: getOneBookMark方法
   */
  @MessagePattern('inventory.getBookMark')
  @Cacheable({
    key: 'character:bookMark:#{payload.characterId}:#{payload.bookMarkId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getBookMark(@Payload() payload: { characterId: string; bookMarkId: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取页签数据: ${payload.characterId}, 页签ID: ${payload.bookMarkId}`);
    
    const bookMark = await this.inventoryService.getBookMark(
      payload.characterId,
      payload.bookMarkId
    );

    if (bookMark) {
      return { code: 0, data: bookMark };
    } else {
      return { code: -1, message: '页签不存在' };
    }
  }

  /**
   * 添加物品到背包
   * 对应old项目: addToBag方法
   */
  @MessagePattern('inventory.addItemToBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addItemToBag(@Payload() payload: { characterId: string; bookMarkId: number; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`添加物品到背包: ${JSON.stringify(payload)}`);
    
    const result = await this.inventoryService.addItemToBag(
      payload.characterId,
      payload.bookMarkId,
      payload.itemId
    );

    return result;
  }

  /**
   * 从背包移除物品
   * 对应old项目: removeFromBag方法
   */
  @MessagePattern('inventory.removeItemFromBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeItemFromBag(@Payload() payload: { characterId: string; bookMarkId: number; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`从背包移除物品: ${JSON.stringify(payload)}`);
    
    const result = await this.inventoryService.removeItemFromBag(
      payload.characterId,
      payload.bookMarkId,
      payload.itemId
    );

    return result;
  }

  /**
   * 扩展背包容量
   * 对应old项目: expandBag方法
   */
  @MessagePattern('inventory.expandBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async expandBag(@Payload() payload: { characterId: string; bookMarkId: number; expandCount?: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`扩展背包: ${JSON.stringify(payload)}`);
    
    const result = await this.inventoryService.expandBag(
      payload.characterId,
      payload.bookMarkId,
      payload.expandCount || 1
    );

    return result;
  }

  /**
   * 整理背包
   * 对应old项目: sortBag方法
   */
  @MessagePattern('inventory.sortBag')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async sortBag(@Payload() payload: { characterId: string; bookMarkId: number; sortType?: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`整理背包: ${JSON.stringify(payload)}`);
    
    const result = await this.inventoryService.sortBag(
      payload.characterId,
      payload.bookMarkId,
      payload.sortType || 'default'
    );

    return result;
  }

  /**
   * 使用物品
   * 对应old项目: useItemMainType方法
   */
  @MessagePattern('inventory.useItem')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async useItem(@Payload() payload: { characterId: string; bookMarkId: number; itemId: string; configId: number; useType: number; subType: number; quantity?: number; heroId?: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`使用物品: ${JSON.stringify(payload)}`);
    
    const result = await this.inventoryService.useItem(
      payload.characterId,
      payload.bookMarkId,
      payload.itemId,
      payload.configId,
      payload.useType,
      payload.subType,
      payload.quantity || 1,
      payload.heroId
    );

    return result;
  }

  /**
   * 查找物品在背包中的位置
   * 对应old项目: findItemInBag方法
   */
  @MessagePattern('inventory.findItemInBag')
  @Cacheable({
    key: 'character:findItem:#{payload.characterId}:#{payload.itemId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async findItemInBag(@Payload() payload: { characterId: string; itemId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`查找物品位置: ${payload.characterId}, 物品: ${payload.itemId}`);
    
    const position = await this.inventoryService.findItemInBag(
      payload.characterId,
      payload.itemId
    );

    if (position) {
      return { code: 0, data: position };
    } else {
      return { code: -1, message: '物品不存在' };
    }
  }

  /**
   * 计算扩展费用
   * 对应old项目: calculateExpandCost方法
   */
  @MessagePattern('inventory.calculateExpandCost')
  @Cacheable({
    key: 'character:expandCost:#{payload.characterId}:#{payload.bookMarkId}:#{payload.expandCount}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async calculateExpandCost(@Payload() payload: { characterId: string; bookMarkId: number; expandCount?: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`计算扩展费用: ${JSON.stringify(payload)}`);
    
    const cost = await this.inventoryService.calculateExpandCost(
      payload.characterId,
      payload.bookMarkId,
      payload.expandCount || 1
    );

    return { code: 0, cost };
  }

  /**
   * 批量操作物品
   * 对应old项目的批量操作逻辑
   */
  @MessagePattern('inventory.batchOperateItems')
  @CacheEvict({
    key: 'character:bag*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchOperateItems(@Payload() payload: { characterId: string; operations: Array<{ type: 'add' | 'remove'; bookMarkId: number; itemId: string; }>; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`批量操作物品: ${payload.characterId}, 操作数量: ${payload.operations.length}`);
    
    const results: any[] = [];
    
    for (const operation of payload.operations) {
      let result;
      if (operation.type === 'add') {
        result = await this.inventoryService.addItemToBag(
          payload.characterId,
          operation.bookMarkId,
          operation.itemId
        );
      } else {
        result = await this.inventoryService.removeItemFromBag(
          payload.characterId,
          operation.bookMarkId,
          operation.itemId
        );
      }
      
      results.push({
        operation,
        result
      });
    }

    return { code: 0, results };
  }
}
