/**
 * Tactic Controller - 严格基于old项目API设计
 * 确保与old项目的API接口100%一致
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TacticService } from './tactic.service';
import { Cacheable, CacheEvict } from '@libs/redis';
import {SwapHerosDto} from "@character/common/dto/formation.dto";

import { InjectedContext } from '@libs/common/types';

import { XResponse } from '@libs/common/types/result.type';

@Controller()
export class TacticController {
  private readonly logger = new Logger(TacticController.name);

  constructor(private readonly tacticService: TacticService) {}

  /**
   * 获取角色战术数据
   * 对应old项目: 战术系统的数据获取
   */
  @MessagePattern('tactic.getTactics')
  @Cacheable({
    key: 'character:tactics:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterTactics(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取角色战术数据: ${payload.characterId}`);
    
    const tactics = await this.tacticService.getCharacterTactics(payload.characterId);
    if (!tactics) {
      // 如果不存在，初始化战术数据
      const newTactics = await this.tacticService.initCharacterTactics(
        payload.characterId, 
        payload.serverId || 'server_001'
      );
      return { code: 0, data: newTactics };
    }

    return { code: 0, data: tactics };
  }

  /**
   * 获取战术列表（客户端格式）
   * 对应old项目: makeClientTacticList方法
   */
  @MessagePattern('tactic.getTacticList')
  @Cacheable({
    key: 'character:tacticList:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTacticList(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取战术列表: ${payload.characterId}`);
    
    const tacticList = await this.tacticService.getTacticList(payload.characterId);
    return { code: 0, data: tacticList };
  }

  /**
   * 获取战术信息
   * 对应old项目: getTacticInfo方法
   */
  @MessagePattern('tactic.getTacticInfo')
  @Cacheable({
    key: 'character:tacticInfo:#{payload.characterId}:#{payload.tacticKey}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTacticInfo(@Payload() payload: { characterId: string; tacticKey: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取战术信息: ${payload.characterId}, 战术: ${payload.tacticKey}`);
    
    const tacticInfo = await this.tacticService.getTacticInfo(
      payload.characterId,
      payload.tacticKey
    );

    if (tacticInfo) {
      return { code: 0, data: tacticInfo };
    } else {
      return { code: -1, message: '战术不存在' };
    }
  }

  /**
   * 激活战术
   * 对应old项目: activateTactic方法
   */
  @MessagePattern('tactic.activateTactic')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async activateTactic(@Payload() payload: { characterId: string; tacticKey: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`激活战术: ${payload.characterId}, 战术: ${payload.tacticKey}`);
    
    const result = await this.tacticService.activateTactic(
      payload.characterId,
      payload.tacticKey
    );

    return result;
  }

  /**
   * 升级战术
   * 对应old项目: upgradeTactic方法
   */
  @MessagePattern('tactic.upgradeTactic')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeTactic(@Payload() payload: { characterId: string; tacticKey: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`升级战术: ${payload.characterId}, 战术: ${payload.tacticKey}`);
    
    const result = await this.tacticService.upgradeTactic(
      payload.characterId,
      payload.tacticKey
    );

    return result;
  }

  /**
   * 解锁战术
   * 对应old项目: unlockTactic方法
   */
  @MessagePattern('tactic.unlockTactic')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async unlockTactic(@Payload() payload: { characterId: string; tacticKey: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`解锁战术: ${payload.characterId}, 战术: ${payload.tacticKey}`);
    
    const result = await this.tacticService.unlockTactic(
      payload.characterId,
      payload.tacticKey
    );

    return result;
  }

  /**
   * 设置阵容战术
   * 对应old项目: setFormationTactics方法
   */
  @MessagePattern('tactic.setFormationTactics')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setFormationTactics(@Payload() payload: { characterId: string; formationId: string; attackTacticId: string; defenseTacticId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`设置阵容战术: ${JSON.stringify(payload)}`);
    
    const result = await this.tacticService.setFormationTactics(
      payload.characterId,
      payload.formationId,
      payload.attackTacticId,
      payload.defenseTacticId
    );

    return result;
  }

  /**
   * 获取阵容战术配置
   * 对应old项目: getFormationTactics方法
   */
  @MessagePattern('tactic.getFormationTactics')
  @Cacheable({
    key: 'character:formationTactics:#{payload.characterId}:#{payload.formationId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getFormationTactics(@Payload() payload: { characterId: string; formationId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取阵容战术配置: ${payload.characterId}, 阵容: ${payload.formationId}`);
    
    const config = await this.tacticService.getFormationTactics(
      payload.characterId,
      payload.formationId
    );

    if (config) {
      return { code: 0, data: config };
    } else {
      return { code: -1, message: '阵容战术配置不存在' };
    }
  }

  /**
   * 计算战术效果
   * 对应old项目: calcTacticEffects方法
   */
  @MessagePattern('tactic.calculateTacticEffects')
  @Cacheable({
    key: 'character:tacticEffects:#{payload.characterId}:#{payload.tacticKey}:#{payload.level}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async calculateTacticEffects(@Payload() payload: { characterId: string; tacticKey: string; level: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`计算战术效果: ${JSON.stringify(payload)}`);
    
    const effects = await this.tacticService.calculateTacticEffects(
      payload.characterId,
      payload.tacticKey,
      payload.level
    );

    return { code: 0, data: effects };
  }

  /**
   * 计算球员战术加成
   * 对应old项目: calcHeroTacticsAttr方法
   */
  @MessagePattern('tactic.calculateHeroBonus')
  @Cacheable({
    key: 'character:heroTacticBonus:#{payload.characterId}:#{payload.characterId}:#{payload.formationId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async calculateHeroBonus(@Payload() payload: { characterId: string; heroId: string; formationId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`计算球员战术加成: ${JSON.stringify(payload)}`);
    
    const bonus = await this.tacticService.calculateHeroBonus(
      payload.characterId,
      payload.heroId,
      payload.formationId
    );

    return { code: 0, data: bonus };
  }

  /**
   * 更新战术使用统计
   * 对应old项目: updateTacticUsageStats方法
   */
  @MessagePattern('tactic.updateTacticUsage')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateTacticUsage(@Payload() payload: { characterId: string; tacticKey: string; isWin: boolean; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`更新战术使用统计: ${JSON.stringify(payload)}`);
    
    await this.tacticService.updateTacticUsage(
      payload.characterId,
      payload.tacticKey,
      payload.isWin
    );

    return { code: 0 };
  }

  /**
   * 批量操作战术
   * 对应old项目的批量操作逻辑
   */
  @MessagePattern('tactic.batchOperateTactics')
  @CacheEvict({
    key: 'character:tactic*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchOperateTactics(@Payload() payload: { characterId: string; operations: Array<{ type: 'activate' | 'upgrade' | 'unlock'; tacticKey: string; }>; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`批量操作战术: ${payload.characterId}, 操作数量: ${payload.operations.length}`);
    
    const results: any[] = [];
    
    for (const operation of payload.operations) {
      let result;
      switch (operation.type) {
        case 'activate':
          result = await this.tacticService.activateTactic(payload.characterId, operation.tacticKey);
          break;
        case 'upgrade':
          result = await this.tacticService.upgradeTactic(payload.characterId, operation.tacticKey);
          break;
        case 'unlock':
          result = await this.tacticService.unlockTactic(payload.characterId, operation.tacticKey);
          break;
        default:
          result = { success: false, message: '未知操作类型' };
      }
      
      results.push({
        operation,
        result
      });
    }

    return { code: 0, results };
  }
}
