/**
 * Tactic Service - 严格基于old项目战术系统重新实现
 * 确保与old项目的业务逻辑100%一致
 */

import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { Tactic, TacticDocument, CharacterTactic, TacticType, TacticEffect, TacticEffectType, FormationTacticConfig } from '@character/common/schemas/tactic.schema';
import { TacticRepository } from '@character/common/repositories/tactic.repository';
import { GameConfigFacade } from '@libs/game-config';
import {SwapHerosDto} from "@character/common/dto/formation.dto";
import { CharacterService } from '../character/character.service';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class TacticService {
  private readonly logger = new Logger(TacticService.name);

  constructor(
    private readonly tacticRepository: TacticRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
  ) {}

  /**
   * 获取角色的战术数据
   * 基于old项目: 战术系统初始化
   */
  async getCharacterTactics(characterId: string): Promise<XResult<TacticDocument | null>> {
    return await this.tacticRepository.findByCharacterId(characterId, true) as TacticDocument | null;
  }

  /**
   * 初始化角色战术数据
   * 基于old项目: 战术系统初始化逻辑
   */
  async initCharacterTactics(characterId: string, serverId: string): Promise<XResult<TacticDocument>> {
    const uid = characterId; // 在新架构中，uid就是characterId

    // 初始化基础战术 - 基于old项目initTactics
    const initialTactics = await this.initBasicTactics();

    const tacticData = {
      uid,
      characterId,
      serverId,
      tactics: initialTactics,
      formationTactics: [],
      combinations: [],
      heroBonusCache: [],
      createTime: Date.now(),
      updateTime: Date.now(),
    };

    return await this.tacticRepository.create(tacticData);
  }

  /**
   * 获取战术信息
   * 基于old项目: getTacticInfo方法
   */
  async getTacticInfo(characterId: string, tacticKey: string): Promise<XResult<CharacterTactic | null>> {
    this.logger.log(`获取战术信息: ${characterId}, 战术: ${tacticKey}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return null;
    }

    return tactics.getTactic(tacticKey);
  }

  /**
   * 激活战术
   * 基于old项目: activateTactic方法
   */
  async activateTactic(characterId: string, tacticKey: string): Promise<XResult<{ success: boolean; message?: string }>> {
    this.logger.log(`激活战术: ${characterId}, 战术: ${tacticKey}`);

    let tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      tactics = await this.initCharacterTactics(characterId, 'server_001');
    }

    const success = tactics.activateTactic(tacticKey);
    if (success) {
      await tactics.save();
      return { success: true };
    } else {
      return { success: false, message: '战术未解锁或不存在' };
    }
  }

  /**
   * 升级战术
   * 基于old项目: upgradeTactic方法
   */
  async upgradeTactic(characterId: string, tacticKey: string): Promise<XResult<{ success: boolean; message?: string; newLevel?: number }>> {
    this.logger.log(`升级战术: ${characterId}, 战术: ${tacticKey}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return { success: false, message: '战术数据不存在' };
    }

    const tactic = tactics.getTactic(tacticKey);
    if (!tactic) {
      return { success: false, message: '战术不存在' };
    }

    const oldLevel = tactic.level;
    const success = tactics.upgradeTactic(tacticKey);
    
    if (success) {
      await tactics.save();
      // 清除相关缓存
      tactics.invalidateHeroBonusCache();
      await tactics.save();
      
      return { success: true, newLevel: tactic.level };
    } else {
      return { success: false, message: '经验不足或已达最大等级' };
    }
  }

  /**
   * 解锁战术
   * 基于old项目: unlockTactic方法
   */
  async unlockTactic(characterId: string, tacticKey: string): Promise<XResult<{ success: boolean; message?: string }>> {
    this.logger.log(`解锁战术: ${characterId}, 战术: ${tacticKey}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return { success: false, message: '战术数据不存在' };
    }

    const tactic = tactics.getTactic(tacticKey);
    if (!tactic) {
      return { success: false, message: '战术不存在' };
    }

    if (tactic.isUnlocked) {
      return { success: false, message: '战术已解锁' };
    }

    // TODO: 检查解锁条件
    const canUnlock = await this.checkUnlockConditions(characterId, tactic);
    if (!canUnlock) {
      return { success: false, message: '不满足解锁条件' };
    }

    const success = tactics.unlockTactic(tacticKey);
    if (success) {
      await tactics.save();
      return { success: true };
    } else {
      return { success: false, message: '解锁失败' };
    }
  }

  /**
   * 设置阵容战术
   * 基于old项目: setFormationTactics方法
   */
  async setFormationTactics(characterId: string, formationId: string, attackTacticId: string, defenseTacticId: string): Promise<XResult<{ success: boolean; message?: string }>> {
    this.logger.log(`设置阵容战术: ${characterId}, 阵容: ${formationId}, 进攻: ${attackTacticId}, 防守: ${defenseTacticId}`);

    let tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      tactics = await this.initCharacterTactics(characterId, 'server_001');
    }

    // 验证战术是否存在且已解锁
    const attackTactic = tactics.getTactic(attackTacticId);
    const defenseTactic = tactics.getTactic(defenseTacticId);

    if (!attackTactic || !attackTactic.isUnlocked) {
      return { success: false, message: '进攻战术不存在或未解锁' };
    }

    if (!defenseTactic || !defenseTactic.isUnlocked) {
      return { success: false, message: '防守战术不存在或未解锁' };
    }

    tactics.setFormationTactics(formationId, attackTacticId, defenseTacticId);
    await tactics.save();

    return { success: true };
  }

  /**
   * 获取阵容战术配置
   * 基于old项目: getFormationTactics方法
   */
  async getFormationTactics(characterId: string, formationId: string): Promise<XResult<FormationTacticConfig | null>> {
    this.logger.log(`获取阵容战术配置: ${characterId}, 阵容: ${formationId}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return null;
    }

    return tactics.getFormationTactics(formationId);
  }

  /**
   * 计算战术效果
   * 基于old项目: calcTacticEffects方法
   */
  async calculateTacticEffects(characterId: string, tacticKey: string, level: number): Promise<XResult<TacticEffect[]>> {
    this.logger.log(`计算战术效果: ${characterId}, 战术: ${tacticKey}, 等级: ${level}`);

    // TODO: 从配置表获取战术效果配置
    const tacticConfigs = await this.gameConfig.tactic.getAll();
    const config = tacticConfigs.find(c => c.name === tacticKey);
    
    if (!config) {
      return [];
    }

    // 根据等级计算效果
    const effects: TacticEffect[] = [];

    // 基于old项目: 战术效果计算逻辑实现
    if (config) {
      // 1. 基础效果计算（基于old项目战术配置表）
      const baseEffect = this.calculateBaseEffect(config, level);
      if (baseEffect) {
        effects.push(baseEffect);
      }

      // 2. 等级加成效果（基于old项目：每级增加效果）
      const levelBonus = this.calculateLevelBonus(config, level);
      if (levelBonus) {
        effects.push(levelBonus);
      }

      // 3. 组合效果（基于old项目：多个战术的组合效果）
      const comboEffects = await this.calculateComboEffects(characterId, tacticKey, level);
      effects.push(...comboEffects);

      this.logger.debug(`战术效果计算完成: ${tacticKey}, 效果数量: ${effects.length}`);
    } else {
      this.logger.warn(`未找到战术配置: ${tacticKey}`);
    }

    return effects;
  }

  /**
   * 计算球员战术加成
   * 基于old项目: calcHeroTacticsAttr方法
   */
  async calculateHeroBonus(characterId: string, heroId: string, formationId: string): Promise<XResult<any>> {
    this.logger.log(`计算球员战术加成: ${characterId}, 球员: ${heroId}, 阵容: ${formationId}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return { attributeBonus: {}, skillBonus: {} };
    }

    return tactics.calculateHeroBonus(heroId, formationId);
  }

  /**
   * 更新战术使用统计
   * 基于old项目: updateTacticUsageStats方法
   */
  async updateTacticUsage(characterId: string, tacticKey: string, isWin: boolean): Promise<XResult<void>> {
    this.logger.log(`更新战术使用统计: ${characterId}, 战术: ${tacticKey}, 胜利: ${isWin}`);

    const tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      return;
    }

    tactics.updateTacticUsage(tacticKey, isWin);
    await this.tacticRepository.update(tactics._id.toString(), tactics.toObject());
  }

  /**
   * 获取战术列表（客户端数据）
   * 基于old项目: makeClientTacticList方法
   */
  async getTacticList(characterId: string): Promise<XResult<any[]>> {
    this.logger.log(`获取战术列表: ${characterId}`);

    let tactics = await this.getCharacterTactics(characterId);
    if (!tactics) {
      tactics = await this.initCharacterTactics(characterId, 'server_001');
    }

    return tactics.makeClientTacticList();
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 初始化基础战术
   * 基于old项目: initTactics方法
   */
  private async initBasicTactics(): Promise<XResult<CharacterTactic[]>> {
    // 基于old项目: 从Tactic配置表获取基础战术
    try {
      const tacticConfigs = await this.gameConfig.tactic.getAll();

      // 筛选出基础战术（基于old项目：level=1且managerLevel=0的战术为基础战术）
      const basicTacticConfigs = tacticConfigs.filter(config =>
        config.level === 1 && (config.managerLevel === 0 || !config.managerLevel)
      );

      if (basicTacticConfigs.length > 0) {
        const configBasedTactics: CharacterTactic[] = [];

        for (const config of basicTacticConfigs) {
          // 基于old项目：战术类型通过type字段确定
          const tacticType = this.determineTacticType(config);

          const basicTactic: CharacterTactic = {
            tacticId: config.id?.toString() || '101',
            tacticKey: config.name || `tactic_${config.id}`,
            name: config.name || '基础战术',
            level: 1,
            experience: 0,
            isActive: true, // 基础战术默认激活
            isUnlocked: true,
            type: tacticType,
            currentEffects: [],
            unlockConditions: [],
            unlockTime: Date.now(),
            lastUsedTime: Date.now(),
            usageCount: 0,
            winCount: 0,
            totalMatches: 0,
            winRate: 0,
            canUpgrade: config.level < 5, // 基于old项目：战术最高5级
            requiredExperience: config.proficiency || 100,
          };

          configBasedTactics.push(basicTactic);
        }

        this.logger.debug(`从配置表加载基础战术: ${configBasedTactics.length}个`);
        return configBasedTactics;
      }
    } catch (error) {
      this.logger.error('从配置表获取基础战术失败，使用默认配置', error);
    }

    // 配置表获取失败或为空时，使用硬编码的默认战术
    const basicTactics: CharacterTactic[] = [
      {
        tacticId: '101',
        tacticKey: 'basic_attack',
        name: '基础进攻',
        level: 1,
        experience: 0,
        isActive: true,
        isUnlocked: true,
        type: TacticType.ATTACK,
        currentEffects: [],
        unlockConditions: [],
        unlockTime: Date.now(),
        lastUsedTime: Date.now(),
        usageCount: 0,
        winCount: 0,
        totalMatches: 0,
        winRate: 0,
        canUpgrade: true,
        requiredExperience: 100,
      },
      {
        tacticId: '1101',
        tacticKey: 'basic_defense',
        name: '基础防守',
        level: 1,
        experience: 0,
        isActive: true,
        isUnlocked: true,
        type: TacticType.DEFENSE,
        currentEffects: [],
        unlockConditions: [],
        unlockTime: Date.now(),
        lastUsedTime: Date.now(),
        usageCount: 0,
        winCount: 0,
        totalMatches: 0,
        winRate: 0,
        canUpgrade: true,
        requiredExperience: 100,
      },
    ];

    return basicTactics;
  }

  /**
   * 检查战术解锁条件
   * 基于old项目: checkTacticUnlockCondition方法
   */
  private async checkUnlockConditions(characterId: string, tactic: CharacterTactic): Promise<XResult<boolean>> {
    // 基于old项目: 战术解锁条件检查逻辑
    try {
      // 1. 获取战术配置中的解锁条件
      const tacticConfigs = await this.gameConfig.tactic.getAll();
      const config = tacticConfigs.find(c => c.name === tactic.tacticKey);

      if (!config) {
        this.logger.warn(`未找到战术配置: ${tactic.tacticKey}`);
        return false;
      }

      // 2. 检查经理等级要求（基于old项目：managerLevel字段）
      if (config.managerLevel && config.managerLevel > 0) {
        const characterLevel = await this.getCharacterLevel(characterId);
        if (characterLevel < config.managerLevel) {
          this.logger.debug(`经理等级不足: 需要${config.managerLevel}, 当前${characterLevel}`);
          return false;
        }
      }

      // 3. 检查战术等级要求（基于old项目：level字段）
      if (config.level && config.level > 1) {
        // 检查是否有前置等级的同类战术
        const existingTactic = tactic;
        if (!existingTactic || existingTactic.level < config.level - 1) {
          this.logger.debug(`战术等级不足: 需要前置等级${config.level - 1}`);
          return false;
        }
      }

      // 4. 检查熟练度要求（基于old项目：proficiency字段）
      if (config.proficiency && config.proficiency > 0) {
        const currentProficiency = tactic.experience || 0;
        if (currentProficiency < config.proficiency) {
          this.logger.debug(`熟练度不足: 需要${config.proficiency}, 当前${currentProficiency}`);
          return false;
        }
      }

      // 5. 检查物品要求（基于old项目：itemId和itemNumber字段）
      if (config.itemId && config.itemNumber && config.itemNumber > 0) {
        const hasRequiredItem = await this.checkRequiredItem(characterId, config.itemId, config.itemNumber);
        if (!hasRequiredItem) {
          this.logger.debug(`所需物品不足: 物品${config.itemId} 需要${config.itemNumber}个`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('检查解锁条件失败', error);
      return false;
    }
  }

  /**
   * 计算基础效果
   * 基于old项目: Tactic配置表的AddType和AddValue字段
   */
  private calculateBaseEffect(config: any, level: number): TacticEffect | null {
    try {
      // 基于old项目：战术配置表包含AddType1-5和AddValue1-5字段
      // AddType定义效果类型，AddValue定义效果数值
      const effects: TacticEffect[] = [];

      // 遍历5个效果槽位（基于old项目Tactic配置表结构）
      for (let i = 1; i <= 5; i++) {
        const addType = config[`addType${i}`];
        const addValue = config[`addValue${i}`];

        if (addType && addValue) {
          const effect = this.createEffectFromTypeAndValue(addType, addValue, level, config.name);
          if (effect) {
            effects.push(effect);
          }
        }
      }

      // 返回第一个效果作为基础效果，其他效果通过其他方法返回
      return effects.length > 0 ? effects[0] : null;
    } catch (error) {
      this.logger.error('计算基础效果失败', error);
      return null;
    }
  }

  /**
   * 根据类型和数值创建效果
   * 基于old项目: AddType字段的含义映射
   */
  private createEffectFromTypeAndValue(addType: number, addValue: number, level: number, tacticName: string): TacticEffect | null {
    // 基于old项目：AddType字段的含义
    const typeMapping = {
      1: { attribute: 'speed', target: 'hero' },           // 速度
      2: { attribute: 'shooting', target: 'hero' },        // 射门
      3: { attribute: 'passing', target: 'hero' },         // 传球
      4: { attribute: 'defending', target: 'hero' },       // 防守
      5: { attribute: 'dribbling', target: 'hero' },       // 盘带
      6: { attribute: 'physicality', target: 'hero' },     // 身体
      7: { attribute: 'goalkeeping', target: 'hero' },     // 门将
      8: { attribute: 'attack', target: 'team' },          // 团队进攻
      9: { attribute: 'defend', target: 'team' },          // 团队防守
      10: { attribute: 'morale', target: 'team' },         // 士气
      11: { attribute: 'formation', target: 'formation' }, // 阵型
      12: { attribute: 'counter', target: 'special' },     // 反击
    };

    const mapping = typeMapping[addType];
    if (!mapping) {
      return null;
    }

    return {
      type: mapping.target === 'hero' ? TacticEffectType.ATTRIBUTE_BONUS : TacticEffectType.FORMATION_BONUS,
      target: mapping.target,
      attribute: mapping.attribute,
      value: addValue,
      levelMultiplier: level, // 等级倍数
      formula: `${addValue} * ${level}`, // 计算公式
      duration: 0, // 战术效果通常是永久的
      description: `${tacticName} - ${mapping.attribute}加成`,
    };
  }

  /**
   * 计算等级加成
   * 基于old项目: 战术等级加成逻辑
   */
  private calculateLevelBonus(config: any, level: number): TacticEffect | null {
    try {
      // 基于old项目：高等级战术有额外加成
      if (level <= 1) {
        return null; // 1级没有额外加成
      }

      const bonusValue = Math.floor((level - 1) * (config.levelBonus || 1));
      if (bonusValue <= 0) {
        return null;
      }

      const levelBonusEffect: TacticEffect = {
        type: TacticEffectType.SKILL_BONUS,
        target: config.target || 'team',
        attribute: config.attribute || 'attack',
        value: bonusValue,
        levelMultiplier: 1,
        formula: `${bonusValue} * 1`,
        duration: 0,
        description: `${config.name || '未知战术'} - 等级加成`,
      };

      return levelBonusEffect;
    } catch (error) {
      this.logger.error('计算等级加成失败', error);
      return null;
    }
  }

  /**
   * 计算组合效果
   * 基于old项目: 多个战术的组合效果
   */
  private async calculateComboEffects(characterId: string, tacticKey: string, level: number): Promise<XResult<TacticEffect[]>> {
    try {
      const comboEffects: TacticEffect[] = [];

      // TODO: 获取角色的所有已激活战术
      // const activeTactics = await this.getActiveTactics(characterId);

      // TODO: 检查战术组合配置
      // const comboConfigs = await this.gameConfig.tacticCombo?.getAll();

      // 基于old项目：某些战术组合会产生额外效果
      // 暂时返回空数组，等配置表完善后实现

      return comboEffects;
    } catch (error) {
      this.logger.error('计算组合效果失败', error);
      return [];
    }
  }

  /**
   * 获取角色等级
   * 基于old项目: player.level
   * 修复：直接调用同服务内的CharacterService，避免微服务调用
   */
  private async getCharacterLevel(characterId: string): Promise<XResult<number>> {
    try {
      // 直接调用CharacterService获取角色信息
      const characterInfo = await this.characterService.getCharacterInfo(characterId);
      return characterInfo.level || 1;
    } catch (error) {
      this.logger.error('获取角色等级失败', error);
      return 1;
    }
  }

  /**
   * 检查前置战术
   * 基于old项目: 战术树依赖关系
   */
  private async checkPrerequisiteTactics(characterId: string, prerequisiteTactics: string[]): Promise<XResult<boolean>> {
    try {
      const characterTactics = await this.getTacticList(characterId);

      for (const requiredTactic of prerequisiteTactics) {
        const hasTactic = characterTactics.some(t =>
          t.tacticKey === requiredTactic && t.level > 0
        );

        if (!hasTactic) {
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('检查前置战术失败', error);
      return false;
    }
  }

  /**
   * 检查单个所需物品
   * 基于old项目: ItemID和ItemNumber字段
   */
  private async checkRequiredItem(characterId: string, itemId: number, itemNumber: number): Promise<XResult<boolean>> {
    try {
      // TODO: 调用Character服务检查物品
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'inventory.checkItem',
      //   { characterId, itemId, quantity: itemNumber }
      // );
      //
      // return result.data.hasItem;

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查所需物品失败', error);
      return false;
    }
  }

  /**
   * 检查所需物品列表
   * 基于old项目: 扩展的物品解锁条件
   */
  private async checkRequiredItems(characterId: string, requiredItems: any[]): Promise<XResult<boolean>> {
    try {
      // TODO: 调用Character服务检查物品
      // for (const item of requiredItems) {
      //   const result = await this.microserviceClient.call(
      //     MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //     'inventory.checkItem',
      //     { characterId, itemId: item.itemId, quantity: item.quantity }
      //   );
      //
      //   if (!result.data.hasItem) {
      //     return false;
      //   }
      // }

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查所需物品失败', error);
      return false;
    }
  }

  /**
   * 检查所需成就
   * 基于old项目: 成就解锁系统
   */
  private async checkRequiredAchievements(characterId: string, requiredAchievements: string[]): Promise<XResult<boolean>> {
    try {
      // TODO: 调用Achievement服务检查成就
      // for (const achievementId of requiredAchievements) {
      //   const result = await this.microserviceClient.call(
      //     MICROSERVICE_NAMES.ACHIEVEMENT_SERVICE,
      //     'achievement.checkAchievement',
      //     { characterId, achievementId }
      //   );
      //
      //   if (!result.data.hasAchievement) {
      //     return false;
      //   }
      // }

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查所需成就失败', error);
      return false;
    }
  }

  /**
   * 确定战术类型
   * 基于old项目: Tactic配置表的type字段和效果分析
   */
  private determineTacticType(config: any): TacticType {
    // 基于old项目：通过type字段确定战术类型
    if (config.type) {
      switch (config.type) {
        case 1:
        case 'attack':
          return TacticType.ATTACK;
        case 2:
        case 'defense':
          return TacticType.DEFENSE;
        case 3:
        case 'balanced':
          return TacticType.BALANCED;
        case 4:
        case 'counter':
          return TacticType.COUNTER;
        default:
          break;
      }
    }

    // 如果没有type字段，通过效果类型分析（基于old项目AddType字段）
    const attackTypes = [1, 2, 3, 5, 8]; // 速度、射门、传球、盘带、团队进攻
    const defenseTypes = [4, 6, 7, 9];   // 防守、身体、门将、团队防守

    let attackScore = 0;
    let defenseScore = 0;

    for (let i = 1; i <= 5; i++) {
      const addType = config[`addType${i}`];
      if (attackTypes.includes(addType)) {
        attackScore++;
      } else if (defenseTypes.includes(addType)) {
        defenseScore++;
      }
    }

    if (attackScore > defenseScore) {
      return TacticType.ATTACK;
    } else if (defenseScore > attackScore) {
      return TacticType.DEFENSE;
    } else {
      return TacticType.BALANCED;
    }
  }
}
