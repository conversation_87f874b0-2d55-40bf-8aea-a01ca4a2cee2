{"timestamp": "2025-08-17T05:29:20.051Z", "stats": {"filesProcessed": 1, "repositoryFiles": 0, "serviceFiles": 0, "controllerFiles": 1, "throwsConverted": 0, "functionsUpdated": 22, "messagePatternUpdated": 22, "importsAdded": 1, "errors": []}, "conversions": [{"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "createCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "loginCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "logoutCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getCharacterInfo", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "updateCharacter", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getCharacterList", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "buyEnergy", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "levelUp", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "completeCreateStep", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "finishGuide", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "setCharacterBelief", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "useRedeemCode", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "updateContinuedBuff", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "searchCharacterByName", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getPersonInfo", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "createRole", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "modifyCharacterName", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getEnergyReward", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "costCashTask", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "getScoutData", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "updateScoutData", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}, {"file": "E:/football manager/server-new/apps/character/src/modules/character/character.controller.ts", "layer": "controller", "className": "CharacterController", "methodName": "initializeFromAuth", "changes": ["返回类型更新为MicroserviceResponse", "转换为统一响应格式"]}], "errors": []}