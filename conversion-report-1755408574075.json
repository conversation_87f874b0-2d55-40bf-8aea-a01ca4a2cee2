{"timestamp": "2025-08-17T05:29:34.075Z", "stats": {"filesProcessed": 1, "repositoryFiles": 1, "serviceFiles": 0, "controllerFiles": 0, "throwsConverted": 13, "functionsUpdated": 14, "messagePatternUpdated": 0, "importsAdded": 1, "errors": []}, "conversions": [{"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "create", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findById", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByCharacterId", "changes": ["返回类型更新为Result<T>"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByUserId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByName", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByOpenId", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "update", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "softDelete", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "existsByName", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findWithPagination", "changes": ["使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "findByLevelRange", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "getOnlineCount", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "bulkUpdate", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}, {"file": "E:/football manager/server-new/apps/character/src/common/repositories/character.repository.ts", "layer": "repository", "className": "CharacterRepository", "methodName": "getServerStats", "changes": ["返回类型更新为Result<T>", "使用ExceptionToResultUtils包装"]}], "errors": []}