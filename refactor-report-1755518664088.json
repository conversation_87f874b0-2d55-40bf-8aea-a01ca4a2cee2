{"totalFiles": 38, "processedFiles": 10, "totalThrows": 125, "convertedThrows": 22, "errors": [], "fileDetails": [{"path": "apps\\character\\src\\modules\\inventory\\inventory.service.ts", "totalThrows": 9, "convertedThrows": 2, "throwDetails": [{"line": 68, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 184, "original": "throw new NotFoundException('背包不存在');", "converted": true, "result": "return ResultUtils.failure(UNKNOWN_ERROR, \"背包不存在\");", "exceptionType": "NotFoundException", "code": "UNKNOWN_ERROR", "message": "背包不存在"}, {"line": 190, "original": "throw new NotFoundException('物品不存在');", "converted": true, "result": "return ResultUtils.failure(UNKNOWN_ERROR, \"物品不存在\");", "exceptionType": "NotFoundException", "code": "UNKNOWN_ERROR", "message": "物品不存在"}, {"line": 751, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 771, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 791, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 811, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 831, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 851, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 68, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 751, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 771, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 791, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 811, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 831, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 851, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\modules\\formation\\formation.service.ts", "totalThrows": 2, "convertedThrows": 1, "throwDetails": [{"line": 78, "original": "throw new NotFoundException(`角色阵容数据不存在: ${characterId}`);", "converted": true, "result": "return ResultUtils.failure(UNKNOWN_ERROR, \"未知错误\");", "exceptionType": "NotFoundException", "code": "UNKNOWN_ERROR", "message": "未知错误"}, {"line": 1230, "original": "throw new Error('无法获取任何球员信息');", "converted": false, "reason": "不是NestJS异常: Error"}]}, {"path": "apps\\character\\src\\modules\\character\\character.service.ts", "totalThrows": 39, "convertedThrows": 32, "throwDetails": [{"line": 48, "original": "throw new BadRequestException({\r\n          code: ErrorCode.CHARACTER_NAME_TAKEN,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NAME_TAKEN.toString(), ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN]);", "exceptionType": "BadRequestException", "code": "ErrorCode.CHARACTER_NAME_TAKEN", "message": "ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN]"}, {"line": 53, "original": "throw new BadRequestException({\r\n          code: ErrorCode.INVALID_PARAMETER,\r\n          message: '角色ID必须由Auth服务提供',\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.INVALID_PARAMETER.toString(), \"角色ID必须由Auth服务提供\");", "exceptionType": "BadRequestException", "code": "ErrorCode.INVALID_PARAMETER", "message": "角色ID必须由Auth服务提供"}, {"line": 80, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 130, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 162, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 196, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 217, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 228, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 234, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 245, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 257, "original": "throw new BadRequestException({\r\n            code: ErrorCode.CHARACTER_NAME_TAKEN,\r\n            message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],\r\n          });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NAME_TAKEN.toString(), ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN]);", "exceptionType": "BadRequestException", "code": "ErrorCode.CHARACTER_NAME_TAKEN", "message": "ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN]"}, {"line": 267, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 309, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 320, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 337, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 348, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 353, "original": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_CURRENCY,\r\n          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.INSUFFICIENT_CURRENCY.toString(), ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY]);", "exceptionType": "BadRequestException", "code": "ErrorCode.INSUFFICIENT_CURRENCY", "message": "ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY]"}, {"line": 370, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 381, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 390, "original": "throw new BadRequestException({\r\n          code: ErrorCode.INSUFFICIENT_CURRENCY,\r\n          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.INSUFFICIENT_CURRENCY.toString(), ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY]);", "exceptionType": "BadRequestException", "code": "ErrorCode.INSUFFICIENT_CURRENCY", "message": "ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY]"}, {"line": 413, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 424, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 431, "original": "throw new BadRequestException({\r\n          code: ErrorCode.INVALID_PARAMETER,\r\n          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.INVALID_PARAMETER.toString(), ErrorMessages[ErrorCode.INVALID_PARAMETER]);", "exceptionType": "BadRequestException", "code": "ErrorCode.INVALID_PARAMETER", "message": "ErrorMessages[ErrorCode.INVALID_PARAMETER]"}, {"line": 460, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 471, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 487, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 498, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 521, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 624, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 634, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 645, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 650, "original": "throw new BadRequestException({\r\n          code: ErrorCode.REDEEM_CODE_ALREADY_USED,\r\n          message: ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.REDEEM_CODE_ALREADY_USED.toString(), ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED]);", "exceptionType": "BadRequestException", "code": "ErrorCode.REDEEM_CODE_ALREADY_USED", "message": "ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED]"}, {"line": 669, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 680, "original": "throw new NotFoundException({\r\n          code: ErrorCode.CHARACTER_NOT_FOUND,\r\n          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],\r\n        });", "converted": true, "result": "return ResultUtils.failure(ErrorCode.CHARACTER_NOT_FOUND.toString(), ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]);", "exceptionType": "NotFoundException", "code": "ErrorCode.CHARACTER_NOT_FOUND", "message": "ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND]"}, {"line": 694, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 1792, "original": "throw new Error('角色不存在');", "converted": false, "reason": "不是NestJS异常: Error"}, {"line": 1821, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 1835, "original": "throw new Error('角色不存在');", "converted": false, "reason": "不是NestJS异常: Error"}, {"line": 1848, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 80, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '创建失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 133, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 199, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 223, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 240, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '获取失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 276, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '更新失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 321, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 349, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '添加失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 385, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 431, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 481, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 511, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 548, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 664, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '设置失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 702, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '操作失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 730, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '更新失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 1860, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 1887, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\config\\app.config.ts", "totalThrows": 1, "convertedThrows": 0, "throwDetails": [{"line": 75, "original": "throw new Error(`Character服务配置验证失败: ${error.message}`);", "converted": false, "reason": "不是NestJS异常: Error"}]}, {"path": "apps\\character\\src\\common\\repositories\\tactic.repository.ts", "totalThrows": 11, "convertedThrows": 0, "throwDetails": [{"line": 26, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 47, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 59, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 71, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 87, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 122, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 137, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 162, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 179, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 204, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 26, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 47, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 59, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 71, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 87, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 122, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 137, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 162, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 179, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 204, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\common\\repositories\\item.repository.ts", "totalThrows": 12, "convertedThrows": 0, "throwDetails": [{"line": 28, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 61, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 82, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 112, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 132, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 145, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 167, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 179, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 208, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 220, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 242, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 291, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 28, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 61, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 82, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 112, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 132, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 145, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 167, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 179, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 208, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 220, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 242, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 291, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\common\\repositories\\inventory.repository.ts", "totalThrows": 17, "convertedThrows": 0, "throwDetails": [{"line": 27, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 58, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 73, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 94, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 114, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 127, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 173, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 199, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 220, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 241, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 266, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 328, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 373, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 423, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 445, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 458, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 27, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 58, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 73, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 94, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 114, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 127, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 173, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 199, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 220, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 241, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 266, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 328, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 373, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 423, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 445, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 458, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\common\\repositories\\formation.repository.ts", "totalThrows": 18, "convertedThrows": 1, "throwDetails": [{"line": 34, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 50, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 86, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 99, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 127, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 142, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 165, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 186, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 199, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 226, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 289, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '查找失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 318, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 341, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 399, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 421, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 432, "original": "throw new Error('源阵容不存在');", "converted": false, "reason": "不是NestJS异常: Error"}, {"line": 457, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 479, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 34, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 50, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 86, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 99, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 127, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 142, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 165, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 186, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 199, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 226, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 318, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 341, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 399, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 421, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 457, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 479, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\common\\repositories\\character.repository.ts", "totalThrows": 13, "convertedThrows": 1, "throwDetails": [{"line": 34, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 46, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 69, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 92, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 108, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 128, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 173, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 218, "original": "throw error;", "converted": true, "result": "return ResultUtils.failure(\n        CommonErrorCodes.UNKNOWN_ERROR,\n        '查找失败'\n      );", "exceptionType": "CatchBlockThrow", "code": "CommonErrorCodes.UNKNOWN_ERROR", "message": "操作失败"}, {"line": 251, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 273, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 295, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 347, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 34, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 46, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 69, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 92, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 108, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 128, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 150, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 173, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 251, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 273, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 295, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}, {"line": 347, "original": "throw error;", "converted": false, "reason": "方法返回类型不是Result类型"}]}, {"path": "apps\\character\\src\\common\\decorators\\secure-repository.decorator.ts", "totalThrows": 3, "convertedThrows": 0, "throwDetails": [{"line": 40, "original": "throw error;", "converted": false, "reason": "无法找到包含的方法"}, {"line": 61, "original": "throw new Error(`缺少必需字段: ${field}`);", "converted": false, "reason": "不是NestJS异常: Error"}, {"line": 147, "original": "throw new Error('检测到潜在的安全威胁');", "converted": false, "reason": "不是NestJS异常: Error"}, {"line": 40, "original": "throw error;", "converted": false, "reason": "无法找到包含的方法"}]}]}