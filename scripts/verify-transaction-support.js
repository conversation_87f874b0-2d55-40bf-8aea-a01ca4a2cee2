/**
 * 验证MongoDB事务支持脚本
 */

const mongoose = require('mongoose');

async function verifyTransactionSupport() {
  try {
    console.log('🔍 连接到MongoDB...');
    
    // 使用新的连接字符串
    await mongoose.connect('********************************************************************************');
    
    console.log('✅ MongoDB连接成功');
    
    // 检查副本集状态
    const admin = mongoose.connection.db.admin();
    const isMasterResult = await admin.command({ isMaster: 1 });
    
    console.log('📊 MongoDB状态信息:');
    console.log(`   节点类型: ${isMasterResult.ismaster ? 'PRIMARY' : 'SECONDARY'}`);
    console.log(`   副本集名称: ${isMasterResult.setName || '无'}`);
    console.log(`   支持事务: ${!!(isMasterResult.setName || isMasterResult.msg === 'isdbgrid')}`);
    
    if (isMasterResult.setName) {
      console.log('🎉 副本集配置成功！支持事务功能');
      
      // 测试简单事务
      console.log('\n🧪 测试事务功能...');
      await testTransaction();
      
    } else {
      console.log('❌ 副本集配置失败，仍为单节点模式');
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

async function testTransaction() {
  const session = await mongoose.startSession();
  
  try {
    // 创建测试集合
    const TestCollection = mongoose.model('TransactionTest', new mongoose.Schema({
      name: String,
      value: Number
    }));
    
    // 开始事务
    session.startTransaction();
    
    // 在事务中执行操作
    await TestCollection.create([{ name: 'test1', value: 100 }], { session });
    await TestCollection.create([{ name: 'test2', value: 200 }], { session });
    
    // 提交事务
    await session.commitTransaction();
    
    console.log('✅ 事务测试成功！');
    
    // 清理测试数据
    await TestCollection.deleteMany({ name: { $in: ['test1', 'test2'] } });
    
  } catch (error) {
    await session.abortTransaction();
    console.error('❌ 事务测试失败:', error.message);
  } finally {
    session.endSession();
  }
}

// 运行验证
verifyTransactionSupport().catch(console.error);
