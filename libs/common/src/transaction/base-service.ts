import { Logger } from '@nestjs/common';
import { XResult, ResultUtils, FailureResult, SuccessResult } from '../types/result.type';
import { TransactionManager, TransactionOptions, TransactionOperation } from './transaction-manager';

/**
 * 简洁实用的Service基类
 * 
 * 特性：
 * - 统一的事务管理
 * - Result模式集成
 * - 错误传递工具
 * - 日志记录
 * 
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepo: CharacterRepository,
 *     private readonly guildRepo: GuildRepository
 *   ) {
 *     super('CharacterService');
 *   }
 * 
 *   async transferCharacter(
 *     characterId: string, 
 *     guildId: string
 *   ): Promise<Result<CharacterDocument>> {
 *     return this.executeTransaction(async (session) => {
 *       // 检查角色存在
 *       const characterResult = await this.characterRepo.findById(characterId, session);
 *       const error = this.propagateError<CharacterDocument>(characterResult);
 *       if (error) return error;
 * 
 *       // 执行转会
 *       return await this.characterRepo.transferToGuild(characterId, guildId, session);
 *     });
 *   }
 * }
 * ```
 */
export abstract class BaseService {
  protected readonly logger: Logger;

  constructor(loggerContext: string) {
    this.logger = new Logger(loggerContext);
  }

  // ========== 事务管理 ==========

  /**
   * 执行事务操作
   * 
   * @param operation 事务操作函数
   * @param options 事务配置选项
   * @returns Promise<Result<T>> 执行结果
   */
  protected async executeTransaction<T>(
    operation: TransactionOperation<T>,
    options?: TransactionOptions
  ): Promise<XResult<T>> {
    try {
      this.logger.log('开始执行事务');
      const result = await TransactionManager.execute(operation, options);
      
      if (ResultUtils.isSuccess(result)) {
        this.logger.log('事务执行成功');
      } else {
        this.logger.warn('事务执行失败', { code: result.code, message: result.message });
      }
      
      return result;
    } catch (error: any) {
      this.logger.error('事务执行异常', error);
      return ResultUtils.error(error.message || '事务执行异常', 'TRANSACTION_EXCEPTION');
    }
  }

  // ========== 错误处理工具 ==========

  /**
   * 检查Repository结果是否失败（推荐使用）
   *
   * 符合Result模式的错误检查方式，不返回null
   *
   * @param result Repository返回的Result
   * @returns 失败时返回true，成功时返回false
   */
  protected isFailure(result: XResult<any>): result is FailureResult {
    return ResultUtils.isFailure(result);
  }

  /**
   * 检查Repository结果是否成功
   *
   * @param result Repository返回的Result
   * @returns 成功时返回true，失败时返回false
   */
  protected isSuccess<T>(result: XResult<T>): result is SuccessResult<T> {
    return ResultUtils.isSuccess(result);
  }

  /**
   * 传递Repository错误（兼容性方法，不推荐）
   *
   * ⚠️ 注意：此方法返回null，打破了Result模式的核心理念
   * 推荐直接使用 isFailure() 方法进行检查
   *
   * @deprecated 使用 isFailure() 替代
   * @param result Repository返回的Result
   * @returns 失败时返回错误Result，成功时返回null
   */
  protected propagateError<T>(result: XResult<any>): XResult<T> | null {
    return ResultUtils.isFailure(result) ? (result as XResult<T>) : null;
  }

  /**
   * 检查多个Repository结果
   * 
   * @param results Repository结果数组
   * @returns 第一个失败的Result，或null（全部成功）
   */
  protected checkResults<T>(results: XResult<any>[]): XResult<T> | null {
    for (const result of results) {
      if (ResultUtils.isFailure(result)) {
        return result as XResult<T>;
      }
    }
    return null;
  }

  /**
   * 验证业务条件
   * 
   * @param condition 验证条件
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   * @returns 验证失败时返回错误Result，成功时返回null
   */
  protected validateCondition<T>(
    condition: boolean,
    errorMessage: string,
    errorCode = 'VALIDATION_ERROR'
  ): XResult<T> | null {
    return condition ? null : ResultUtils.error(errorMessage, errorCode);
  }

  /**
   * 验证数据存在性
   * 
   * @param data 要验证的数据
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   * @returns 数据不存在时返回错误Result，存在时返回null
   */
  protected validateExists<T, U>(
    data: T | null | undefined,
    errorMessage: string,
    errorCode = 'NOT_FOUND'
  ): XResult<U> | null {
    return data ? null : ResultUtils.error(errorMessage, errorCode);
  }

  // ========== 日志工具 ==========

  /**
   * 记录业务操作日志
   */
  protected logOperation(operation: string, data?: any) {
    if (data) {
      this.logger.log(`业务操作: ${operation}`, JSON.stringify(data));
    } else {
      this.logger.log(`业务操作: ${operation}`);
    }
  }

  /**
   * 记录业务错误日志
   */
  protected logError(operation: string, error: any) {
    this.logger.error(`业务操作失败: ${operation}`, error?.message || error);
  }

  /**
   * 记录业务警告日志
   */
  protected logWarning(operation: string, message: string, data?: any) {
    if (data) {
      this.logger.warn(`业务警告: ${operation} - ${message}`, JSON.stringify(data));
    } else {
      this.logger.warn(`业务警告: ${operation} - ${message}`);
    }
  }

  // ========== 结果处理工具 ==========

  /**
   * 创建成功结果
   */
  protected success<T>(data: T, message?: string): XResult<T> {
    return ResultUtils.ok(data);
  }

  /**
   * 创建错误结果
   */
  protected error<T>(message: string, code = 'BUSINESS_ERROR'): XResult<T> {
    return ResultUtils.error(message, code);
  }

  /**
   * 创建空成功结果
   */
  protected empty(): XResult<void> {
    return ResultUtils.empty();
  }
}
