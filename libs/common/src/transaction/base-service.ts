import { Logger } from '@nestjs/common';
import { Result, ResultUtils } from '../types/result.type';
import { TransactionManager, TransactionOptions, TransactionOperation } from './transaction-manager';

/**
 * 简洁实用的Service基类
 * 
 * 特性：
 * - 统一的事务管理
 * - Result模式集成
 * - 错误传递工具
 * - 日志记录
 * 
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepo: CharacterRepository,
 *     private readonly guildRepo: GuildRepository
 *   ) {
 *     super('CharacterService');
 *   }
 * 
 *   async transferCharacter(
 *     characterId: string, 
 *     guildId: string
 *   ): Promise<Result<CharacterDocument>> {
 *     return this.executeTransaction(async (session) => {
 *       // 检查角色存在
 *       const characterResult = await this.characterRepo.findById(characterId, session);
 *       const error = this.propagateError<CharacterDocument>(characterResult);
 *       if (error) return error;
 * 
 *       // 执行转会
 *       return await this.characterRepo.transferToGuild(characterId, guildId, session);
 *     });
 *   }
 * }
 * ```
 */
export abstract class BaseService {
  protected readonly logger: Logger;

  constructor(loggerContext: string) {
    this.logger = new Logger(loggerContext);
  }

  // ========== 事务管理 ==========

  /**
   * 执行事务操作
   * 
   * @param operation 事务操作函数
   * @param options 事务配置选项
   * @returns Promise<Result<T>> 执行结果
   */
  protected async executeTransaction<T>(
    operation: TransactionOperation<T>,
    options?: TransactionOptions
  ): Promise<Result<T>> {
    try {
      this.logger.log('开始执行事务');
      const result = await TransactionManager.execute(operation, options);
      
      if (ResultUtils.isSuccess(result)) {
        this.logger.log('事务执行成功');
      } else {
        this.logger.warn('事务执行失败', { code: result.code, message: result.message });
      }
      
      return result;
    } catch (error: any) {
      this.logger.error('事务执行异常', error);
      return ResultUtils.error(error.message || '事务执行异常', 'TRANSACTION_EXCEPTION');
    }
  }

  // ========== 错误处理工具 ==========

  /**
   * 传递Repository错误（最常用）
   * 
   * @param result Repository返回的Result
   * @returns 失败时返回转换后的Result，成功时返回null
   */
  protected propagateError<T, U>(result: Result<T>): Result<U> | null {
    return ResultUtils.isFailure(result) ? (result as Result<U>) : null;
  }

  /**
   * 检查多个Repository结果
   * 
   * @param results Repository结果数组
   * @returns 第一个失败的Result，或null（全部成功）
   */
  protected checkResults<T>(results: Result<any>[]): Result<T> | null {
    for (const result of results) {
      if (ResultUtils.isFailure(result)) {
        return result as Result<T>;
      }
    }
    return null;
  }

  /**
   * 验证业务条件
   * 
   * @param condition 验证条件
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   * @returns 验证失败时返回错误Result，成功时返回null
   */
  protected validateCondition<T>(
    condition: boolean,
    errorMessage: string,
    errorCode = 'VALIDATION_ERROR'
  ): Result<T> | null {
    return condition ? null : ResultUtils.error(errorMessage, errorCode);
  }

  /**
   * 验证数据存在性
   * 
   * @param data 要验证的数据
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   * @returns 数据不存在时返回错误Result，存在时返回null
   */
  protected validateExists<T, U>(
    data: T | null | undefined,
    errorMessage: string,
    errorCode = 'NOT_FOUND'
  ): Result<U> | null {
    return data ? null : ResultUtils.error(errorMessage, errorCode);
  }

  // ========== 日志工具 ==========

  /**
   * 记录业务操作日志
   */
  protected logOperation(operation: string, data?: any) {
    this.logger.log(`业务操作: ${operation}`, data);
  }

  /**
   * 记录业务错误日志
   */
  protected logError(operation: string, error: any) {
    this.logger.error(`业务操作失败: ${operation}`, error);
  }

  /**
   * 记录业务警告日志
   */
  protected logWarning(operation: string, message: string, data?: any) {
    this.logger.warn(`业务警告: ${operation} - ${message}`, data);
  }

  // ========== 结果处理工具 ==========

  /**
   * 创建成功结果
   */
  protected success<T>(data: T, message?: string): Result<T> {
    return ResultUtils.ok(data);
  }

  /**
   * 创建错误结果
   */
  protected error<T>(message: string, code = 'BUSINESS_ERROR'): Result<T> {
    return ResultUtils.error(message, code);
  }

  /**
   * 创建空成功结果
   */
  protected empty(): Result<void> {
    return ResultUtils.empty();
  }
}
