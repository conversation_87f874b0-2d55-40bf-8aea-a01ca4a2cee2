import { ClientSession } from 'mongoose';
import { XResult, XResultUtils } from '../types/result.type';
import { TransactionConfigManager, TransactionExecutionOptions } from './transaction-config';

/**
 * 事务配置选项
 */
export interface TransactionOptions {
  /** 最大重试次数，默认3次 */
  maxRetries?: number;
  /** 重试延迟基数（毫秒），默认1000ms */
  retryDelay?: number;
  /** 事务超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 读偏好 */
  readPreference?: 'primary' | 'secondary';
  /** 读关注级别 */
  readConcern?: 'local' | 'majority' | 'snapshot';
  /** 写关注级别 */
  writeConcern?: 'majority' | number;
}

/**
 * 事务操作函数类型
 */
export type TransactionOperation<T> = (session: ClientSession) => Promise<XResult<T>>;

/**
 * 可重试错误代码
 */
const RETRYABLE_ERROR_CODES = [
  11000, // 重复键错误
  112,   // 写冲突
  244,   // 事务冲突
  251    // 事务已中止
];

/**
 * 简洁实用的事务管理器
 * 
 * 核心特性：
 * - 自动资源管理
 * - 智能重试机制
 * - Result模式集成
 * - 类型安全
 * 
 * 使用示例：
 * ```typescript
 * const result = await TransactionManager.execute(async (session) => {
 *   const playerResult = await playerRepo.update(id, data, session);
 *   if (ResultUtils.isFailure(playerResult)) return playerResult;
 *   
 *   const teamResult = await teamRepo.addPlayer(teamId, id, session);
 *   return teamResult;
 * });
 * ```
 */
export class TransactionManager {
  private static mongoose: any;
  private static transactionSupportCache = new Map<string, boolean>();

  /**
   * 初始化事务管理器
   */
  static initialize(mongooseInstance: any) {
    this.mongoose = mongooseInstance;
    // 清空缓存，因为可能是新的连接
    this.transactionSupportCache.clear();
  }

  /**
   * 执行事务操作
   * 
   * @param operation 事务操作函数
   * @param options 事务配置选项
   * @returns Promise<Result<T>> 执行结果
   */
  static async execute<T>(
    operation: TransactionOperation<T>,
    options: TransactionOptions = {}
  ): Promise<XResult<T>> {
    const config = this.mergeDefaultOptions(options);
    
    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      const result = await this.executeOnce(operation, config);
      
      // 成功或不可重试的错误，直接返回
      if (XResultUtils.isSuccess(result) || !this.isRetryableError(result)) {
        return result;
      }
      
      // 最后一次尝试，返回结果
      if (attempt === config.maxRetries) {
        return result;
      }
      
      // 等待后重试
      await this.delay(config.retryDelay * attempt);
    }
    
    return XResultUtils.error('事务执行失败', 'TRANSACTION_FAILED');
  }

  /**
   * 执行单次事务
   */
  private static async executeOnce<T>(
    operation: TransactionOperation<T>,
    options: TransactionOptions
  ): Promise<XResult<T>> {
    if (!this.mongoose) {
      return XResultUtils.error('Mongoose未初始化', 'MONGOOSE_NOT_INITIALIZED');
    }

    // 检查是否支持事务（使用缓存）
    const supportsTransactions = await this.checkTransactionSupport();

    if (!supportsTransactions) {
      console.warn('⚠️ MongoDB不支持事务，将在非事务模式下执行操作');
      // 在非事务模式下直接执行操作
      try {
        const result = await operation(null as any); // 传递null作为session
        return result;
      } catch (error: any) {
        return XResultUtils.error(
          error.message || '操作执行异常',
          this.getErrorCode(error)
        );
      }
    }

    const session = await this.mongoose.startSession();

    try {
      // TODO 后续使用默认配置
      // 启动事务（使用正确的事务配置）
      // const transactionConfig = TransactionConfigManager.getDefaultConfig();
      // session.startTransaction({
      //   readPreference: transactionConfig.readPreference,
      //   readConcern: transactionConfig.readConcern,
      //   writeConcern: transactionConfig.writeConcern,
      //   maxCommitTimeMS: transactionConfig.maxCommitTimeMS
      // });
      // 启动事务
      session.startTransaction({
        readPreference: options.readPreference,
        readConcern: { level: options.readConcern },
        writeConcern: { w: options.writeConcern }
      });

      // 设置超时
      const timeoutPromise = new Promise<XResult<T>>((_, reject) => {
        setTimeout(() => reject(new Error('事务超时')), options.timeout);
      });

      // 执行操作
      const operationPromise = operation(session);
      const result = await Promise.race([operationPromise, timeoutPromise]);

      // 检查结果
      if (XResultUtils.isFailure(result)) {
        await session.abortTransaction();
        return result;
      }

      // 提交事务
      await session.commitTransaction();
      return result;

    } catch (error: any) {
      await session.abortTransaction();
      return XResultUtils.error(
        error.message || '事务执行异常',
        this.getErrorCode(error)
      );
    } finally {
      session.endSession();
    }
  }

  /**
   * 检查MongoDB是否支持事务（带缓存，支持多数据库）
   */
  private static async checkTransactionSupport(): Promise<boolean> {
    const dbName = this.mongoose.connection.db.databaseName;

    // 如果已经检测过这个数据库，直接返回缓存结果
    if (this.transactionSupportCache.has(dbName)) {
      return this.transactionSupportCache.get(dbName)!;
    }

    try {
      console.log(`🔍 检测数据库 ${dbName} 的事务支持...`);
      const admin = this.mongoose.connection.db.admin();
      const result = await admin.command({ isMaster: 1 });

      // 检查是否为副本集或分片集群
      const supported = !!(result.setName || result.msg === 'isdbgrid');

      // 缓存结果
      this.transactionSupportCache.set(dbName, supported);

      if (supported) {
        console.log(`✅ 数据库 ${dbName} 支持事务（副本集或分片集群）`);
      } else {
        console.log(`⚠️ 数据库 ${dbName} 不支持事务（单节点模式）`);
      }

      return supported;
    } catch (error) {
      console.warn(`❌ 检查数据库 ${dbName} 事务支持时出错:`, error);
      this.transactionSupportCache.set(dbName, false);
      return false;
    }
  }

  /**
   * 合并默认配置
   */
  private static mergeDefaultOptions(options: TransactionOptions): Required<TransactionOptions> {
    return {
      maxRetries: options.maxRetries ?? 3,
      retryDelay: options.retryDelay ?? 1000,
      timeout: options.timeout ?? 30000,
      readPreference: options.readPreference ?? 'primary',
      readConcern: options.readConcern ?? 'local',
      writeConcern: options.writeConcern ?? 'majority'
    };
  }

  /**
   * 检查是否为可重试错误
   */
  private static isRetryableError(result: XResult<any>): boolean {
    if (XResultUtils.isSuccess(result)) return false;
    
    const retryableCodes = [
      'TRANSACTION_CONFLICT',
      'WRITE_CONFLICT',
      'DUPLICATE_KEY',
      'NETWORK_ERROR'
    ];
    
    return retryableCodes.includes(result.code);
  }

  /**
   * 获取错误代码
   */
  private static getErrorCode(error: any): string {
    if (RETRYABLE_ERROR_CODES.includes(error.code)) {
      switch (error.code) {
        case 11000: return 'DUPLICATE_KEY';
        case 112: return 'WRITE_CONFLICT';
        case 244: return 'TRANSACTION_CONFLICT';
        case 251: return 'TRANSACTION_ABORTED';
        default: return 'RETRYABLE_ERROR';
      }
    }
    
    return 'TRANSACTION_ERROR';
  }

  /**
   * 延迟函数
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 事务工具函数
 */
export class TransactionUtils {
  /**
   * 检查Repository结果并传递错误
   * 这是Service层最常用的工具函数
   */
  static propagateError<T, U>(result: XResult<T>): XResult<U> | null {
    return XResultUtils.isFailure(result) ? (result as XResult<U>) : null;
  }

  /**
   * 批量检查多个Repository结果
   * 用于需要多个前置检查的场景
   */
  static checkResults<T>(results: XResult<any>[]): XResult<T> | null {
    for (const result of results) {
      if (XResultUtils.isFailure(result)) {
        return result as XResult<T>;
      }
    }
    return null;
  }

  /**
   * 创建事务操作包装器
   * 用于Repository层方法的事务支持
   */
  static wrapOperation<T>(
    operation: (session?: ClientSession) => Promise<T>
  ): (session?: ClientSession) => Promise<XResult<T>> {
    return async (session?: ClientSession) => {
      try {
        const result = await operation(session);
        return XResultUtils.ok(result);
      } catch (error: any) {
        return XResultUtils.error(error.message || '操作失败', 'OPERATION_ERROR');
      }
    };
  }
}
