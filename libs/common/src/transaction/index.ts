/**
 * Mongoose事务组件库
 * 提供优雅、类型安全、易用的事务处理能力
 * 
 * 特性：
 * - 装饰器模式简化事务声明
 * - 完整的Result模式集成
 * - 自动重试和错误恢复
 * - 性能监控和指标收集
 * - 分布式事务支持
 * - 完善的类型安全
 */

export * from './types';
export * from './config';
export * from './manager';
export * from './decorators';
export * from './monitoring';
export * from './utils';

// 便捷导出
export { TransactionManager } from './manager';
export { Transactional, TransactionalClass } from './decorators';
export { TransactionConfig } from './config';
export { TransactionMonitor } from './monitoring';
