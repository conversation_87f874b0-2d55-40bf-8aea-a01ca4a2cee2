import { Injectable, Logger } from '@nestjs/common';
import { Model, Document, ClientSession } from 'mongoose';
import { XResult, XResultUtils } from '../types/result.type';
import { TransactionUtils } from './transaction-manager';

/**
 * 简洁实用的Repository基类
 * 
 * 特性：
 * - 统一的事务支持
 * - Result模式集成
 * - 常用CRUD操作
 * - 类型安全
 * 
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterRepository extends BaseRepository<CharacterDocument> {
 *   constructor(@InjectModel(Character.name) model: Model<CharacterDocument>) {
 *     super(model, 'CharacterRepository');
 *   }
 * 
 *   // 自定义业务方法
 *   async transferToGuild(
 *     characterId: string, 
 *     guildId: string, 
 *     session?: ClientSession
 *   ): Promise<Result<CharacterDocument>> {
 *     return this.wrapOperation(async (session) => {
 *       return await this.model.findByIdAndUpdate(
 *         characterId,
 *         { $set: { guildId, transferDate: new Date() } },
 *         { session, new: true }
 *       ).exec();
 *     })(session);
 *   }
 * }
 * ```
 */
export abstract class BaseRepository<T extends Document> {
  protected readonly logger: Logger;

  constructor(
    protected readonly model: Model<T>,
    loggerContext: string
  ) {
    this.logger = new Logger(loggerContext);
  }

  /**
   * 获取模型实例（用于高级操作）
   */
  get mongooseModel(): Model<T> {
    return this.model;
  }

  // ========== 基础CRUD操作 ==========

  /**
   * 根据ID查找文档
   */
  async findById(id: string, session?: ClientSession): Promise<XResult<T | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.findById(id, null, options).exec();
    })(session);
  }

  /**
   * 查找多个文档
   */
  async find(filter: any = {}, session?: ClientSession): Promise<XResult<T[]>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.find(filter, null, options).exec();
    })(session);
  }

  /**
   * 查找单个文档
   */
  async findOne(filter: any, session?: ClientSession): Promise<XResult<T | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.findOne(filter, null, options).exec();
    })(session);
  }

  /**
   * 创建文档
   */
  async create(data: Partial<T>, session?: ClientSession): Promise<XResult<T>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const docs = await this.model.create([data], options);
      return docs[0];
    })(session);
  }

  /**
   * 更新文档
   */
  async updateById(
    id: string, 
    update: Partial<T>, 
    session?: ClientSession
  ): Promise<XResult<T | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findByIdAndUpdate(id, { $set: update }, options).exec();
    })(session);
  }

  /**
   * 删除文档
   */
  async deleteById(id: string, session?: ClientSession): Promise<XResult<boolean>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const result = await this.model.deleteOne({ _id: id }, options).exec();
      return result.deletedCount > 0;
    })(session);
  }

  /**
   * 检查文档是否存在
   */
  async exists(filter: any, session?: ClientSession): Promise<XResult<boolean>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const count = await this.model.countDocuments(filter, options).exec();
      return count > 0;
    })(session);
  }

  /**
   * 计数文档
   */
  async count(filter: any = {}, session?: ClientSession): Promise<XResult<number>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.countDocuments(filter, options).exec();
    })(session);
  }

  // ========== 批量操作 ==========

  /**
   * 批量创建
   */
  async createMany(dataList: Partial<T>[], session?: ClientSession): Promise<XResult<T[]>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.create(dataList, options);
    })(session);
  }

  /**
   * 批量更新
   */
  async bulkWrite(operations: any[], session?: ClientSession): Promise<XResult<any>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.bulkWrite(operations, options);
    })(session);
  }

  // ========== 工具方法 ==========

  /**
   * 包装数据库操作，自动处理异常转Result
   */
  protected wrapOperation<R>(
    operation: (session?: ClientSession) => Promise<R>
  ): (session?: ClientSession) => Promise<XResult<R>> {
    return TransactionUtils.wrapOperation(operation);
  }

  /**
   * 记录操作日志
   */
  protected logOperation(operation: string, data?: any) {
    this.logger.log(`${operation}`, data);
  }

  /**
   * 记录错误日志
   */
  protected logError(operation: string, error: any) {
    this.logger.error(`${operation} failed`, error);
  }
}
