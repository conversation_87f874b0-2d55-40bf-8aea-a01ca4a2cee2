import { Injectable, Logger } from '@nestjs/common';
import { Model, Document, ClientSession, FilterQuery, QueryOptions, PopulateOptions } from 'mongoose';
import { XResult, XResultUtils } from '../types/result.type';
import { TransactionUtils } from './transaction-manager';

/**
 * 查询选项接口
 */
export interface QueryOptionsExtended<T = any> {
  lean?: boolean;
  select?: string | string[];
  populate?: any;
  sort?: any;
  skip?: number;
  limit?: number;
  filter?: FilterQuery<T>;
  session?: ClientSession;
}

/**
 * 分页查询选项
 */
export interface PaginationOptions<T = any> {
  page: number;
  limit: number;
  filter?: FilterQuery<T>;
  sort?: any;
  select?: string | string[];
  populate?: any;
  lean?: boolean;
}

/**
 * 高性能Repository基类
 *
 * 特性：
 * - 统一的事务支持
 * - Result模式集成
 * - Lean查询性能优化
 * - 智能查询选择
 * - 分页查询支持
 * - 类型安全
 *
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterRepository extends BaseRepository<CharacterDocument> {
 *   constructor(@InjectModel(Character.name) model: Model<CharacterDocument>) {
 *     super(model, 'CharacterRepository');
 *   }
 *
 *   // 获取角色详情（需要后续操作）
 *   async getCharacterForUpdate(id: string): Promise<XResult<CharacterDocument | null>> {
 *     return this.findById(id, { lean: false });
 *   }
 *
 *   // 获取角色显示信息（只读，性能优化）
 *   async getCharacterProfile(id: string): Promise<XResult<Character | null>> {
 *     return this.findById(id, {
 *       lean: true,
 *       select: 'name level experience serverId'
 *     });
 *   }
 *
 *   // 获取角色列表（分页，性能优化）
 *   async getCharacterList(options: PaginationOptions): Promise<XResult<PaginatedResult<Character>>> {
 *     return this.findWithPagination({
 *       ...options,
 *       lean: true,
 *       select: 'name level experience'
 *     });
 *   }
 * }
 * ```
 */
export abstract class BaseRepository<T extends Document> {
  protected readonly logger: Logger;

  constructor(
    protected readonly model: Model<T>,
    loggerContext: string
  ) {
    this.logger = new Logger(loggerContext);
  }

  /**
   * 获取模型实例（用于高级操作）
   */
  get mongooseModel(): Model<T> {
    return this.model;
  }

  // ========== 基础CRUD操作（支持Lean查询优化） ==========

  /**
   * 根据ID查找文档（方法重载）
   */
  async findById(id: string): Promise<XResult<T | any | null>>;
  async findById(id: string, session: ClientSession): Promise<XResult<T | any | null>>;
  async findById(id: string, options: QueryOptionsExtended<T>): Promise<XResult<T | any | null>>;
  async findById(id: string, options: QueryOptionsExtended<T>, session: ClientSession): Promise<XResult<T | any | null>>;

  async findById(
    id: string,
    optionsOrSession?: QueryOptionsExtended<T> | ClientSession,
    session?: ClientSession
  ): Promise<XResult<T | any | null>> {
    // 智能参数识别
    const { options, actualSession } = this.parseOptionsAndSession(optionsOrSession, session);

    return this.wrapOperation(async (session) => {
      let query = this.model.findById(id);
      query = this.applyQueryOptions(query, options, session);
      return await query.exec();
    })(actualSession);
  }

  /**
   * 查找多个文档（方法重载）
   */
  async find(filter: FilterQuery<T>): Promise<XResult<T[] | any[]>>;
  async find(filter: FilterQuery<T>, session: ClientSession): Promise<XResult<T[] | any[]>>;
  async find(filter: FilterQuery<T>, options: QueryOptionsExtended<T>): Promise<XResult<T[] | any[]>>;
  async find(filter: FilterQuery<T>, options: QueryOptionsExtended<T>, session: ClientSession): Promise<XResult<T[] | any[]>>;

  async find(
    filter: FilterQuery<T> = {},
    optionsOrSession?: QueryOptionsExtended<T> | ClientSession,
    session?: ClientSession
  ): Promise<XResult<T[] | any[]>> {
    // 智能参数识别
    const { options, actualSession } = this.parseOptionsAndSession(optionsOrSession, session);

    return this.wrapOperation(async (session) => {
      let query = this.model.find(filter);
      query = this.applyQueryOptions(query, options, session);
      return await query.exec();
    })(actualSession);
  }

  /**
   * 查找单个文档（方法重载）
   */
  async findOne(filter: FilterQuery<T>): Promise<XResult<T | any | null>>;
  async findOne(filter: FilterQuery<T>, session: ClientSession): Promise<XResult<T | any | null>>;
  async findOne(filter: FilterQuery<T>, options: QueryOptionsExtended<T>): Promise<XResult<T | any | null>>;
  async findOne(filter: FilterQuery<T>, options: QueryOptionsExtended<T>, session: ClientSession): Promise<XResult<T | any | null>>;

  async findOne(
    filter: FilterQuery<T>,
    optionsOrSession?: QueryOptionsExtended<T> | ClientSession,
    session?: ClientSession
  ): Promise<XResult<T | any | null>> {
    // 智能参数识别
    const { options, actualSession } = this.parseOptionsAndSession(optionsOrSession, session);

    return this.wrapOperation(async (session) => {
      let query = this.model.findOne(filter);
      query = this.applyQueryOptions(query, options, session);
      return await query.exec();
    })(actualSession);
  }

  /**
   * 创建文档
   */
  async create(data: Partial<T>, session?: ClientSession): Promise<XResult<T>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const docs = await this.model.create([data], options);
      return docs[0];
    })(session);
  }

  /**
   * 更新文档
   */
  async updateById(
    id: string, 
    update: Partial<T>, 
    session?: ClientSession
  ): Promise<XResult<T | null>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session, new: true } : { new: true };
      return await this.model.findByIdAndUpdate(id, { $set: update }, options).exec();
    })(session);
  }

  /**
   * 删除文档
   */
  async deleteById(id: string, session?: ClientSession): Promise<XResult<boolean>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const result = await this.model.deleteOne({ _id: id }, options).exec();
      return result.deletedCount > 0;
    })(session);
  }

  /**
   * 检查文档是否存在
   */
  async exists(filter: any, session?: ClientSession): Promise<XResult<boolean>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      const count = await this.model.countDocuments(filter, options).exec();
      return count > 0;
    })(session);
  }

  /**
   * 计数文档
   */
  async count(filter: FilterQuery<T> = {}, session?: ClientSession): Promise<XResult<number>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.countDocuments(filter, options).exec();
    })(session);
  }

  // ========== 高性能查询方法 ==========

  /**
   * 分页查询（性能优化）
   * @param options 分页选项
   * @param session 事务会话
   */
  async findWithPagination(
    options: PaginationOptions<T>,
    session?: ClientSession
  ): Promise<XResult<BasePaginatedResult<T | any>>> {
    return this.wrapOperation(async (session) => {
      const { page, limit, filter = {}, sort, select, populate, lean = true } = options;
      const skip = (page - 1) * limit;

      // 构建查询选项
      const queryOptions: QueryOptionsExtended<T> = {
        sort,
        skip,
        limit,
        select,
        populate,
        lean
      };

      // 并行执行数据查询和总数统计
      const [data, total] = await Promise.all([
        this.find(filter, queryOptions, session).then(result =>
          XResultUtils.isSuccess(result) ? result.data : []
        ),
        this.count(filter, session).then(result =>
          XResultUtils.isSuccess(result) ? result.data : 0
        )
      ]);

      const pages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      };
    })(session);
  }

  /**
   * Lean查询（性能优化）
   * @param filter 查询条件
   * @param options 查询选项
   * @param session 事务会话
   */
  async findLean(
    filter: FilterQuery<T> = {},
    options: Omit<QueryOptionsExtended<T>, 'lean'> = {},
    session?: ClientSession
  ): Promise<XResult<any[]>> {
    return this.find(filter, { ...options, lean: true }, session);
  }

  /**
   * 根据ID进行Lean查询（性能优化）
   * @param id 文档ID
   * @param options 查询选项
   * @param session 事务会话
   */
  async findByIdLean(
    id: string,
    options: Omit<QueryOptionsExtended<T>, 'lean'> = {},
    session?: ClientSession
  ): Promise<XResult<any | null>> {
    return this.findById(id, { ...options, lean: true }, session);
  }

  /**
   * 搜索查询（性能优化）
   * @param searchFields 搜索字段
   * @param searchTerm 搜索词
   * @param options 查询选项
   * @param session 事务会话
   */
  async search(
    searchFields: string[],
    searchTerm: string,
    options: QueryOptionsExtended<T> = {},
    session?: ClientSession
  ): Promise<XResult<T[] | any[]>> {
    return this.wrapOperation(async (session) => {
      const searchFilter: any = {
        $or: searchFields.map(field => ({
          [field]: { $regex: searchTerm, $options: 'i' }
        }))
      };

      // 合并搜索条件和其他过滤条件
      const combinedFilter: any = options.filter
        ? { $and: [searchFilter, options.filter] }
        : searchFilter;

      const findResult = await this.find(combinedFilter, { ...options, lean: true }, session);
      if (XResultUtils.isSuccess(findResult)) {
        return findResult.data;
      } else {
        throw new Error(findResult.message);
      }
    })(session);
  }

  // ========== 批量操作 ==========

  /**
   * 批量创建
   */
  async createMany(dataList: Partial<T>[], session?: ClientSession): Promise<XResult<T[]>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.create(dataList, options);
    })(session);
  }

  /**
   * 批量更新
   */
  async bulkWrite(operations: any[], session?: ClientSession): Promise<XResult<any>> {
    return this.wrapOperation(async (session) => {
      const options = session ? { session } : {};
      return await this.model.bulkWrite(operations, options);
    })(session);
  }

  // ========== 工具方法 ==========

  /**
   * 应用查询选项到查询对象
   * @param query Mongoose查询对象
   * @param options 查询选项
   * @param session 事务会话
   */
  protected applyQueryOptions(
    query: any,
    options: QueryOptionsExtended<T>,
    session?: ClientSession
  ): any {
    // 应用session
    if (session) {
      query = query.session(session);
    }

    // 应用字段选择
    if (options.select) {
      query = query.select(options.select);
    }

    // 应用排序
    if (options.sort) {
      query = query.sort(options.sort);
    }

    // 应用跳过
    if (options.skip !== undefined) {
      query = query.skip(options.skip);
    }

    // 应用限制
    if (options.limit !== undefined) {
      query = query.limit(options.limit);
    }

    // 应用关联查询
    if (options.populate) {
      if (Array.isArray(options.populate)) {
        options.populate.forEach(pop => query = query.populate(pop));
      } else {
        query = query.populate(options.populate);
      }
    }

    // 应用lean查询（默认为true以优化性能）
    if (options.lean !== false) {
      query = query.lean();
    }

    return query;
  }

  /**
   * 包装数据库操作，自动处理异常转Result
   */
  protected wrapOperation<R>(
    operation: (session?: ClientSession) => Promise<R>
  ): (session?: ClientSession) => Promise<XResult<R>> {
    return TransactionUtils.wrapOperation(operation);
  }

  /**
   * 记录操作日志
   */
  protected logOperation(operation: string, data?: any) {
    this.logger.log(`${operation}`, data);
  }

  /**
   * 记录错误日志
   */
  protected logError(operation: string, error: any) {
    this.logger.error(`${operation} failed`, error);
  }

  /**
   * 性能监控装饰器
   * @param queryName 查询名称
   * @param queryFn 查询函数
   */
  protected async measureQuery<R>(
    queryName: string,
    queryFn: () => Promise<R>
  ): Promise<R> {
    const start = Date.now();
    try {
      const result = await queryFn();
      const duration = Date.now() - start;

      this.logOperation(`Query ${queryName}`, { duration: `${duration}ms` });

      if (duration > 1000) {
        this.logger.warn(`Slow query detected: ${queryName} took ${duration}ms`);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.logError(`Query ${queryName}`, { error, duration: `${duration}ms` });
      throw error;
    }
  }

  /**
   * 智能参数识别：区分options和session
   * @param optionsOrSession 可能是options或session
   * @param session 明确的session参数
   */
  private parseOptionsAndSession(
    optionsOrSession?: QueryOptionsExtended<T> | ClientSession,
    session?: ClientSession
  ): { options: QueryOptionsExtended<T>; actualSession?: ClientSession } {
    let options: QueryOptionsExtended<T> = {};
    let actualSession: ClientSession | undefined;

    if (optionsOrSession) {
      if (this.isClientSession(optionsOrSession)) {
        // 第二个参数是session
        actualSession = optionsOrSession;
      } else {
        // 第二个参数是options
        options = optionsOrSession;
        actualSession = session;
      }
    } else {
      actualSession = session;
    }

    return { options, actualSession };
  }

  /**
   * 判断对象是否为ClientSession
   * @param obj 待判断的对象
   */
  private isClientSession(obj: any): obj is ClientSession {
    return obj &&
           typeof obj === 'object' &&
           typeof obj.startTransaction === 'function' &&
           typeof obj.commitTransaction === 'function' &&
           typeof obj.abortTransaction === 'function';
  }
}
