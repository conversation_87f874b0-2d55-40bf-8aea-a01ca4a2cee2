/**
 * 统一结果类型定义
 * 用于替代异常抛出模式，提供类型安全的错误处理
 *
 * ========================================
 * 🚀 Result模式手动转换完整指南
 * ========================================
 *
 * ## 核心理念
 * - 用Result<T>替代异常处理，让错误成为类型系统的一部分
 * - Repository层：负责数据访问，所有异常转换为Result
 * - Service层：负责业务逻辑，检查Result并传递错误
 * - Controller层：负责接口适配，将Result转换为HTTP响应
 *
 * ## 转换步骤
 *
 * ### 1. Repository层转换
 * ```typescript
 * // 步骤1：修改返回类型
 * async findById(id: string): Promise<Result<CharacterDocument | null>>
 *
 * // 步骤2：使用RepositoryResultWrapper包装
 * async findById(id: string): Promise<Result<CharacterDocument | null>> {
 *   return await RepositoryResultWrapper.wrapNullable(async () => {
 *     return await this.characterModel.findById(id).exec();
 *   });
 * }
 * ```
 *
 * ### 2. Service层转换
 * ```typescript
 * // 步骤1：修改返回类型
 * async updateCharacter(id: string, dto: UpdateDto): Promise<Result<CharacterDocument>>
 *
 * // 步骤2：检查Repository结果
 * async updateCharacter(id: string, dto: UpdateDto): Promise<Result<CharacterDocument>> {
 *   const characterResult = await this.characterRepository.findById(id);
 *   const errorResult = ServiceResultHandler.propagateError<CharacterDocument>(characterResult);
 *   if (errorResult) return errorResult;
 *
 *   const character = characterResult.data;
 *   // 业务逻辑...
 *
 *   const saveResult = await this.characterRepository.update(character);
 *   return saveResult; // 直接返回Repository的Result
 * }
 *
 * // 步骤3：替换throw语句
 * // 原始：throw new BadRequestException('角色名已存在');
 * // 转换：return ResultUtils.error('角色名已存在', 'CHARACTER_NAME_TAKEN');
 * ```
 *
 * ### 3. Controller层转换
 * ```typescript
 * // 使用MicroserviceResponseUtils.fromResult()转换
 * @MessagePattern('character.update')
 * async updateCharacter(@Payload() payload: UpdatePayload): Promise<MicroserviceResponse<CharacterDocument>> {
 *   const result = await this.characterService.updateCharacter(payload.id, payload.dto);
 *   return MicroserviceResponseUtils.fromResult(result);
 * }
 * ```
 *
 * ## 常用工具类速查
 *
 * ### ResultUtils - 基础工具
 * - `ResultUtils.ok(data)` - 创建成功结果
 * - `ResultUtils.error(message, code?)` - 创建失败结果
 * - `ResultUtils.empty()` - 创建空成功结果
 * - `ResultUtils.isSuccess(result)` - 检查是否成功
 * - `ResultUtils.isFailure(result)` - 检查是否失败
 *
 * ### RepositoryResultWrapper - Repository层专用
 * - `RepositoryResultWrapper.wrap()` - 通用包装
 * - `RepositoryResultWrapper.wrapNullable()` - 可空查询包装
 * - `RepositoryResultWrapper.wrapArray()` - 数组查询包装
 * - `RepositoryResultWrapper.wrapBoolean()` - 布尔操作包装
 * - `RepositoryResultWrapper.wrapCount()` - 计数操作包装
 *
 * ### ServiceResultHandler - Service层专用
 * - `ServiceResultHandler.propagateError()` - 传递错误（最常用）
 * - `ServiceResultHandler.checkOrFail()` - 检查并转换错误
 * - `ServiceResultHandler.chainChecks()` - 链式检查
 *
 * ## 转换优先级
 * 1. 先转换Repository层（数据访问层）
 * 2. 再转换Service层（业务逻辑层）
 * 3. 最后转换Controller层（接口层）
 *
 * ## 注意事项
 * - 避免使用unwrap()等会抛出异常的方法，除非在测试代码中
 * - Service层要检查每个Repository调用的结果
 * - 保持错误信息的传递，不要丢失原始错误
 * - 使用有意义的错误代码，便于前端处理
 */

/**
 * 成功结果类型
 */
export interface SuccessResult<T> {
  success: true;
  data: T;
  code?: string;
  message?: string;
}

/**
 * 失败结果类型
 */
export interface FailureResult {
  success: false;
  code: string;
  message: string;
  data: null;
  details?: any; // 额外的错误详情
}

/**
 * 统一结果类型
 */
export type XResult<T> = SuccessResult<T> | FailureResult;

/**
 * 分页结果类型
 */
export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 分页成功结果
 */
export type PaginatedResult<T> = XResult<PaginationResult<T>>;

/**
 * 结果工具类 - 优化版本，提供更简洁的API
 */
export class XResultUtils {
  /**
   * 创建成功结果
   */
  static success<T>(data: T, message?: string, code?: string): SuccessResult<T> {
    return {
      success: true,
      data,
      message,
      code: code || 'SUCCESS'
    };
  }

  /**
   * 创建失败结果
   */
  static failure(code: string, message: string, details?: any): FailureResult {
    return {
      success: false,
      code,
      message,
      data: null,
      details
    };
  }

  // ========== 新增：简化的创建方法 ==========

  /**
   * 快速创建成功结果（最常用）
   *
   * 使用场景：
   * - Repository层：包装数据库查询结果
   * - Service层：返回业务处理成功的数据
   * - 替代直接return语句
   *
   * 使用方法：
   * ```typescript
   * // 原始代码
   * return character;
   *
   * // 转换后
   * return ResultUtils.ok(character);
   * ```
   *
   * @param data 成功时返回的数据
   * @returns 成功结果对象
   */
  static ok<T>(data: T): SuccessResult<T> {
    return this.success(data);
  }

  /**
   * 快速创建失败结果（最常用）
   *
   * 使用场景：
   * - 替代throw语句
   * - 业务逻辑验证失败
   * - 参数校验失败
   *
   * 使用方法：
   * ```typescript
   * // 原始代码
   * throw new BadRequestException('角色名已存在');
   *
   * // 转换后
   * return ResultUtils.error('角色名已存在', 'CHARACTER_NAME_TAKEN');
   * ```
   *
   * @param message 错误消息
   * @param code 错误代码，默认为'ERROR'
   * @returns 失败结果对象
   */
  static error(message: string, code = 'ERROR'): FailureResult {
    return this.failure(code, message);
  }

  /**
   * 创建空成功结果
   *
   * 使用场景：
   * - 删除操作成功
   * - 更新操作成功但不需要返回数据
   * - void方法的成功返回
   *
   * 使用方法：
   * ```typescript
   * // 原始代码
   * await this.characterRepository.delete(id);
   * return;
   *
   * // 转换后
   * await this.characterRepository.delete(id);
   * return ResultUtils.empty();
   * ```
   *
   * @returns 空成功结果对象
   */
  static empty(): SuccessResult<void> {
    return this.success(undefined as void);
  }

  /**
   * 创建分页成功结果
   */
  static paginatedSuccess<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
    message?: string
  ): SuccessResult<PaginationResult<T>> {
    const pages = Math.ceil(total / limit);
    return this.success({
      data,
      total,
      page,
      limit,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1
    }, message);
  }

  /**
   * 检查结果是否成功
   */
  static isSuccess<T>(result: XResult<T>): result is SuccessResult<T> {
    return result.success === true;
  }

  /**
   * 检查结果是否失败
   */
  static isFailure<T>(result: XResult<T>): result is FailureResult {
    return result.success === false;
  }

  /**
   * 从结果中提取数据，失败时返回默认值
   */
  static getDataOrDefault<T>(result: XResult<T>, defaultValue: T): T {
    return this.isSuccess(result) ? result.data : defaultValue;
  }

  /**
   * 从结果中提取数据，失败时返回null
   */
  static getDataOrNull<T>(result: XResult<T>): T | null {
    return this.isSuccess(result) ? result.data : null;
  }

  // ========== 新增：便捷的数据提取方法 ==========

  /**
   * 安全提取数据，失败时抛出错误（用于确信成功的场景）
   *
   * 使用场景：
   * - 测试代码中，确信操作必须成功
   * - 初始化代码中，失败时应该终止程序
   * - 调试时快速获取数据
   *
   * ⚠️ 注意：这会抛出异常，破坏Result模式，谨慎使用
   *
   * 使用方法：
   * ```typescript
   * // 测试代码中
   * const character = ResultUtils.unwrap(await characterService.create(dto));
   * expect(character.name).toBe('测试角色');
   *
   * // 初始化代码中
   * const config = ResultUtils.unwrap(await configService.load());
   * ```
   *
   * @param result Result对象
   * @returns 成功时的数据
   * @throws Error 失败时抛出错误
   */
  static unwrap<T>(result: XResult<T>): T {
    if (this.isFailure(result)) {
      throw new Error(`Result unwrap failed: ${result.message} (${result.code})`);
    }
    return result.data;
  }

  /**
   * 提取数据或执行回调（用于错误处理）
   *
   * 使用场景：
   * - 需要对错误进行特殊处理
   * - 提供默认值或备用逻辑
   * - 错误恢复机制
   *
   * 使用方法：
   * ```typescript
   * // 提供默认值
   * const character = ResultUtils.unwrapOr(result, () => defaultCharacter);
   *
   * // 错误处理
   * const data = ResultUtils.unwrapOr(result, (error) => {
   *   this.logger.error('操作失败', error);
   *   return fallbackData;
   * });
   * ```
   *
   * @param result Result对象
   * @param onError 错误处理回调函数
   * @returns 成功时的数据或错误处理回调的返回值
   */
  static unwrapOr<T>(result: XResult<T>, onError: (error: FailureResult) => T): T {
    return this.isSuccess(result) ? result.data : onError(result);
  }

  /**
   * 提取数据或抛出自定义错误
   *
   * 使用场景：
   * - 需要抛出特定的错误消息
   * - 与现有异常处理机制集成
   * - 快速失败模式
   *
   * ⚠️ 注意：这会抛出异常，破坏Result模式，谨慎使用
   *
   * 使用方法：
   * ```typescript
   * // 抛出自定义错误
   * const character = ResultUtils.unwrapOrThrow(result, '角色创建失败，请重试');
   *
   * // 使用原始错误消息
   * const data = ResultUtils.unwrapOrThrow(result);
   * ```
   *
   * @param result Result对象
   * @param errorMessage 自定义错误消息，不提供则使用原始消息
   * @returns 成功时的数据
   * @throws Error 失败时抛出错误
   */
  static unwrapOrThrow<T>(result: XResult<T>, errorMessage?: string): T {
    if (this.isFailure(result)) {
      throw new Error(errorMessage || result.message);
    }
    return result.data;
  }

  /**
   * 链式处理结果
   *
   * 使用场景：
   * - 多个操作依次执行，前一个失败则不再继续
   * - 简化链式调用的代码
   * - 保持代码的扁平化
   *
   * 使用方法：
   * ```typescript
   * // 原始代码
   * let result = await this.step1();
   * if (ResultUtils.isFailure(result)) return result;
   * result = await this.step2(result.data);
   * if (ResultUtils.isFailure(result)) return result;
   * result = await this.step3(result.data);
   * if (ResultUtils.isFailure(result)) return result;
   * return result;
   *
   * // 转换后
   * let result = await this.step1();
   * result = await ResultUtils.chain(result, this.step2);
   * result = await ResultUtils.chain(result, this.step3);
   * return result;
   * ```
   *
   * @param result 前一个操作的结果
   * @param fn 下一个操作的函数
   * @returns Promise<Result<U>> 下一个操作的结果
   */
  static async chain<T, U>(
    result: XResult<T>,
    fn: (data: T) => Promise<XResult<U>>
  ): Promise<XResult<U>> {
    if (this.isFailure(result)) {
      return result;
    }
    return await fn(result.data);
  }

  /**
   * 映射成功结果的数据
   *
   * 使用场景：
   * - 对成功结果的数据进行转换
   * - 提取数据的子集
   * - 保持代码的扁平化
   *
   * 使用方法：
   * ```typescript
   * // 原始代码
   * const result = await this.step1();
   * if (ResultUtils.isFailure(result)) return result;
   * const data = result.data;
   * const mappedData = this.mapData(data);
   * return ResultUtils.success(mappedData);
   *
   * // 转换后
   * const result = await this.step1();
   * return ResultUtils.map(result, this.mapData);
   * ```
   *
   * @param result 原始结果
   * @param fn 数据映射函数
   * @returns Result<U> 映射后的结果
   */
  static map<T, U>(
    result: XResult<T>,
    fn: (data: T) => U
  ): XResult<U> {
    if (this.isFailure(result)) {
      return result;
    }
    return this.success(fn(result.data), result.message, result.code);
  }
}

/**
 * 常用错误码定义
 */
export const CommonErrorCodes = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // 资源错误
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // 业务逻辑错误
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_RESOURCES: 'INSUFFICIENT_RESOURCES',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  
  // 外部服务错误
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const;

export type CommonErrorCode = typeof CommonErrorCodes[keyof typeof CommonErrorCodes];

// ========== 新增：Repository层专用工具 ==========

/**
 * Repository层Result包装器
 * 提供最简洁的数据库操作包装，自动处理异常转换
 *
 * 核心理念：Repository层只负责数据访问，所有异常都转换为Result
 */
export class RepositoryResultWrapper {
  /**
   * 包装数据库操作（通用版本）
   *
   * 使用场景：
   * - 创建、更新、删除操作
   * - 复杂查询操作
   * - 任何可能抛出异常的数据库操作
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * async create(dto: CreateCharacterDto): Promise<CharacterDocument> {
   *   try {
   *     const character = new this.characterModel(dto);
   *     return await character.save();
   *   } catch (error) {
   *     throw new Error('创建失败');
   *   }
   * }
   *
   * // 转换后
   * async create(dto: CreateCharacterDto): Promise<Result<CharacterDocument>> {
   *   return await RepositoryResultWrapper.wrap(async () => {
   *     const character = new this.characterModel(dto);
   *     return await character.save();
   *   });
   * }
   * ```
   *
   * @param operation 数据库操作函数
   * @returns Promise<Result<T>> 包装后的结果
   */
  static async wrap<T>(operation: () => Promise<T>): Promise<XResult<T>> {
    try {
      const data = await operation();
      return XResultUtils.ok(data);
    } catch (error: any) {
      return XResultUtils.error(error.message || '数据库操作失败', 'DATABASE_ERROR');
    }
  }

  /**
   * 包装可能返回null的查询操作
   *
   * 使用场景：
   * - findById、findOne等单条记录查询
   * - 可能不存在的数据查询
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * async findById(id: string): Promise<CharacterDocument | null> {
   *   return await this.characterModel.findById(id).exec();
   * }
   *
   * // 转换后
   * async findById(id: string): Promise<Result<CharacterDocument | null>> {
   *   return await RepositoryResultWrapper.wrapNullable(async () => {
   *     return await this.characterModel.findById(id).exec();
   *   });
   * }
   * ```
   *
   * @param operation 可能返回null的查询操作
   * @returns Promise<Result<T | null>> 包装后的结果
   */
  static async wrapNullable<T>(operation: () => Promise<T | null>): Promise<XResult<T | null>> {
    return this.wrap(operation);
  }

  /**
   * 包装数组查询操作
   *
   * 使用场景：
   * - find、findAll等多条记录查询
   * - 列表查询操作
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * async findByUserId(userId: string): Promise<CharacterDocument[]> {
   *   return await this.characterModel.find({ userId }).exec();
   * }
   *
   * // 转换后
   * async findByUserId(userId: string): Promise<Result<CharacterDocument[]>> {
   *   return await RepositoryResultWrapper.wrapArray(async () => {
   *     return await this.characterModel.find({ userId }).exec();
   *   });
   * }
   * ```
   *
   * @param operation 数组查询操作
   * @returns Promise<Result<T[]>> 包装后的结果
   */
  static async wrapArray<T>(operation: () => Promise<T[]>): Promise<XResult<T[]>> {
    return this.wrap(operation);
  }

  /**
   * 包装计数操作
   *
   * 使用场景：
   * - count、countDocuments等计数查询
   * - 统计操作
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * async getCount(): Promise<number> {
   *   return await this.characterModel.countDocuments().exec();
   * }
   *
   * // 转换后
   * async getCount(): Promise<Result<number>> {
   *   return await RepositoryResultWrapper.wrapCount(async () => {
   *     return await this.characterModel.countDocuments().exec();
   *   });
   * }
   * ```
   *
   * @param operation 计数操作
   * @returns Promise<Result<number>> 包装后的结果
   */
  static async wrapCount(operation: () => Promise<number>): Promise<XResult<number>> {
    return this.wrap(operation);
  }

  /**
   * 包装布尔操作
   *
   * 使用场景：
   * - exists、has等存在性检查
   * - 删除操作的成功标识
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * async existsByName(name: string): Promise<boolean> {
   *   const count = await this.characterModel.countDocuments({ name }).exec();
   *   return count > 0;
   * }
   *
   * // 转换后
   * async existsByName(name: string): Promise<Result<boolean>> {
   *   return await RepositoryResultWrapper.wrapBoolean(async () => {
   *     const count = await this.characterModel.countDocuments({ name }).exec();
   *     return count > 0;
   *   });
   * }
   * ```
   *
   * @param operation 布尔操作
   * @returns Promise<Result<boolean>> 包装后的结果
   */
  static async wrapBoolean(operation: () => Promise<boolean>): Promise<XResult<boolean>> {
    return this.wrap(operation);
  }

  // ========== 新增：事务支持的包装方法 ==========

  /**
   * 包装支持事务的数据库操作
   *
   * 使用场景：
   * - 需要在事务中执行的Repository方法
   * - 支持可选的ClientSession参数
   * - 保持事务的ACID特性
   *
   * 转换示例：
   * ```typescript
   * import { ClientSession } from 'mongoose';
   *
   * // 原始代码
   * async transferPlayer(
   *   playerId: string,
   *   newTeamId: string,
   *   session?: ClientSession
   * ): Promise<PlayerDocument> {
   *   const options = session ? { session } : {};
   *   return await this.model.findOneAndUpdate(
   *     { _id: playerId },
   *     { $set: { teamId: newTeamId } },
   *     { ...options, new: true }
   *   ).exec();
   * }
   *
   * // 转换后
   * async transferPlayer(
   *   playerId: string,
   *   newTeamId: string,
   *   session?: ClientSession
   * ): Promise<Result<PlayerDocument>> {
   *   return await RepositoryResultWrapper.wrapWithSession(async () => {
   *     const options = session ? { session } : {};
   *     return await this.model.findOneAndUpdate(
   *       { _id: playerId },
   *       { $set: { teamId: newTeamId } },
   *       { ...options, new: true }
   *     ).exec();
   *   });
   * }
   * ```
   *
   * @param operation 支持事务的数据库操作
   * @returns Promise<Result<T>> 包装后的结果
   */
  static async wrapWithSession<T>(operation: () => Promise<T>): Promise<XResult<T>> {
    return this.wrap(operation);
  }
}

// ========== 新增：Service层专用工具 ==========

/**
 * Service层Result处理器
 * 提供业务逻辑层的便捷方法，专门处理Repository返回的Result
 *
 * 核心理念：Service层负责业务逻辑，需要检查Repository结果并进行相应处理
 */
export class ServiceResultHandler {
  /**
   * 检查Repository结果并提取数据（简单版本）
   *
   * 使用场景：
   * - 简单的数据提取，失败时返回null
   * - 不需要复杂错误处理的场景
   *
   * ⚠️ 注意：这会丢失错误信息，建议使用propagateError
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * const character = await this.characterRepository.findById(id);
   * if (!character) {
   *   throw new NotFoundException('角色不存在');
   * }
   *
   * // 转换后（不推荐）
   * const characterResult = await this.characterRepository.findById(id);
   * const character = ServiceResultHandler.checkAndExtract(characterResult);
   * if (!character) {
   *   return ResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
   * }
   * ```
   *
   * @param result Repository返回的Result
   * @returns 成功时的数据，失败时返回null
   */
  static checkAndExtract<T>(result: XResult<T>): T | null {
    if (XResultUtils.isFailure(result)) {
      return null; // 或者可以抛出错误，根据业务需求
    }
    return result.data;
  }

  /**
   * 检查Repository结果，失败时返回指定的失败Result
   *
   * 使用场景：
   * - 需要将Repository错误转换为业务错误
   * - 提供更友好的错误消息
   *
   * 转换示例：
   * ```typescript
   * const characterResult = await this.characterRepository.findById(id);
   * const checkedResult = ServiceResultHandler.checkOrFail(
   *   characterResult,
   *   'CHARACTER_NOT_FOUND',
   *   '角色不存在或已被删除'
   * );
   * if (ResultUtils.isFailure(checkedResult)) {
   *   return checkedResult;
   * }
   * const character = checkedResult.data;
   * ```
   *
   * @param result Repository返回的Result
   * @param failureCode 失败时的错误代码
   * @param failureMessage 失败时的错误消息
   * @returns 成功时返回原Result，失败时返回新的失败Result
   */
  static checkOrFail<T>(result: XResult<T>, failureCode: string, failureMessage: string): XResult<T> {
    if (XResultUtils.isFailure(result)) {
      return XResultUtils.failure(failureCode, failureMessage);
    }
    return result;
  }

  /**
   * 链式检查多个Repository结果
   *
   * 使用场景：
   * - 需要多个前置检查都成功才执行主要操作
   * - 复杂的业务流程验证
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * const character = await this.characterRepository.findById(characterId);
   * const nameExists = await this.characterRepository.existsByName(newName);
   * if (nameExists) {
   *   throw new BadRequestException('角色名已存在');
   * }
   * character.name = newName;
   * return await character.save();
   *
   * // 转换后
   * return await ServiceResultHandler.chainChecks([
   *   () => this.characterRepository.findById(characterId),
   *   () => this.characterRepository.existsByName(newName).then(result =>
   *     ResultUtils.isSuccess(result) && result.data
   *       ? ResultUtils.error('角色名已存在', 'NAME_TAKEN')
   *       : ResultUtils.ok(true)
   *   )
   * ], async () => {
   *   const characterResult = await this.characterRepository.findById(characterId);
   *   const character = characterResult.data;
   *   character.name = newName;
   *   return await this.characterRepository.update(character);
   * });
   * ```
   *
   * @param checks 前置检查函数数组
   * @param finalOperation 最终执行的操作
   * @returns Promise<Result<T>> 最终操作的结果
   */
  static async chainChecks<T>(
    checks: (() => Promise<XResult<any>>)[],
    finalOperation: () => Promise<XResult<T>>
  ): Promise<XResult<T>> {
    for (const check of checks) {
      const result = await check();
      if (XResultUtils.isFailure(result)) {
        return result as XResult<T>;
      }
    }
    return await finalOperation();
  }

  /**
   * 快速检查并传递错误（推荐使用）
   *
   * 使用场景：
   * - Repository调用后的标准错误检查
   * - 需要将Repository错误传递给上层
   * - 最常用的错误处理模式
   *
   * 转换示例：
   * ```typescript
   * // 原始代码
   * const character = await this.characterRepository.findById(id);
   * if (!character) {
   *   throw new NotFoundException('角色不存在');
   * }
   * character.name = newName;
   * return await character.save();
   *
   * // 转换后
   * const characterResult = await this.characterRepository.findById(id);
   * const errorResult = ServiceResultHandler.propagateError<CharacterDocument>(characterResult);
   * if (errorResult) return errorResult;
   *
   * const character = characterResult.data;
   * character.name = newName;
   * const saveResult = await this.characterRepository.update(character);
   * const saveError = ServiceResultHandler.propagateError<CharacterDocument>(saveResult);
   * if (saveError) return saveError;
   *
   * return ResultUtils.ok(saveResult.data);
   * ```
   *
   * @param result Repository返回的Result
   * @returns 失败时返回转换后的Result，成功时返回null
   */
  static propagateError<T, U>(result: XResult<T>): XResult<U> | null {
    return XResultUtils.isFailure(result) ? (result as XResult<U>) : null;
  }
}

// ========== 新增：事务管理器Result适配版本 ==========

/**
 * 支持Result模式的事务管理器
 * 提供与Result模式完全兼容的事务处理能力
 *
 * 核心特性：
 * - 自动将事务异常转换为Result
 * - 支持事务重试机制
 * - 保持ACID特性
 * - 与原有事务管理器兼容
 */
export class TransactionResultManager {
  private readonly logger = console; // 可以注入实际的Logger

  /**
   * 执行事务操作并返回Result
   *
   * 使用场景：
   * - 需要在事务中执行多个Repository操作
   * - 自动处理事务的提交和回滚
   * - 将事务异常转换为Result
   *
   * 转换示例：
   * ```typescript
   * import { ClientSession } from 'mongoose';
   *
   * // 原始代码
   * async executeTransfer(playerId: string, newTeamId: string): Promise<PlayerDocument> {
   *   return this.transactionManager.executeTransaction(async (session) => {
   *     const player = await this.findById(playerId, { session });
   *     if (!player) {
   *       throw new Error('Player not found');
   *     }
   *     return await this.transferPlayer(playerId, newTeamId, session);
   *   });
   * }
   *
   * // 转换后
   * async executeTransfer(playerId: string, newTeamId: string): Promise<Result<PlayerDocument>> {
   *   return await TransactionResultManager.executeTransaction(async (session) => {
   *     const playerResult = await this.findById(playerId, { session });
   *     const errorResult = ServiceResultHandler.propagateError<PlayerDocument>(playerResult);
   *     if (errorResult) return errorResult;
   *
   *     const player = playerResult.data;
   *     if (!player) {
   *       return ResultUtils.error('Player not found', 'PLAYER_NOT_FOUND');
   *     }
   *
   *     return await this.transferPlayer(playerId, newTeamId, session);
   *   });
   * }
   * ```
   *
   * @param operation 事务操作函数，返回Result<T>
   * @returns Promise<Result<T>> 事务执行结果
   */
  static async executeTransaction<T>(
    operation: (session: any) => Promise<XResult<T>>
  ): Promise<XResult<T>> {
    // 动态导入mongoose，避免在非MongoDB环境中出错
    let mongoose: any;
    try {
      mongoose = require('mongoose');
    } catch (error) {
      return XResultUtils.error('Mongoose not available', 'MONGOOSE_NOT_FOUND');
    }

    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      const result = await operation(session);

      // 检查操作结果
      if (XResultUtils.isFailure(result)) {
        await session.abortTransaction();
        return result;
      }

      await session.commitTransaction();
      return result;

    } catch (error: any) {
      await session.abortTransaction();
      console.error('Transaction failed:', error);
      return XResultUtils.error(
        error.message || '事务执行失败',
        'TRANSACTION_ERROR'
      );

    } finally {
      session.endSession();
    }
  }

  /**
   * 执行带重试机制的事务操作
   *
   * 使用场景：
   * - 处理并发冲突导致的事务失败
   * - 网络不稳定环境下的事务重试
   * - 提高事务成功率
   *
   * 转换示例：
   * ```typescript
   * // Service层使用
   * async processBulkTransfers(transfers: TransferRequest[]): Promise<Result<void>> {
   *   return await TransactionResultManager.executeWithRetry(async (session) => {
   *     for (const transfer of transfers) {
   *       const result = await this.playerRepo.transferPlayer(
   *         transfer.playerId,
   *         transfer.newTeamId,
   *         session
   *       );
   *
   *       const errorResult = ServiceResultHandler.propagateError<void>(result);
   *       if (errorResult) return errorResult;
   *     }
   *
   *     return ResultUtils.empty();
   *   }, 3);
   * }
   * ```
   *
   * @param operation 事务操作函数
   * @param maxRetries 最大重试次数，默认3次
   * @returns Promise<Result<T>> 事务执行结果
   */
  static async executeWithRetry<T>(
    operation: (session: any) => Promise<XResult<T>>,
    maxRetries = 3
  ): Promise<XResult<T>> {
    let lastResult: XResult<T>;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      lastResult = await this.executeTransaction(operation);

      if (XResultUtils.isSuccess(lastResult)) {
        return lastResult;
      }

      // 检查是否为可重试的错误
      if (this.isRetryableError(lastResult) && attempt < maxRetries) {
        await this.delay(attempt * 1000); // 指数退避
        continue;
      }

      // 不可重试的错误或达到最大重试次数
      break;
    }

    return lastResult!;
  }

  /**
   * 检查是否为可重试的错误
   */
  private static isRetryableError(result: XResult<any>): boolean {
    if (XResultUtils.isSuccess(result)) return false;

    const retryableCodes = [
      'TRANSACTION_ERROR',
      'DATABASE_ERROR',
      'NETWORK_ERROR'
    ];

    return retryableCodes.includes(result.code);
  }

  /**
   * 延迟函数
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 异常转Result工具类
 * 用于将传统的异常处理转换为Result模式
 */
export class ExceptionToResultUtils {
  /**
   * 将可能抛出异常的函数包装为返回Result的函数
   */
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    errorCodeMap?: Record<string, string>
  ): Promise<XResult<T>> {
    try {
      const data = await fn();
      return XResultUtils.success(data);
    } catch (error) {
      return this.handleError(error, errorCodeMap);
    }
  }

  /**
   * 将可能抛出异常的同步函数包装为返回Result的函数
   */
  static wrap<T>(
    fn: () => T,
    errorCodeMap?: Record<string, string>
  ): XResult<T> {
    try {
      const data = fn();
      return XResultUtils.success(data);
    } catch (error) {
      return this.handleError(error, errorCodeMap);
    }
  }

  /**
   * 处理错误并转换为FailureResult
   */
  private static handleError(
    error: any,
    errorCodeMap?: Record<string, string>
  ): FailureResult {
    // 如果已经是Result类型，直接返回
    if (error && typeof error === 'object' && 'success' in error && error.success === false) {
      return error as FailureResult;
    }

    // 处理NestJS异常
    if (error && typeof error === 'object' && error.response) {
      const response = error.response;
      return XResultUtils.failure(
        response.code || error.name || 'HTTP_EXCEPTION',
        response.message || error.message || 'HTTP异常',
        {
          statusCode: error.status,
          originalError: error.name,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      );
    }

    // 处理MongoDB错误
    if (error && error.name === 'MongoError') {
      return this.handleMongoError(error);
    }

    // 处理验证错误
    if (error && error.name === 'ValidationError') {
      return XResultUtils.failure(
        'VALIDATION_ERROR',
        '数据验证失败',
        { validationErrors: error.errors }
      );
    }

    // 使用错误码映射
    if (errorCodeMap && error.message && errorCodeMap[error.message]) {
      return XResultUtils.failure(
        errorCodeMap[error.message],
        error.message,
        { originalError: error.name }
      );
    }

    // 默认错误处理
    return XResultUtils.failure(
      error.code || error.name || CommonErrorCodes.UNKNOWN_ERROR,
      error.message || '未知错误',
      {
        originalError: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    );
  }

  /**
   * 处理MongoDB特定错误
   */
  private static handleMongoError(error: any): FailureResult {
    switch (error.code) {
      case 11000: // 重复键错误
        return XResultUtils.failure(
          CommonErrorCodes.RESOURCE_ALREADY_EXISTS,
          '数据已存在',
          { duplicateKey: error.keyValue }
        );
      case 121: // 文档验证失败
        return XResultUtils.failure(
          'DOCUMENT_VALIDATION_ERROR',
          '文档验证失败',
          { validationError: error.errInfo }
        );
      default:
        return XResultUtils.failure(
          'DATABASE_ERROR',
          error.message || '数据库操作失败',
          { mongoErrorCode: error.code }
        );
    }
  }
}

/**
 * 微服务响应接口规范
 */
export interface XResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
  requestId?: string;
}

/**
 * 微服务响应工具类
 */
export class XResponseUtils {
  /**
   * 从Result创建微服务响应
   */
  static fromResult<T>(result: XResult<T>, requestId?: string): XResponse<T> {
    if (XResultUtils.isSuccess(result)) {
      return {
        code: 0,
        message: result.message || '操作成功',
        data: result.data,
        timestamp: Date.now(),
        requestId
      };
    } else {
      return {
        code: this.getErrorCode(result.code),
        message: result.message,
        data: null as T,
        timestamp: Date.now(),
        requestId
      };
    }
  }

  /**
   * 创建成功响应
   * @param data 响应数据
   * @param message 响应消息
   * @param requestId 请求ID
   * @returns XResponse
   * @template T 响应数据类型
   * 使用场景：
   * - 在Controller层将Result转换为统一的响应格式
   * - 在Controller层创建成功响应
   *
   * 使用示例：
   * ```typescript
   * return MicroserviceResponseUtils.success(data, '操作成功', requestId);
   * ```
   */
  static success<T>(data: T, message: string = '操作成功', requestId?: string): XResponse<T> {
    return {
      code: 0,
      message,
      data,
      timestamp: Date.now(),
      requestId
    };
  }

  /**
   * 创建错误响应
   * @param code 错误码
   * @param message 错误消息
   * @param requestId 请求ID
   * @returns XResponse
   * @template T 响应数据类型
   *
   * 使用场景：
   * - 在Controller层创建错误响应
   *
   * 使用示例：
   * ```typescript
   * return MicroserviceResponseUtils.error(1001, '参数错误', requestId);
   * ```
   */
  static error<T = null>(code: number, message: string, requestId?: string): XResponse<T> {
    return {
      code,
      message,
      data: null as T,
      timestamp: Date.now(),
      requestId
    };
  }

  /**
   * 将错误码转换为数字码
   */
  private static getErrorCode(code: string): number {
    const errorCodeMap: Record<string, number> = {
      // 通用错误 1000-1999
      [CommonErrorCodes.UNKNOWN_ERROR]: 1000,
      [CommonErrorCodes.INVALID_PARAMETER]: 1001,
      [CommonErrorCodes.PERMISSION_DENIED]: 1002,

      // 资源错误 2000-2999
      [CommonErrorCodes.RESOURCE_NOT_FOUND]: 2000,
      [CommonErrorCodes.RESOURCE_ALREADY_EXISTS]: 2001,
      [CommonErrorCodes.RESOURCE_CONFLICT]: 2002,

      // 业务逻辑错误 3000-3999
      [CommonErrorCodes.BUSINESS_RULE_VIOLATION]: 3000,
      [CommonErrorCodes.INSUFFICIENT_RESOURCES]: 3001,
      [CommonErrorCodes.OPERATION_NOT_ALLOWED]: 3002,

      // 外部服务错误 4000-4999
      [CommonErrorCodes.EXTERNAL_SERVICE_ERROR]: 4000,
      [CommonErrorCodes.NETWORK_ERROR]: 4001,
      [CommonErrorCodes.TIMEOUT_ERROR]: 4002,

      // Character服务特定错误 5000-5999
      'CHARACTER_NOT_FOUND': 5000,
      'CHARACTER_NAME_TAKEN': 5001,
      'CHARACTER_LEVEL_MAX': 5002,
      'CHARACTER_ENERGY_INSUFFICIENT': 5003,
      'CHARACTER_CASH_INSUFFICIENT': 5004,
      'CHARACTER_GOLD_INSUFFICIENT': 5005,

      // 数据库错误 9000-9999
      'DATABASE_ERROR': 9000,
      'DOCUMENT_VALIDATION_ERROR': 9001,
      'VALIDATION_ERROR': 9002,
    };

    return errorCodeMap[code] || 1000; // 默认返回通用错误码
  }
}
