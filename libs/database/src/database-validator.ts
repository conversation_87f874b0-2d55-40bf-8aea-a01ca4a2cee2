import { ConfigService } from '@nestjs/config';

/**
 * 数据库配置验证结果
 */
export interface DatabaseValidationResult {
  serviceName: string;
  isValid: boolean;
  warnings: string[];
  errors: string[];
  recommendations: string[];
  config: {
    uri: string;
    hasReplicaSet: boolean;
    transactionSupport: 'FULL' | 'DEGRADED' | 'NONE';
    readPreference: string;
    writeConcern: string | number;
  };
}

/**
 * 数据库配置验证器
 * 
 * 用于验证数据库配置是否符合最佳实践
 */
export class DatabaseConfigValidator {
  
  /**
   * 验证服务的数据库配置
   * @param serviceName 服务名称
   * @param configService 配置服务
   * @returns 验证结果
   */
  static validateServiceConfig(
    serviceName: string, 
    configService: ConfigService
  ): DatabaseValidationResult {
    const result: DatabaseValidationResult = {
      serviceName,
      isValid: true,
      warnings: [],
      errors: [],
      recommendations: [],
      config: {
        uri: '',
        hasReplicaSet: false,
        transactionSupport: 'NONE',
        readPreference: 'primary',
        writeConcern: 1
      }
    };

    // 检查基础配置
    this.validateBasicConfig(serviceName, configService, result);
    
    // 检查副本集配置
    this.validateReplicaSetConfig(serviceName, configService, result);
    
    // 检查事务配置
    this.validateTransactionConfig(serviceName, configService, result);
    
    // 生成建议
    this.generateRecommendations(serviceName, result);

    return result;
  }

  /**
   * 验证基础配置
   */
  private static validateBasicConfig(
    serviceName: string,
    configService: ConfigService,
    result: DatabaseValidationResult
  ) {
    const servicePrefix = serviceName.toUpperCase();
    
    // 检查必需的环境变量
    const requiredVars = [
      `${servicePrefix}_MONGODB_HOST`,
      `${servicePrefix}_MONGODB_PORT`, 
      `${servicePrefix}_MONGODB_USERNAME`,
      `${servicePrefix}_MONGODB_PASSWORD`,
      `${servicePrefix}_MONGODB_DATABASE`
    ];

    for (const varName of requiredVars) {
      if (!configService.get(varName)) {
        result.errors.push(`缺少必需的环境变量: ${varName}`);
        result.isValid = false;
      }
    }

    // 构建URI
    if (result.isValid) {
      const host = configService.get(`${servicePrefix}_MONGODB_HOST`);
      const port = configService.get(`${servicePrefix}_MONGODB_PORT`);
      const username = configService.get(`${servicePrefix}_MONGODB_USERNAME`);
      const password = configService.get(`${servicePrefix}_MONGODB_PASSWORD`);
      const database = configService.get(`${servicePrefix}_MONGODB_DATABASE`);
      
      result.config.uri = `mongodb://${username}:***@${host}:${port}/${database}`;
    }
  }

  /**
   * 验证副本集配置
   */
  private static validateReplicaSetConfig(
    serviceName: string,
    configService: ConfigService,
    result: DatabaseValidationResult
  ) {
    const servicePrefix = serviceName.toUpperCase();
    const replicaSet = configService.get(`${servicePrefix}_MONGODB_REPLICA_SET`) ||
                       configService.get('MONGODB_REPLICA_SET');
    
    result.config.hasReplicaSet = !!replicaSet;
    
    if (replicaSet) {
      result.config.uri += `?replicaSet=${replicaSet}`;
      result.config.transactionSupport = 'FULL';
    } else {
      result.config.transactionSupport = 'DEGRADED';
      
      // 检查是否为关键服务
      const criticalServices = ['auth', 'economy', 'match'];
      if (criticalServices.includes(serviceName)) {
        result.warnings.push(`关键服务 ${serviceName} 未配置副本集，事务功能将降级`);
        result.recommendations.push(`建议为 ${serviceName} 服务配置副本集以获得完整的ACID事务支持`);
      }
    }

    // 检查读写配置
    const readPreference = configService.get(`${servicePrefix}_MONGODB_READ_PREFERENCE`) ||
                          configService.get('MONGODB_READ_PREFERENCE') ||
                          'primary';
    
    const writeConcern = configService.get(`${servicePrefix}_MONGODB_WRITE_CONCERN`) ||
                        configService.get('MONGODB_WRITE_CONCERN') ||
                        1;

    result.config.readPreference = readPreference;
    result.config.writeConcern = writeConcern;
  }

  /**
   * 验证事务配置
   */
  private static validateTransactionConfig(
    serviceName: string,
    configService: ConfigService,
    result: DatabaseValidationResult
  ) {
    if (!result.config.hasReplicaSet) {
      result.warnings.push('未配置副本集，事务将在降级模式下运行');
      return;
    }

    // 检查事务相关配置
    const criticalServices = ['auth', 'economy', 'match'];
    const isCritical = criticalServices.includes(serviceName);

    if (isCritical) {
      // 关键服务的事务配置检查
      if (result.config.readPreference !== 'primary') {
        result.warnings.push(`关键服务建议使用 readPreference: 'primary'`);
      }

      if (result.config.writeConcern !== 'majority') {
        result.warnings.push(`关键服务建议使用 writeConcern: 'majority'`);
      }
    }
  }

  /**
   * 生成配置建议
   */
  private static generateRecommendations(
    serviceName: string,
    result: DatabaseValidationResult
  ) {
    const criticalServices = ['auth', 'economy', 'match'];
    const isCritical = criticalServices.includes(serviceName);

    if (isCritical && !result.config.hasReplicaSet) {
      result.recommendations.push(
        `🔥 强烈建议：${serviceName} 是关键服务，应配置副本集以获得：`,
        '   • 完整的ACID事务支持',
        '   • 高可用性保证', 
        '   • 数据安全保护'
      );
    }

    if (!isCritical && !result.config.hasReplicaSet) {
      result.recommendations.push(
        `💡 建议：${serviceName} 服务当前配置合理，如需完整事务支持可考虑副本集`
      );
    }

    // 性能优化建议
    result.recommendations.push(
      '⚡ 性能优化建议：',
      '   • 根据业务需求调整连接池大小',
      '   • 监控数据库连接和查询性能',
      '   • 定期备份数据（特别是单节点部署）'
    );
  }

  /**
   * 打印验证结果
   */
  static printValidationResult(result: DatabaseValidationResult) {
    console.log(`\n📊 数据库配置验证结果 - ${result.serviceName}`);
    console.log('='.repeat(50));
    
    console.log(`✅ 配置状态: ${result.isValid ? '有效' : '无效'}`);
    console.log(`🔗 连接URI: ${result.config.uri}`);
    console.log(`🔄 副本集: ${result.config.hasReplicaSet ? '已配置' : '未配置'}`);
    console.log(`⚡ 事务支持: ${result.config.transactionSupport}`);
    console.log(`📖 读偏好: ${result.config.readPreference}`);
    console.log(`✍️ 写关注: ${result.config.writeConcern}`);

    if (result.errors.length > 0) {
      console.log('\n❌ 错误:');
      result.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️ 警告:');
      result.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    if (result.recommendations.length > 0) {
      console.log('\n💡 建议:');
      result.recommendations.forEach(rec => console.log(`   ${rec}`));
    }

    console.log('='.repeat(50));
  }
}
