/**
 * MongoDB数据库配置
 */

import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';

/**
 * 数据库连接配置接口
 */
export interface DatabaseConnectionOptions {
  maxPoolSize?: number;
  serverSelectionTimeoutMS?: number;
  socketTimeoutMS?: number;
  connectTimeoutMS?: number;
  retryWrites?: boolean;
  retryReads?: boolean;
  bufferCommands?: boolean;
  autoIndex?: boolean;
  autoCreate?: boolean;
  // 副本集相关配置
  replicaSet?: string;
  readPreference?: 'primary' | 'secondary' | 'primaryPreferred' | 'secondaryPreferred' | 'nearest';
  readConcern?: 'local' | 'available' | 'majority' | 'linearizable' | 'snapshot';
  writeConcern?: 'majority' | number;
  // 事务相关配置
  transactionOptions?: {
    readPreference?: string;
    readConcern?: string;
    writeConcern?: string | number;
    maxCommitTimeMS?: number;
  };
}

/**
 * 服务数据库配置接口
 */
export interface ServiceDatabaseConfig {
  serviceName: string;
  serverId?: string;
  uri: string;
  dbName: string;
  options: DatabaseConnectionOptions;
}

/**
 * 创建MongoDB配置（共享密码 + 动态拼接方案 V2.0）
 * 所有区服共享基础配置，通过SERVER_ID动态生成用户名和数据库名
 *
 * 核心优势：
 * - 配置统一：所有区服共享一份配置文件
 * - 密码管理：统一密码管理，修改一处全部生效
 * - 扩展简单：新增区服只需设置SERVER_ID
 * - 权限隔离：每个区服仍有独立用户和数据库
 *
 * @param configService 配置服务
 * @param serviceName 服务名称
 * @returns Mongoose模块配置
 */
export const createMongoConfig = (configService: ConfigService, serviceName: string): MongooseModuleOptions => {
  const serverId = configService.get<string>('SERVER_ID')?.trim();

  // 获取共享的基础配置
  const host = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_HOST`) || '***************';
  const port = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_PORT`) || '27017';
  const password = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_PASSWORD`);

  // 获取命名前缀（支持自定义）
  const usernamePrefix = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_USERNAME_PREFIX`) || `${serviceName}-admin`;
  const dbPrefix = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_DB_PREFIX`) || `${serviceName}_db`;

  if (!password) {
    throw new Error(`数据库密码未配置: ${serviceName.toUpperCase()}_MONGODB_PASSWORD`);
  }

  // 动态生成用户名和数据库名
  const username = generateUsername(serviceName, usernamePrefix, serverId);
  const dbName = generateDatabaseName(serviceName, dbPrefix, serverId);

  // 构建完整URI（包含认证数据库）
  const uri = `mongodb://${username}:${password}@${host}:${port}/${dbName}`;

  // 获取连接选项
  const options = getConnectionOptions(configService, serviceName);

  // 输出配置信息（便于调试）
  console.log(`[${serviceName}] 🔗 MongoDB配置:`);
  console.log(`  服务: ${serviceName}`);
  console.log(`  区服: ${serverId || 'default'}`);
  console.log(`  用户: ${username}`);
  console.log(`  数据库: ${dbName}`);
  console.log(`  主机: ${host}:${port}`);
  console.log(`  完整URI: "${uri}"`);

  return {
    uri,
    dbName,
    ...options,
  };
};

/**
 * 生成用户名（V2.0 动态拼接）
 * @param serviceName 服务名称
 * @param usernamePrefix 用户名前缀
 * @param serverId 区服ID
 * @returns 用户名
 */
function generateUsername(serviceName: string, usernamePrefix: string, serverId?: string): string {
  // 认证服务使用全局用户（不分区服）
  if (serviceName === 'auth') {
    return 'auth-admin';
  }

  // 其他服务使用区服特定用户
  return serverId ? `${usernamePrefix}-${serverId}` : usernamePrefix;
}

/**
 * 生成数据库名称（V2.0 动态拼接）
 * @param serviceName 服务名称
 * @param dbPrefix 数据库前缀
 * @param serverId 区服ID
 * @returns 数据库名称
 */
function generateDatabaseName(serviceName: string, dbPrefix: string, serverId?: string): string {
  // 认证服务始终使用全局数据库（不分区服）
  if (serviceName === 'auth') {
    return 'auth_db';
  }

  // 其他服务支持分区分服
  return serverId ? `${dbPrefix}_${serverId}` : dbPrefix;
}

/**
 * 获取连接配置选项
 * @param configService 配置服务
 * @param serviceName 服务名称
 * @returns 连接选项
 */
function getConnectionOptions(configService: ConfigService, serviceName: string): Record<string, any> {
  const servicePrefix = serviceName.toUpperCase();

  // 获取服务特定的连接池大小，如果没有则使用默认值
  const defaultPoolSize = getDefaultPoolSize(serviceName);

  return {
    // 连接池配置
    maxPoolSize: configService.get<number>(`${servicePrefix}_MONGODB_MAX_POOL_SIZE`) ||
                 configService.get<number>('MONGODB_MAX_POOL_SIZE') ||
                 defaultPoolSize,

    // 超时配置
    serverSelectionTimeoutMS: configService.get<number>(`${servicePrefix}_MONGODB_SERVER_SELECTION_TIMEOUT`) ||
                              configService.get<number>('MONGODB_SERVER_SELECTION_TIMEOUT') ||
                              5000,

    socketTimeoutMS: configService.get<number>(`${servicePrefix}_MONGODB_SOCKET_TIMEOUT`) ||
                     configService.get<number>('MONGODB_SOCKET_TIMEOUT') ||
                     45000,

    connectTimeoutMS: configService.get<number>(`${servicePrefix}_MONGODB_CONNECT_TIMEOUT`) ||
                      configService.get<number>('MONGODB_CONNECT_TIMEOUT') ||
                      10000,

    // 可靠性配置
    retryWrites: configService.get<boolean>(`${servicePrefix}_MONGODB_RETRY_WRITES`) ??
                 configService.get<boolean>('MONGODB_RETRY_WRITES') ??
                 true,

    retryReads: configService.get<boolean>(`${servicePrefix}_MONGODB_RETRY_READS`) ??
                configService.get<boolean>('MONGODB_RETRY_READS') ??
                true,

    // 缓冲配置
    bufferCommands: configService.get<boolean>(`${servicePrefix}_MONGODB_BUFFER_COMMANDS`) ??
                    configService.get<boolean>('MONGODB_BUFFER_COMMANDS') ??
                    true,

    // 自动创建配置
    autoIndex: configService.get<boolean>(`${servicePrefix}_MONGODB_AUTO_INDEX`) ??
               configService.get<boolean>('MONGODB_AUTO_INDEX') ??
               true,

    autoCreate: configService.get<boolean>(`${servicePrefix}_MONGODB_AUTO_CREATE`) ??
                configService.get<boolean>('MONGODB_AUTO_CREATE') ??
                true,
  };
}

/**
 * 获取服务默认连接池大小
 * @param serviceName 服务名称
 * @returns 默认连接池大小
 */
function getDefaultPoolSize(serviceName: string): number {
  // 根据服务特性设置不同的默认连接池大小
  const poolSizes: Record<string, number> = {
    auth: 10,        // 认证服务：中等并发
    character: 15,   // 角色服务：高并发
    hero: 15,        // 球员服务：高并发
    match: 15,       // 比赛服务：高并发
    economy: 10,     // 经济服务：中等并发
    social: 10,      // 社交服务：中等并发
    activity: 10,    // 活动服务：中等并发
  };

  return poolSizes[serviceName] || 10; // 默认10个连接
}

// 数据库健康检查配置
export const createHealthCheckConfig = (configService: ConfigService, serviceName: string) => {
  const config = createMongoConfig(configService, serviceName);
  return {
    name: `${serviceName}_mongodb`,
    uri: config.uri,
    timeout: 5000,
  };
};

// 数据库连接事件处理
export const setupDatabaseEvents = (serviceName: string) => {
  return {
    connectionFactory: (connection: any) => {
      connection.on('connected', () => {
        console.log(`[${serviceName}] MongoDB connected successfully`);
      });

      connection.on('disconnected', () => {
        console.log(`[${serviceName}] MongoDB disconnected`);
      });

      connection.on('error', (error: any) => {
        console.error(`[${serviceName}] MongoDB connection error:`, error);
      });

      connection.on('reconnected', () => {
        console.log(`[${serviceName}] MongoDB reconnected`);
      });

      return connection;
    },
  };
};
