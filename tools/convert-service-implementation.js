#!/usr/bin/env node

/**
 * Service层实现逻辑转换工具
 * 
 * 功能：
 * 1. 自动添加Repository调用的Result检查
 * 2. 转换throw语句为ResultUtils.error()
 * 3. 转换return语句为ResultUtils.ok()
 * 4. 智能处理Result传递链
 * 
 * 使用方法：
 * node tools/convert-service-implementation.js [选项]
 * 
 * 选项：
 * --dry-run              预览模式，不实际修改文件
 * --target=path          指定文件路径
 * --verbose              详细输出
 * 
 * 示例：
 * node tools/convert-service-implementation.js --target=apps/character/src/modules/character/character.service.ts --dry-run
 */

const fs = require('fs');
const path = require('path');

class ServiceImplementationConverter {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.targetPath = options.targetPath;
    this.verbose = options.verbose || false;
    this.stats = {
      methodsConverted: 0,
      repositoryCallsConverted: 0,
      throwStatementsConverted: 0,
      returnStatementsConverted: 0,
      errors: []
    };
  }

  /**
   * 执行转换
   */
  async convert() {
    if (!this.targetPath) {
      console.error('❌ 请指定目标文件路径 --target=path');
      return;
    }

    console.log('🚀 开始Service实现逻辑转换...');
    console.log(`📂 目标文件: ${this.targetPath}`);
    console.log('');

    await this.processFile(this.targetPath);
    this.printStats();
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      console.log(`📄 处理文件: ${filePath}`);

      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.convertFileContent(content);

      if (content !== modifiedContent) {
        if (this.dryRun) {
          console.log(`   ✏️ [DRY RUN] 文件将被修改`);
          if (this.verbose) {
            this.showDiff(content, modifiedContent);
          }
        } else {
          fs.writeFileSync(filePath, modifiedContent, 'utf8');
          console.log(`   ✅ 文件已修改`);
        }
      } else {
        console.log(`   ℹ️ 文件无需修改`);
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`   ❌ 处理文件失败: ${error.message}`);
    }
  }

  /**
   * 转换文件内容
   */
  convertFileContent(content) {
    let modified = content;

    // 1. 添加import语句（如果需要）
    if (!content.includes('ServiceResultHandler')) {
      modified = this.addImportStatement(modified);
    }

    // 2. 转换方法实现
    modified = this.convertMethodImplementations(modified);

    return modified;
  }

  /**
   * 添加import语句
   */
  addImportStatement(content) {
    // 查找Result相关的import
    const resultImportRegex = /import\s*{[^}]*Result[^}]*}\s*from\s*['"][^'"]*result\.type['"];?/;
    const match = content.match(resultImportRegex);

    if (match) {
      // 如果已有Result import，添加ServiceResultHandler
      const existingImport = match[0];
      if (!existingImport.includes('ServiceResultHandler')) {
        const newImport = existingImport.replace(
          /}\s*from/,
          ', ServiceResultHandler } from'
        );
        return content.replace(existingImport, newImport);
      }
    } else {
      // 添加新的import
      const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?\s*\n/g;
      let lastImportMatch;
      let match;
      
      while ((match = importRegex.exec(content)) !== null) {
        lastImportMatch = match;
      }

      if (lastImportMatch) {
        const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
        const importStatement = "import { Result, ResultUtils, ServiceResultHandler } from '@libs/common/types/result.type';\n";
        
        return content.slice(0, insertPosition) + 
               importStatement + 
               content.slice(insertPosition);
      }
    }

    return content;
  }

  /**
   * 转换方法实现
   */
  convertMethodImplementations(content) {
    let modified = content;

    // 匹配async方法
    const methodRegex = /async\s+(\w+)\s*\([^)]*\)\s*:\s*Promise<Result<[^>]+>>\s*{([\s\S]*?)(?=\n\s*(?:async\s+\w+|}\s*$|\/\*\*|\n\s*$))/g;

    let match;
    while ((match = methodRegex.exec(content)) !== null) {
      const methodName = match[1];
      const methodBody = match[2];
      const fullMatch = match[0];

      if (this.verbose) {
        console.log(`     🔧 转换方法: ${methodName}`);
      }

      // 检查是否已经使用了Result模式
      if (methodBody.includes('ResultUtils.') || methodBody.includes('ServiceResultHandler.')) {
        continue; // 已经转换过，跳过
      }

      const convertedMethod = this.convertSingleMethod(methodName, methodBody, fullMatch);
      if (convertedMethod !== fullMatch) {
        modified = modified.replace(fullMatch, convertedMethod);
        this.stats.methodsConverted++;
      }
    }

    return modified;
  }

  /**
   * 转换单个方法
   */
  convertSingleMethod(methodName, methodBody, fullMatch) {
    let convertedBody = methodBody;

    // 1. 转换Repository调用
    convertedBody = this.convertRepositoryCalls(convertedBody);

    // 2. 转换throw语句
    convertedBody = this.convertThrowStatements(convertedBody);

    // 3. 转换return语句
    convertedBody = this.convertReturnStatements(convertedBody);

    return fullMatch.replace(methodBody, convertedBody);
  }

  /**
   * 转换Repository调用
   */
  convertRepositoryCalls(methodBody) {
    let converted = methodBody;

    // 匹配Repository调用模式
    const repoCallRegex = /(\s*)(const\s+(\w+)\s*=\s*await\s+this\.(\w+Repository)\.(\w+)\([^)]*\);?)/g;

    let match;
    const replacements = [];

    while ((match = repoCallRegex.exec(methodBody)) !== null) {
      const [fullMatch, indent, originalCall, varName, repoName, methodName] = match;

      // 修复：正确构建Repository调用
      const repoCall = originalCall.replace(`const ${varName} =`, '').trim();

      const replacement = `${indent}const ${varName}Result = ${repoCall}
${indent}const errorResult = ServiceResultHandler.propagateError<any>(${varName}Result);
${indent}if (errorResult) return errorResult;
${indent}const ${varName} = ${varName}Result.data;`;

      replacements.push({ original: fullMatch, replacement });
      this.stats.repositoryCallsConverted++;
    }

    // 应用替换
    for (const { original, replacement } of replacements) {
      converted = converted.replace(original, replacement);
    }

    return converted;
  }

  /**
   * 转换throw语句
   */
  convertThrowStatements(methodBody) {
    let converted = methodBody;

    // 匹配各种throw模式
    const throwPatterns = [
      {
        regex: /throw\s+new\s+BadRequestException\(\s*{\s*code:\s*ErrorCode\.(\w+),\s*message:\s*([^}]+)\s*}\s*\);?/g,
        replacement: (match, code, message) => `return ResultUtils.error(${message}, '${code}');`
      },
      {
        regex: /throw\s+new\s+NotFoundException\(\s*([^)]+)\s*\);?/g,
        replacement: (match, message) => `return ResultUtils.error(${message}, 'RESOURCE_NOT_FOUND');`
      },
      {
        regex: /throw\s+new\s+Error\(\s*([^)]+)\s*\);?/g,
        replacement: (match, message) => `return ResultUtils.error(${message});`
      },
      {
        regex: /throw\s+([^;]+);?/g,
        replacement: (match, expression) => `return ResultUtils.error('操作失败');`
      }
    ];

    for (const pattern of throwPatterns) {
      const matches = [...converted.matchAll(pattern.regex)];
      for (const match of matches) {
        const replacement = pattern.replacement(...match);
        converted = converted.replace(match[0], replacement);
        this.stats.throwStatementsConverted++;
      }
    }

    return converted;
  }

  /**
   * 转换return语句
   */
  convertReturnStatements(methodBody) {
    let converted = methodBody;

    // 匹配return语句（排除已经是Result的）
    const returnRegex = /(\s*return\s+)([^;]+)(;?)/g;

    let match;
    const replacements = [];

    while ((match = returnRegex.exec(methodBody)) !== null) {
      const [fullMatch, returnKeyword, returnValue, semicolon] = match;
      
      // 跳过已经是Result的return语句
      if (returnValue.includes('ResultUtils.') || returnValue.includes('Result<')) {
        continue;
      }

      const replacement = `${returnKeyword}ResultUtils.ok(${returnValue})${semicolon}`;
      replacements.push({ original: fullMatch, replacement });
      this.stats.returnStatementsConverted++;
    }

    // 应用替换
    for (const { original, replacement } of replacements) {
      converted = converted.replace(original, replacement);
    }

    return converted;
  }

  /**
   * 显示差异
   */
  showDiff(original, modified) {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    console.log('   📋 变更预览:');
    
    let diffCount = 0;
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length) && diffCount < 30; i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine !== modifiedLine) {
        if (originalLine) {
          console.log(`   - ${originalLine}`);
        }
        if (modifiedLine) {
          console.log(`   + ${modifiedLine}`);
        }
        diffCount++;
      }
    }
    
    if (diffCount >= 30) {
      console.log('   ... (更多变更)');
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 转换统计:');
    console.log(`  🔧 转换方法数: ${this.stats.methodsConverted}`);
    console.log(`  📞 Repository调用转换: ${this.stats.repositoryCallsConverted}`);
    console.log(`  🚫 throw语句转换: ${this.stats.throwStatementsConverted}`);
    console.log(`  ↩️ return语句转换: ${this.stats.returnStatementsConverted}`);
    
    if (this.stats.errors.length > 0) {
      console.log(`  ❌ 错误数量: ${this.stats.errors.length}`);
      this.stats.errors.forEach(error => {
        console.log(`     ${error.file}: ${error.error}`);
      });
    }
    
    if (this.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
      console.log('   移除 --dry-run 参数来执行实际修改');
    } else {
      console.log('\n✅ 转换完成！');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose') {
      options.verbose = true;
    } else if (arg.startsWith('--target=')) {
      options.targetPath = arg.split('=')[1];
    }
  }
  
  const converter = new ServiceImplementationConverter(options);
  converter.convert().catch(console.error);
}

module.exports = ServiceImplementationConverter;
