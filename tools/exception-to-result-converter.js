#!/usr/bin/env node

/**
 * 异常转Result模式自动转换工具 v2.0
 *
 * 🎯 核心功能：
 * 1. 智能识别调用链层级（Repository/Service/Controller）
 * 2. Result传递链模式：避免重复创建Result
 * 3. 统一Controller接口约束：MicroserviceResponse格式
 * 4. 批量转换MessagePattern方法
 * 5. 保持错误上下文完整传递
 * 6. 生成转换报告和回滚脚本
 */

const fs = require('fs');
const path = require('path');
const { Project, SyntaxKind } = require('ts-morph');

class ExceptionToResultConverter {
  constructor(options = {}) {
    this.options = {
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      backup: false, // 禁用备份，使用git管理
      generateReport: options.generateReport !== false,
      generateRollback: options.generateRollback !== false,
      ...options
    };

    this.project = new Project({
      compilerOptions: {
        target: 'ES2020',
        module: 'CommonJS',
        strict: false,
        skipLibCheck: true,
        allowJs: true,
      },
    });

    this.stats = {
      filesProcessed: 0,
      repositoryFiles: 0,
      serviceFiles: 0,
      controllerFiles: 0,
      throwsConverted: 0,
      functionsUpdated: 0,
      messagePatternUpdated: 0,
      importsAdded: 0,
      errors: []
    };

    this.conversionReport = [];
    this.rollbackCommands = [];
  }

  /**
   * 转换指定目录或文件
   */
  async convertDirectory(dirPath) {
    const stat = fs.statSync(dirPath);

    if (stat.isFile()) {
      console.log(`🔍 处理单个文件: ${dirPath}`);
      await this.convertFile(dirPath);
    } else {
      console.log(`🔍 开始扫描目录: ${dirPath}`);
      const files = this.findTypeScriptFiles(dirPath);
      console.log(`📁 找到 ${files.length} 个TypeScript文件`);

      for (const filePath of files) {
        await this.convertFile(filePath);
      }
    }

    this.printStats();
  }

  /**
   * 转换单个文件
   */
  async convertFile(filePath) {
    try {
      if (this.options.verbose) {
        console.log(`📄 处理文件: ${filePath}`);
      }

      // 创建备份
      if (this.options.backup && !this.options.dryRun) {
        this.createBackup(filePath);
      }

      const sourceFile = this.project.addSourceFileAtPath(filePath);
      let hasChanges = false;

      // 1. 添加必要的import
      if (this.addRequiredImports(sourceFile)) {
        hasChanges = true;
        this.stats.importsAdded++;
      }

      // 2. 识别文件层级并转换
      const layer = this.identifyFileLayer(filePath, sourceFile);
      if (layer !== 'unknown') {
        if (this.convertClassMethods(sourceFile, layer)) {
          hasChanges = true;
        }
      } else {
        if (this.options.verbose) {
          console.log(`  ⚠️ 跳过未识别层级的文件`);
        }
      }

      // 3. 保存文件
      if (hasChanges && !this.options.dryRun) {
        await sourceFile.save();
      }

      this.stats.filesProcessed++;

    } catch (error) {
      console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    }
  }

  /**
   * 查找TypeScript文件
   */
  findTypeScriptFiles(dirPath) {
    const files = [];

    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(fullPath);
        } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
          // 只处理Repository和Service文件
          if (item.includes('.repository.') || item.includes('.service.')) {
            files.push(fullPath);
          }
        }
      }
    };

    scanDir(dirPath);
    return files;
  }

  /**
   * 添加必要的import语句
   */
  addRequiredImports(sourceFile) {
    const imports = sourceFile.getImportDeclarations();
    const hasResultImport = imports.some(imp =>
      imp.getModuleSpecifierValue().includes('result.type') ||
      imp.getNamedImports().some(named =>
        ['Result', 'ResultUtils', 'ExceptionToResultUtils'].includes(named.getName())
      )
    );

    if (!hasResultImport) {
      sourceFile.addImportDeclaration({
        moduleSpecifier: '@libs/common/types/result.type',
        namedImports: ['Result', 'ResultUtils', 'ExceptionToResultUtils']
      });

      if (this.options.verbose) {
        console.log(`  ✅ 添加Result类型导入`);
      }

      return true;
    }

    return false;
  }

  /**
   * 智能识别文件层级
   */
  identifyFileLayer(filePath, sourceFile) {
    const fileName = path.basename(filePath);
    const classes = sourceFile.getClasses();

    if (fileName.includes('.repository.') || classes.some(c => c.getName()?.includes('Repository'))) {
      return 'repository';
    }

    if (fileName.includes('.service.') || classes.some(c => c.getName()?.includes('Service'))) {
      return 'service';
    }

    if (fileName.includes('.controller.') || classes.some(c => c.getName()?.includes('Controller'))) {
      return 'controller';
    }

    return 'unknown';
  }

  /**
   * 转换类方法（基于层级）
   */
  convertClassMethods(sourceFile, layer) {
    let hasChanges = false;
    const classes = sourceFile.getClasses();

    for (const classDeclaration of classes) {
      const className = classDeclaration.getName();

      if (!className) continue;

      if (this.options.verbose) {
        console.log(`  🔧 处理${layer}层类: ${className}`);
      }

      const methods = classDeclaration.getMethods();

      for (const method of methods) {
        const conversionResult = this.convertMethodByLayer(method, layer, className);
        if (conversionResult.hasChanges) {
          hasChanges = true;
          this.stats.functionsUpdated++;

          if (layer === 'controller' && conversionResult.isMessagePattern) {
            this.stats.messagePatternUpdated++;
          }

          // 记录转换详情
          this.conversionReport.push({
            file: sourceFile.getFilePath(),
            layer,
            className,
            methodName: method.getName(),
            changes: conversionResult.changes
          });
        }
      }

      // 更新统计
      if (layer === 'repository') this.stats.repositoryFiles++;
      else if (layer === 'service') this.stats.serviceFiles++;
      else if (layer === 'controller') this.stats.controllerFiles++;
    }

    return hasChanges;
  }

  /**
   * 基于层级转换方法
   */
  convertMethodByLayer(method, layer, className) {
    const methodName = method.getName();

    // 跳过构造函数和私有方法
    if (methodName === 'constructor' || method.hasModifier(SyntaxKind.PrivateKeyword)) {
      return { hasChanges: false, isMessagePattern: false, changes: [] };
    }

    const changes = [];
    let hasChanges = false;
    let isMessagePattern = false;

    // 检查是否为MessagePattern方法
    const messagePatternDecorator = method.getDecorator('MessagePattern');
    if (messagePatternDecorator) {
      isMessagePattern = true;
    }

    // 根据层级应用不同的转换策略
    switch (layer) {
      case 'repository':
        hasChanges = this.convertRepositoryMethod(method, changes);
        break;
      case 'service':
        hasChanges = this.convertServiceMethod(method, changes);
        break;
      case 'controller':
        hasChanges = this.convertControllerMethod(method, changes, isMessagePattern);
        break;
    }

    if (hasChanges && this.options.verbose) {
      console.log(`    ✅ 转换${layer}层方法: ${methodName}${isMessagePattern ? ' (MessagePattern)' : ''}`);
    }

    return { hasChanges, isMessagePattern, changes };
  }

  /**
   * 转换Repository层方法
   */
  convertRepositoryMethod(method, changes) {
    let hasChanges = false;

    // 1. 更新返回类型为Result<T>
    if (this.updateReturnTypeToResult(method)) {
      hasChanges = true;
      changes.push('返回类型更新为Result<T>');
    }

    // 2. 使用ExceptionToResultUtils.wrapAsync包装
    if (this.wrapMethodWithResultUtils(method)) {
      hasChanges = true;
      changes.push('使用ExceptionToResultUtils包装');
    }

    return hasChanges;
  }

  /**
   * 转换Service层方法
   */
  convertServiceMethod(method, changes) {
    let hasChanges = false;

    // 1. 更新返回类型为Result<T>
    if (this.updateReturnTypeToResult(method)) {
      hasChanges = true;
      changes.push('返回类型更新为Result<T>');
    }

    // 2. 转换方法体为Result传递模式
    if (this.convertServiceMethodBody(method)) {
      hasChanges = true;
      changes.push('转换为Result传递模式');
    }

    return hasChanges;
  }

  /**
   * 转换Controller层方法
   */
  convertControllerMethod(method, changes, isMessagePattern) {
    let hasChanges = false;

    if (isMessagePattern) {
      // 1. 更新返回类型为MicroserviceResponse
      if (this.updateReturnTypeToMicroserviceResponse(method)) {
        hasChanges = true;
        changes.push('返回类型更新为MicroserviceResponse');
      }

      // 2. 转换方法体为统一响应格式
      if (this.convertControllerMethodBody(method)) {
        hasChanges = true;
        changes.push('转换为统一响应格式');
      }
    }

    return hasChanges;
  }

  /**
   * 更新返回类型为Result<T>
   */
  updateReturnTypeToResult(method) {
    const returnType = method.getReturnTypeNode();

    if (!returnType) {
      return false;
    }

    const returnTypeText = returnType.getText();

    // 如果已经是Result类型，跳过
    if (returnTypeText.includes('Result<')) {
      return false;
    }

    // 转换Promise<T>为Promise<Result<T>>
    if (returnTypeText.startsWith('Promise<')) {
      const innerType = returnTypeText.slice(8, -1);
      method.setReturnType(`Promise<Result<${innerType}>>`);
      return true;
    }

    // 转换T为Result<T>
    if (!returnTypeText.includes('void')) {
      method.setReturnType(`Result<${returnTypeText}>`);
      return true;
    }

    return false;
  }

  /**
   * 更新返回类型为MicroserviceResponse
   */
  updateReturnTypeToMicroserviceResponse(method) {
    const returnType = method.getReturnTypeNode();

    if (!returnType) {
      // 如果没有显式返回类型，添加一个
      method.setReturnType('Promise<MicroserviceResponse>');
      return true;
    }

    const returnTypeText = returnType.getText();

    // 如果已经是MicroserviceResponse类型，跳过
    if (returnTypeText.includes('MicroserviceResponse')) {
      return false;
    }

    // 更新为MicroserviceResponse
    method.setReturnType('Promise<MicroserviceResponse>');
    return true;
  }

  /**
   * 使用ExceptionToResultUtils包装方法
   */
  wrapMethodWithResultUtils(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    const statements = body.getStatements();
    if (statements.length === 0) {
      return false;
    }

    // 检查是否已经使用了ExceptionToResultUtils
    const bodyText = body.getText();
    if (bodyText.includes('ExceptionToResultUtils.wrapAsync')) {
      return false;
    }

    // 查找try-catch块
    const tryStatements = body.getDescendantsOfKind(SyntaxKind.TryStatement);

    if (tryStatements.length > 0) {
      // 如果有try-catch，转换为ExceptionToResultUtils.wrapAsync
      const isAsync = method.hasModifier(SyntaxKind.AsyncKeyword);

      if (isAsync) {
        const tryBlock = tryStatements[0].getTryBlock();
        const tryContent = tryBlock.getStatements().map(stmt => stmt.getText()).join('\n      ');

        const newBody = `{
    return await ExceptionToResultUtils.wrapAsync(async () => {
      ${tryContent}
    });
  }`;

        method.setBodyText(newBody);
        this.stats.throwsConverted++;
        return true;
      }
    }

    return false;
  }

  /**
   * 转换Service层方法体为Result传递模式
   */
  convertServiceMethodBody(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    const bodyText = body.getText();

    // 如果已经使用了Result模式，跳过
    if (bodyText.includes('ResultUtils.') || bodyText.includes('Result<')) {
      return false;
    }

    // 重写整个方法体
    return this.rewriteServiceMethodBody(method);
  }

  /**
   * 重写Service层方法体 - 使用安全的AST操作
   */
  rewriteServiceMethodBody(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    // 查找try-catch块
    const tryStatements = body.getDescendantsOfKind(SyntaxKind.TryStatement);

    if (tryStatements.length === 0) {
      return false;
    }

    const tryStatement = tryStatements[0];
    const tryBlock = tryStatement.getTryBlock();

    // 使用代码写入器重新构建方法体
    method.setBodyText(writer => {
      this.writeServiceMethodBody(writer, tryBlock, method);
    });

    return true;
  }

  /**
   * 使用代码写入器安全地构建Service方法体
   */
  writeServiceMethodBody(writer, tryBlock, method) {
    const statements = tryBlock.getStatements();

    for (const statement of statements) {
      const statementText = statement.getText();

      // 处理Repository调用
      if (this.isRepositoryCall(statementText)) {
        this.writeRepositoryCallWithResultCheck(writer, statement);
      }
      // 处理throw语句
      else if (statement.getKind() === SyntaxKind.ThrowStatement) {
        this.writeThrowAsReturn(writer, statement);
      }
      // 处理return语句
      else if (statement.getKind() === SyntaxKind.ReturnStatement) {
        this.writeReturnAsResultSuccess(writer, statement);
      }
      // 其他语句直接写入
      else {
        writer.writeLine(statementText);
      }
    }
  }

  /**
   * 检查是否为Repository调用
   */
  isRepositoryCall(statementText) {
    return statementText.includes('await this.') &&
           statementText.includes('Repository.') &&
           statementText.startsWith('const ');
  }

  /**
   * 写入Repository调用和Result检查
   */
  writeRepositoryCallWithResultCheck(writer, statement) {
    const statementText = statement.getText();

    // 解析变量名和调用
    const match = statementText.match(/const\s+(\w+)\s*=\s*(await\s+this\.\w+Repository\.\w+\([^)]*\));/);

    if (match) {
      const [, varName, callExpression] = match;
      const resultVarName = `${varName}Result`;

      // 写入Repository调用
      writer.writeLine(`const ${resultVarName} = ${callExpression}`);
      writer.writeLine('');

      // 写入Result检查
      writer.writeLine(`if (!ResultUtils.isSuccess(${resultVarName})) {`);
      writer.writeLine(`  return ${resultVarName} as Result<any>;`);
      writer.writeLine('}');
      writer.writeLine('');

      // 写入数据提取
      writer.writeLine(`const ${varName} = ${resultVarName}.data;`);
    } else {
      // 如果解析失败，直接写入原语句
      writer.writeLine(statementText);
    }
  }

  /**
   * 将throw语句转换为return语句
   */
  writeThrowAsReturn(writer, throwStatement) {
    const expression = throwStatement.getExpression();
    const expressionText = expression.getText();

    // 解析异常类型和消息
    if (expressionText.includes('BadRequestException')) {
      const codeMatch = expressionText.match(/code:\s*ErrorCode\.(\w+)/);
      const messageMatch = expressionText.match(/message:\s*([^,}]+)/);

      if (codeMatch && messageMatch) {
        const errorCode = codeMatch[1];
        const errorMessage = messageMatch[1];
        writer.writeLine(`return ResultUtils.failure('${errorCode}', ${errorMessage});`);
      } else {
        writer.writeLine(`return ResultUtils.failure('INVALID_PARAMETER', '参数无效');`);
      }
    } else if (expressionText.includes('NotFoundException')) {
      writer.writeLine(`return ResultUtils.failure('RESOURCE_NOT_FOUND', '资源不存在');`);
    } else {
      writer.writeLine(`return ResultUtils.failure('UNKNOWN_ERROR', '操作失败');`);
    }
  }

  /**
   * 将return语句转换为ResultUtils.success
   */
  writeReturnAsResultSuccess(writer, returnStatement) {
    const expression = returnStatement.getExpression();

    if (expression) {
      const expressionText = expression.getText();
      writer.writeLine(`return ResultUtils.success(${expressionText});`);
    } else {
      writer.writeLine(`return ResultUtils.success(undefined);`);
    }
  }

  /**
   * 转换内容中的Repository调用
   */
  convertRepositoryCallsInContent(content) {
    const lines = content.split('\n');
    const newLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 匹配Repository调用模式
      const repositoryCallMatch = line.match(/(\s*)const\s+(\w+)\s*=\s*await\s+this\.(\w+Repository)\.(\w+)\(/);

      if (repositoryCallMatch) {
        const [, indent, varName, repoName, methodName] = repositoryCallMatch;

        // 替换为Result变量
        const newLine = line.replace(`const ${varName}`, `const ${varName}Result`);
        newLines.push(newLine);

        // 查找调用结束的行
        let callEndIndex = i;
        let openParens = 0;
        let foundEnd = false;

        for (let j = i; j < lines.length; j++) {
          const currentLine = lines[j];
          openParens += (currentLine.match(/\(/g) || []).length;
          openParens -= (currentLine.match(/\)/g) || []).length;

          if (openParens === 0 && currentLine.includes(');')) {
            callEndIndex = j;
            foundEnd = true;
            break;
          }

          if (j > i) {
            newLines.push(currentLine);
          }
        }

        if (foundEnd) {
          // 添加Result检查逻辑
          newLines.push('');
          newLines.push(`${indent}if (!ResultUtils.isSuccess(${varName}Result)) {`);
          newLines.push(`${indent}  return ${varName}Result as Result<any>;`);
          newLines.push(`${indent}}`);
          newLines.push('');

          // 添加数据提取
          newLines.push(`${indent}const ${varName} = ${varName}Result.data;`);

          // 跳过已处理的行
          i = callEndIndex;
        }
      } else {
        newLines.push(line);
      }
    }

    return newLines.join('\n');
  }

  /**
   * 转换内容中的throw语句
   */
  convertThrowStatementsInContent(content) {
    // 转换 throw new BadRequestException 模式
    content = content.replace(
      /throw new BadRequestException\(\s*{\s*code:\s*ErrorCode\.(\w+),\s*message:\s*([^}]+)\s*}\s*\);/g,
      "return ResultUtils.failure('$1', $2);"
    );

    // 转换 throw new NotFoundException 模式
    content = content.replace(
      /throw new NotFoundException\(\s*([^)]+)\s*\);/g,
      "return ResultUtils.failure('RESOURCE_NOT_FOUND', $1);"
    );

    // 转换其他throw语句
    content = content.replace(
      /throw\s+([^;]+);/g,
      "return ResultUtils.failure('UNKNOWN_ERROR', '操作失败');"
    );

    return content;
  }

  /**
   * 转换内容中的return语句
   */
  convertReturnStatementsInContent(content) {
    // 查找最后的return语句，转换为ResultUtils.success
    const lines = content.split('\n');

    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim();

      if (line.startsWith('return ') && !line.includes('ResultUtils.')) {
        const returnValue = line.replace(/^return\s+/, '').replace(/;$/, '');
        lines[i] = lines[i].replace(line, `return ResultUtils.success(${returnValue});`);
        break;
      }
    }

    return lines.join('\n');
  }

  /**
   * 转换Controller层方法体为统一响应格式
   */
  convertControllerMethodBody(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    const bodyText = body.getText();

    // 如果已经使用了MicroserviceResponseUtils，跳过
    if (bodyText.includes('MicroserviceResponseUtils.fromResult')) {
      return false;
    }

    // 重写Controller方法体
    return this.rewriteControllerMethodBody(method);
  }

  /**
   * 重写Controller层方法体
   */
  rewriteControllerMethodBody(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    const bodyText = body.getText();
    const statements = body.getStatements();

    // 查找Service调用和return语句
    let serviceCallLine = '';
    let logLine = '';
    let hasServiceCall = false;

    for (const statement of statements) {
      const statementText = statement.getText();

      // 查找日志语句
      if (statementText.includes('this.logger.log')) {
        logLine = statementText;
      }

      // 查找Service调用
      if (statementText.includes('await this.') && statementText.includes('Service.')) {
        // 转换Service调用为result变量
        const serviceCallMatch = statementText.match(/const\s+(\w+)\s*=\s*(await\s+this\.\w+Service\.\w+\([^)]*\));/);
        if (serviceCallMatch) {
          serviceCallLine = `const result = ${serviceCallMatch[2]};`;
          hasServiceCall = true;
        } else {
          // 如果没有赋值变量，添加result赋值
          const directCallMatch = statementText.match(/(await\s+this\.\w+Service\.\w+\([^)]*\));/);
          if (directCallMatch) {
            serviceCallLine = `const result = ${directCallMatch[1]}`;
            hasServiceCall = true;
          }
        }
      }
    }

    if (!hasServiceCall) {
      return false;
    }

    // 构建新的方法体
    const newBodyLines = [];

    if (logLine) {
      newBodyLines.push('    ' + logLine);
      newBodyLines.push('    ');
    }

    newBodyLines.push('    ' + serviceCallLine);
    newBodyLines.push('    ');
    newBodyLines.push('    return MicroserviceResponseUtils.fromResult(result, payload.injectedContext?.requestId);');

    const newBodyText = newBodyLines.join('\n');
    method.setBodyText(newBodyText);

    return true;
  }

  /**
   * 在return语句前添加result变量
   */
  addResultVariableBeforeReturn(method, returnStatement) {
    const body = method.getBody();
    const statements = body.getStatements();

    // 查找Service调用语句
    for (let i = statements.length - 1; i >= 0; i--) {
      const statement = statements[i];
      const statementText = statement.getText();

      // 查找await this.xxxService.xxx()调用
      if (statementText.includes('await this.') && statementText.includes('Service.')) {
        // 将Service调用结果赋值给result变量
        const serviceCallMatch = statementText.match(/const\s+(\w+)\s*=\s*await\s+this\.(\w+)\.(\w+)\(/);
        if (serviceCallMatch) {
          const [, varName] = serviceCallMatch;
          // 将变量名改为result
          statement.replaceWithText(statementText.replace(`const ${varName}`, 'const result'));
        } else {
          // 如果没有赋值，添加const result =
          const newStatement = statementText.replace(/await\s+this\./, 'const result = await this.');
          statement.replaceWithText(newStatement);
        }
        break;
      }
    }
  }

  /**
   * 转换try-catch语句
   */
  convertTryStatement(tryStatement) {
    const tryBlock = tryStatement.getTryBlock();
    const catchClause = tryStatement.getCatchClause();

    if (!catchClause) {
      return false;
    }

    // 使用ExceptionToResultUtils.wrapAsync包装
    const isAsync = tryStatement.getFirstAncestorByKind(SyntaxKind.MethodDeclaration)?.hasModifier(SyntaxKind.AsyncKeyword);

    if (isAsync) {
      const wrapperCode = `
        return await ExceptionToResultUtils.wrapAsync(async () => {
          ${tryBlock.getStatements().map(stmt => stmt.getText()).join('\n')}
        });
      `;

      tryStatement.replaceWithText(wrapperCode);
      this.stats.throwsConverted++;
      return true;
    }

    return false;
  }

  /**
   * 转换throw语句
   */
  convertThrowStatement(throwStatement) {
    const expression = throwStatement.getExpression();
    const expressionText = expression.getText();

    // 转换为return ResultUtils.failure
    let failureCode = 'UNKNOWN_ERROR';
    let failureMessage = '未知错误';

    // 尝试解析异常信息
    if (expressionText.includes('NotFoundException')) {
      failureCode = 'RESOURCE_NOT_FOUND';
      failureMessage = '资源不存在';
    } else if (expressionText.includes('BadRequestException')) {
      failureCode = 'INVALID_PARAMETER';
      failureMessage = '参数无效';
    }

    const returnCode = `return ResultUtils.failure('${failureCode}', '${failureMessage}');`;
    throwStatement.replaceWithText(returnCode);

    this.stats.throwsConverted++;
    return true;
  }

  /**
   * 创建备份文件
   */
  createBackup(filePath) {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.copyFileSync(filePath, backupPath);

    if (this.options.verbose) {
      console.log(`  💾 创建备份: ${backupPath}`);
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 转换统计:');
    console.log(`  📁 总处理文件数: ${this.stats.filesProcessed}`);
    console.log(`  🗄️  Repository层: ${this.stats.repositoryFiles}`);
    console.log(`  ⚙️  Service层: ${this.stats.serviceFiles}`);
    console.log(`  🎮 Controller层: ${this.stats.controllerFiles}`);
    console.log(`  🔄 转换throw语句: ${this.stats.throwsConverted}`);
    console.log(`  🔧 更新函数: ${this.stats.functionsUpdated}`);
    console.log(`  📡 MessagePattern更新: ${this.stats.messagePatternUpdated}`);
    console.log(`  📦 添加导入: ${this.stats.importsAdded}`);

    if (this.stats.errors.length > 0) {
      console.log(`  ❌ 错误数量: ${this.stats.errors.length}`);
    }

    if (this.options.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
    }

    // 生成详细报告
    if (this.options.generateReport && !this.options.dryRun) {
      this.generateDetailedReport();
    }

    // 生成回滚脚本
    if (this.options.generateRollback && !this.options.dryRun) {
      this.generateRollbackScript();
    }
  }

  /**
   * 生成详细转换报告
   */
  generateDetailedReport() {
    const reportPath = `conversion-report-${Date.now()}.json`;
    const report = {
      timestamp: new Date().toISOString(),
      stats: this.stats,
      conversions: this.conversionReport,
      errors: this.stats.errors
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📋 详细报告已生成: ${reportPath}`);
  }

  /**
   * 生成回滚脚本
   */
  generateRollbackScript() {
    const rollbackPath = `rollback-${Date.now()}.sh`;
    const rollbackContent = [
      '#!/bin/bash',
      '# 自动生成的回滚脚本',
      `# 生成时间: ${new Date().toISOString()}`,
      '',
      'echo "开始回滚Result模式转换..."',
      '',
      ...this.rollbackCommands,
      '',
      'echo "回滚完成！"'
    ].join('\n');

    fs.writeFileSync(rollbackPath, rollbackContent);
    fs.chmodSync(rollbackPath, '755');
    console.log(`\n🔄 回滚脚本已生成: ${rollbackPath}`);
  }

  /**
   * 转换语句为Result模式
   */
  convertStatementToResultMode(statement) {
    // 这是一个占位方法，实际实现会更复杂
    return false;
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  let targetDir = null;

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose' || arg === '-v') {
      options.verbose = true;
    } else if (arg === '--no-backup') {
      options.backup = false;
    } else if (!targetDir) {
      targetDir = arg;
    }
  }

  if (!targetDir) {
    console.log('用法: node exception-to-result-converter.js <目录路径> [选项]');
    console.log('选项:');
    console.log('  --dry-run     预览模式，不实际修改文件');
    console.log('  --verbose     详细输出');
    console.log('  --no-backup   不创建备份文件');
    process.exit(1);
  }

  const converter = new ExceptionToResultConverter(options);
  converter.convertDirectory(targetDir).catch(console.error);
}

module.exports = ExceptionToResultConverter;
