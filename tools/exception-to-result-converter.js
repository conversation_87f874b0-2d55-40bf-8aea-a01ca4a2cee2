#!/usr/bin/env node

/**
 * 异常转Result模式自动转换工具
 *
 * 功能：
 * 1. 扫描指定目录下的TypeScript文件
 * 2. 识别throw语句和try-catch块
 * 3. 自动转换为Result模式
 * 4. 更新函数返回类型
 * 5. 添加必要的import语句
 */

const fs = require('fs');
const path = require('path');
const { Project, SyntaxKind } = require('ts-morph');

class ExceptionToResultConverter {
  constructor(options = {}) {
    this.options = {
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      backup: options.backup !== false, // 默认创建备份
      ...options
    };

    this.project = new Project({
      compilerOptions: {
        target: 'ES2020',
        module: 'CommonJS',
        strict: false,
        skipLibCheck: true,
        allowJs: true,
      },
    });

    this.stats = {
      filesProcessed: 0,
      throwsConverted: 0,
      functionsUpdated: 0,
      importsAdded: 0
    };
  }

  /**
   * 转换指定目录
   */
  async convertDirectory(dirPath) {
    console.log(`🔍 开始扫描目录: ${dirPath}`);

    const files = this.findTypeScriptFiles(dirPath);
    console.log(`📁 找到 ${files.length} 个TypeScript文件`);

    for (const filePath of files) {
      await this.convertFile(filePath);
    }

    this.printStats();
  }

  /**
   * 转换单个文件
   */
  async convertFile(filePath) {
    try {
      if (this.options.verbose) {
        console.log(`📄 处理文件: ${filePath}`);
      }

      // 创建备份
      if (this.options.backup && !this.options.dryRun) {
        this.createBackup(filePath);
      }

      const sourceFile = this.project.addSourceFileAtPath(filePath);
      let hasChanges = false;

      // 1. 添加必要的import
      if (this.addRequiredImports(sourceFile)) {
        hasChanges = true;
        this.stats.importsAdded++;
      }

      // 2. 转换Repository和Service类
      if (this.convertClassMethods(sourceFile)) {
        hasChanges = true;
      }

      // 3. 保存文件
      if (hasChanges && !this.options.dryRun) {
        await sourceFile.save();
      }

      this.stats.filesProcessed++;

    } catch (error) {
      console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    }
  }

  /**
   * 查找TypeScript文件
   */
  findTypeScriptFiles(dirPath) {
    const files = [];

    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(fullPath);
        } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
          // 只处理Repository和Service文件
          if (item.includes('.repository.') || item.includes('.service.')) {
            files.push(fullPath);
          }
        }
      }
    };

    scanDir(dirPath);
    return files;
  }

  /**
   * 添加必要的import语句
   */
  addRequiredImports(sourceFile) {
    const imports = sourceFile.getImportDeclarations();
    const hasResultImport = imports.some(imp =>
      imp.getModuleSpecifierValue().includes('result.type') ||
      imp.getNamedImports().some(named =>
        ['Result', 'ResultUtils', 'ExceptionToResultUtils'].includes(named.getName())
      )
    );

    if (!hasResultImport) {
      sourceFile.addImportDeclaration({
        moduleSpecifier: '@libs/common/types/result.type',
        namedImports: ['Result', 'ResultUtils', 'ExceptionToResultUtils']
      });

      if (this.options.verbose) {
        console.log(`  ✅ 添加Result类型导入`);
      }

      return true;
    }

    return false;
  }

  /**
   * 转换类方法
   */
  convertClassMethods(sourceFile) {
    let hasChanges = false;
    const classes = sourceFile.getClasses();

    for (const classDeclaration of classes) {
      const className = classDeclaration.getName();

      // 只处理Repository和Service类
      if (!className || (!className.includes('Repository') && !className.includes('Service'))) {
        continue;
      }

      if (this.options.verbose) {
        console.log(`  🔧 处理类: ${className}`);
      }

      const methods = classDeclaration.getMethods();

      for (const method of methods) {
        if (this.convertMethod(method)) {
          hasChanges = true;
          this.stats.functionsUpdated++;
        }
      }
    }

    return hasChanges;
  }

  /**
   * 转换单个方法
   */
  convertMethod(method) {
    const methodName = method.getName();

    // 跳过构造函数和私有方法
    if (methodName === 'constructor' || method.hasModifier(SyntaxKind.PrivateKeyword)) {
      return false;
    }

    let hasChanges = false;

    // 1. 更新返回类型
    if (this.updateReturnType(method)) {
      hasChanges = true;
    }

    // 2. 转换方法体
    if (this.convertMethodBody(method)) {
      hasChanges = true;
    }

    if (hasChanges && this.options.verbose) {
      console.log(`    ✅ 转换方法: ${methodName}`);
    }

    return hasChanges;
  }

  /**
   * 更新返回类型
   */
  updateReturnType(method) {
    const returnType = method.getReturnTypeNode();

    if (!returnType) {
      return false;
    }

    const returnTypeText = returnType.getText();

    // 如果已经是Result类型，跳过
    if (returnTypeText.includes('Result<')) {
      return false;
    }

    // 转换Promise<T>为Promise<Result<T>>
    if (returnTypeText.startsWith('Promise<')) {
      const innerType = returnTypeText.slice(8, -1); // 移除Promise<>
      method.setReturnType(`Promise<Result<${innerType}>>`);
      return true;
    }

    // 转换T为Result<T>
    if (!returnTypeText.includes('void')) {
      method.setReturnType(`Result<${returnTypeText}>`);
      return true;
    }

    return false;
  }

  /**
   * 转换方法体
   */
  convertMethodBody(method) {
    const body = method.getBody();
    if (!body) {
      return false;
    }

    let hasChanges = false;

    // 查找try-catch块
    const tryStatements = body.getDescendantsOfKind(SyntaxKind.TryStatement);

    for (const tryStatement of tryStatements) {
      if (this.convertTryStatement(tryStatement)) {
        hasChanges = true;
      }
    }

    // 查找直接的throw语句
    const throwStatements = body.getDescendantsOfKind(SyntaxKind.ThrowStatement);

    for (const throwStatement of throwStatements) {
      if (this.convertThrowStatement(throwStatement)) {
        hasChanges = true;
      }
    }

    return hasChanges;
  }

  /**
   * 转换try-catch语句
   */
  convertTryStatement(tryStatement) {
    const tryBlock = tryStatement.getTryBlock();
    const catchClause = tryStatement.getCatchClause();

    if (!catchClause) {
      return false;
    }

    // 使用ExceptionToResultUtils.wrapAsync包装
    const isAsync = tryStatement.getFirstAncestorByKind(SyntaxKind.MethodDeclaration)?.hasModifier(SyntaxKind.AsyncKeyword);

    if (isAsync) {
      const wrapperCode = `
        return await ExceptionToResultUtils.wrapAsync(async () => {
          ${tryBlock.getStatements().map(stmt => stmt.getText()).join('\n')}
        });
      `;

      tryStatement.replaceWithText(wrapperCode);
      this.stats.throwsConverted++;
      return true;
    }

    return false;
  }

  /**
   * 转换throw语句
   */
  convertThrowStatement(throwStatement) {
    const expression = throwStatement.getExpression();
    const expressionText = expression.getText();

    // 转换为return ResultUtils.failure
    let failureCode = 'UNKNOWN_ERROR';
    let failureMessage = '未知错误';

    // 尝试解析异常信息
    if (expressionText.includes('NotFoundException')) {
      failureCode = 'RESOURCE_NOT_FOUND';
      failureMessage = '资源不存在';
    } else if (expressionText.includes('BadRequestException')) {
      failureCode = 'INVALID_PARAMETER';
      failureMessage = '参数无效';
    }

    const returnCode = `return ResultUtils.failure('${failureCode}', '${failureMessage}');`;
    throwStatement.replaceWithText(returnCode);

    this.stats.throwsConverted++;
    return true;
  }

  /**
   * 创建备份文件
   */
  createBackup(filePath) {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.copyFileSync(filePath, backupPath);

    if (this.options.verbose) {
      console.log(`  💾 创建备份: ${backupPath}`);
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 转换统计:');
    console.log(`  📁 处理文件数: ${this.stats.filesProcessed}`);
    console.log(`  🔄 转换throw语句: ${this.stats.throwsConverted}`);
    console.log(`  🔧 更新函数: ${this.stats.functionsUpdated}`);
    console.log(`  📦 添加导入: ${this.stats.importsAdded}`);

    if (this.options.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  let targetDir = null;

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose' || arg === '-v') {
      options.verbose = true;
    } else if (arg === '--no-backup') {
      options.backup = false;
    } else if (!targetDir) {
      targetDir = arg;
    }
  }

  if (!targetDir) {
    console.log('用法: node exception-to-result-converter.js <目录路径> [选项]');
    console.log('选项:');
    console.log('  --dry-run     预览模式，不实际修改文件');
    console.log('  --verbose     详细输出');
    console.log('  --no-backup   不创建备份文件');
    process.exit(1);
  }

  const converter = new ExceptionToResultConverter(options);
  converter.convertDirectory(targetDir).catch(console.error);
}

module.exports = ExceptionToResultConverter;
