#!/usr/bin/env node

/**
 * 为MessagePattern方法添加返回类型
 *
 * 功能：
 * 1. 扫描指定目录下所有controller文件
 * 2. 找到@MessagePattern装饰的方法
 * 3. 分析当前返回的data类型
 * 4. 添加Promise<MicroserviceResponse<T>>返回类型
 * 5. 添加必要的import语句
 * 6. 默认忽略apps/auth和apps/gateway目录
 *
 * 使用方法：
 * node tools/add-messagepattern-return-types.js [选项]
 *
 * 选项：
 * --dry-run              预览模式，不实际修改文件
 * --target=path          指定扫描路径（默认：apps）
 * --ignore=dir1,dir2     指定忽略的目录（默认：apps/auth,apps/gateway）
 * --ignore=none          不忽略任何目录
 * --verbose              详细输出
 *
 * 示例：
 * node tools/add-messagepattern-return-types.js --dry-run --verbose
 * node tools/add-messagepattern-return-types.js --target=apps/character
 * node tools/add-messagepattern-return-types.js --ignore=apps/auth,apps/gateway,apps/match
 */

const fs = require('fs');
const path = require('path');

class MessagePatternReturnTypeMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.targetPath = options.targetPath || 'apps';
    this.ignoreDirs = options.ignoreDirs || ['apps/auth', 'apps/gateway'];
    this.verbose = options.verbose || false;
    this.stats = {
      filesScanned: 0,
      filesModified: 0,
      methodsModified: 0,
      dirsIgnored: 0,
      errors: []
    };
  }

  /**
   * 执行迁移
   */
  async migrate() {
    console.log('🚀 开始MessagePattern返回类型迁移...');
    console.log(`📂 扫描目录: ${this.targetPath}`);
    console.log(`🚫 忽略目录: ${this.ignoreDirs.join(', ')}`);
    console.log('');

    await this.scanDirectory(this.targetPath);
    this.printStats();
  }

  /**
   * 扫描目录
   */
  async scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.error(`❌ 目录不存在: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 检查是否应该忽略此目录
        if (this.shouldIgnoreDirectory(fullPath)) {
          this.stats.dirsIgnored++;
          if (this.verbose) {
            console.log(`🚫 忽略目录: ${fullPath}`);
          }
          continue;
        }

        await this.scanDirectory(fullPath);
      } else if (stat.isFile() && this.isControllerFile(item)) {
        await this.processFile(fullPath);
      }
    }
  }

  /**
   * 检查是否应该忽略目录
   */
  shouldIgnoreDirectory(dirPath) {
    const normalizedPath = path.normalize(dirPath).replace(/\\/g, '/');
    return this.ignoreDirs.some(ignoreDir => {
      const normalizedIgnoreDir = path.normalize(ignoreDir).replace(/\\/g, '/');
      return normalizedPath.includes(normalizedIgnoreDir);
    });
  }

  /**
   * 检查是否为controller文件
   */
  isControllerFile(filename) {
    return filename.endsWith('.controller.ts');
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      this.stats.filesScanned++;
      console.log(`📄 扫描文件: ${filePath}`);

      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.modifyFileContent(content, filePath);

      if (content !== modifiedContent) {
        this.stats.filesModified++;
        
        if (this.dryRun) {
          console.log(`   ✏️ [DRY RUN] 文件将被修改`);
          if (this.verbose) {
            this.showDiff(content, modifiedContent);
          }
        } else {
          fs.writeFileSync(filePath, modifiedContent, 'utf8');
          console.log(`   ✅ 文件已修改`);
        }
      } else {
        console.log(`   ℹ️ 文件无需修改`);
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`   ❌ 处理文件失败: ${error.message}`);
    }
  }

  /**
   * 修改文件内容
   */
  modifyFileContent(content, filePath) {
    let modified = content;
    let hasModifications = false;

    // 1. 添加import语句（如果需要）
    if (this.needsImport(content)) {
      modified = this.addImportStatement(modified);
      hasModifications = true;
    }

    // 2. 修改@MessagePattern方法的返回类型
    const methodMatches = this.findMessagePatternMethods(modified);
    
    for (const match of methodMatches) {
      const modifiedMethod = this.addReturnType(match);
      if (modifiedMethod !== match.fullMatch) {
        modified = modified.replace(match.fullMatch, modifiedMethod);
        this.stats.methodsModified++;
        hasModifications = true;
        console.log(`     🔧 修改方法: ${match.methodName} -> ${match.returnType}`);
      }
    }

    return modified;
  }

  /**
   * 检查是否需要添加import
   */
  needsImport(content) {
    return !content.includes('MicroserviceResponse') && 
           content.includes('@MessagePattern');
  }

  /**
   * 添加import语句
   */
  addImportStatement(content) {
    // 找到最后一个import语句的位置
    const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?\s*\n/g;
    let lastImportMatch;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      lastImportMatch = match;
    }

    if (lastImportMatch) {
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      const importStatement = "import { MicroserviceResponse } from '@libs/common/types/result.type';\n";
      
      return content.slice(0, insertPosition) + 
             importStatement + 
             content.slice(insertPosition);
    } else {
      // 如果没有找到import语句，在文件开头添加
      return "import { MicroserviceResponse } from '@libs/common/types/result.type';\n" + content;
    }
  }

  /**
   * 查找所有@MessagePattern方法
   */
  findMessagePatternMethods(content) {
    const methods = [];

    if (this.verbose) {
      console.log('     🔍 开始查找MessagePattern方法...');
    }

    // 先测试简单的MessagePattern匹配
    const simplePatternRegex = /@MessagePattern/g;
    const simpleMatches = content.match(simplePatternRegex);

    if (this.verbose) {
      console.log(`     🔍 简单匹配找到 ${simpleMatches ? simpleMatches.length : 0} 个@MessagePattern`);
    }

    // 更精确的正则表达式，支持多行装饰器
    const messagePatternRegex = /@MessagePattern\([^)]+\)[\s\S]*?async\s+(\w+)\s*\([^)]*\)\s*(?::\s*Promise<[^>]+>)?\s*{/g;

    let match;
    while ((match = messagePatternRegex.exec(content)) !== null) {
      const methodName = match[1];
      const fullMatch = match[0];

      if (this.verbose) {
        console.log(`     📍 找到方法: ${methodName}`);
      }

      // 分析返回类型
      const returnType = this.analyzeReturnType(content, match.index, methodName);

      methods.push({
        methodName,
        fullMatch,
        index: match.index,
        returnType,
        hasReturnType: fullMatch.includes('Promise<')
      });
    }

    if (this.verbose) {
      console.log(`     📊 总共找到 ${methods.length} 个MessagePattern方法`);
    }

    return methods;
  }

  /**
   * 分析方法的返回类型
   */
  analyzeReturnType(content, methodStartIndex, methodName) {
    // 查找方法体
    const methodStart = content.indexOf('{', methodStartIndex);
    if (methodStart === -1) return 'any';

    // 找到方法体的结束位置
    let braceCount = 1;
    let methodEnd = methodStart + 1;
    
    while (methodEnd < content.length && braceCount > 0) {
      if (content[methodEnd] === '{') braceCount++;
      else if (content[methodEnd] === '}') braceCount--;
      methodEnd++;
    }

    const methodBody = content.slice(methodStart, methodEnd);
    
    // 分析return语句中的data类型
    return this.extractDataType(methodBody, methodName);
  }

  /**
   * 从方法体中提取data类型
   */
  extractDataType(methodBody, methodName) {
    // 查找return语句中的data字段
    const returnDataRegex = /return\s*{\s*[^}]*data:\s*([^,}]+)/g;
    const match = returnDataRegex.exec(methodBody);
    
    if (match) {
      const dataValue = match[1].trim();
      
      // 如果data是变量，尝试推断类型
      if (dataValue === 'result') {
        // 查找result变量的来源
        const resultRegex = /const\s+result\s*=\s*await\s+this\.\w+Service\.(\w+)/;
        const serviceMatch = resultRegex.exec(methodBody);
        
        if (serviceMatch) {
          const serviceMethod = serviceMatch[1];
          return this.inferTypeFromServiceMethod(serviceMethod, methodName);
        }
      }
      
      // 如果data是直接的对象或数组
      if (dataValue.startsWith('{') || dataValue.startsWith('[')) {
        return 'any';
      }
      
      // 如果data是其他变量
      return this.inferTypeFromVariable(dataValue, methodName);
    }
    
    // 默认返回any
    return 'any';
  }

  /**
   * 根据Service方法推断返回类型
   */
  inferTypeFromServiceMethod(serviceMethod, controllerMethod) {
    // 基于方法名推断类型的映射表
    const typeMapping = {
      // Character相关
      'createCharacter': 'CharacterDocument',
      'loginCharacter': 'LoginResultDto',
      'updateCharacter': 'CharacterDocument',
      'getCharacterList': 'CharacterInfoDto[]',
      'getCharacterInfo': 'CharacterInfoDto',
      'levelUp': 'LevelUpResultDto',
      'buyEnergy': 'BuyEnergyResultDto',
      
      // Formation相关
      'getFormations': 'FormationDocument[]',
      'createFormation': 'FormationDocument',
      'updateFormation': 'FormationDocument',
      'deleteFormation': 'boolean',
      'setActiveFormation': 'FormationDocument',
      
      // Inventory相关
      'getInventory': 'InventoryDocument[]',
      'addItem': 'InventoryDocument',
      'removeItem': 'boolean',
      'useItem': 'any',
      
      // Tactic相关
      'getTactics': 'TacticDocument[]',
      'createTactic': 'TacticDocument',
      'updateTactic': 'TacticDocument',
      'deleteTactic': 'boolean'
    };
    
    return typeMapping[serviceMethod] || 'any';
  }

  /**
   * 根据变量名推断类型
   */
  inferTypeFromVariable(variableName, methodName) {
    // 基于变量名推断类型
    const variableTypeMapping = {
      'character': 'CharacterDocument',
      'characters': 'CharacterDocument[]',
      'formation': 'FormationDocument',
      'formations': 'FormationDocument[]',
      'inventory': 'InventoryDocument[]',
      'tactic': 'TacticDocument',
      'tactics': 'TacticDocument[]',
      'result': 'any',
      'data': 'any'
    };
    
    return variableTypeMapping[variableName] || 'any';
  }

  /**
   * 为方法添加返回类型
   */
  addReturnType(methodMatch) {
    if (methodMatch.hasReturnType) {
      // 如果已经有返回类型，跳过
      return methodMatch.fullMatch;
    }

    // 在方法签名中添加返回类型
    const returnTypeAnnotation = `: Promise<MicroserviceResponse<${methodMatch.returnType}>>`;
    
    // 查找方法参数的结束位置
    const parenIndex = methodMatch.fullMatch.lastIndexOf(')');
    
    if (parenIndex !== -1) {
      return methodMatch.fullMatch.slice(0, parenIndex + 1) + 
             returnTypeAnnotation + 
             methodMatch.fullMatch.slice(parenIndex + 1);
    }
    
    return methodMatch.fullMatch;
  }

  /**
   * 显示差异
   */
  showDiff(original, modified) {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    console.log('   📋 变更预览:');
    
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine !== modifiedLine) {
        if (originalLine) {
          console.log(`   - ${originalLine}`);
        }
        if (modifiedLine) {
          console.log(`   + ${modifiedLine}`);
        }
      }
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 迁移统计:');
    console.log(`  📁 扫描文件数: ${this.stats.filesScanned}`);
    console.log(`  ✏️ 修改文件数: ${this.stats.filesModified}`);
    console.log(`  🔧 修改方法数: ${this.stats.methodsModified}`);
    console.log(`  🚫 忽略目录数: ${this.stats.dirsIgnored}`);
    
    if (this.stats.errors.length > 0) {
      console.log(`  ❌ 错误数量: ${this.stats.errors.length}`);
      this.stats.errors.forEach(error => {
        console.log(`     ${error.file}: ${error.error}`);
      });
    }
    
    if (this.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
      console.log('   移除 --dry-run 参数来执行实际修改');
    } else {
      console.log('\n✅ 迁移完成！');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose') {
      options.verbose = true;
    } else if (arg.startsWith('--target=')) {
      options.targetPath = arg.split('=')[1];
    } else if (arg.startsWith('--ignore=')) {
      const ignoreDirs = arg.split('=')[1];
      if (ignoreDirs === 'none') {
        options.ignoreDirs = [];
      } else {
        options.ignoreDirs = ignoreDirs.split(',');
      }
    }
  }
  
  const migrator = new MessagePatternReturnTypeMigrator(options);
  migrator.migrate().catch(console.error);
}

module.exports = MessagePatternReturnTypeMigrator;
