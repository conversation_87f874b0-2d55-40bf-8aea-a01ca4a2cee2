#!/usr/bin/env node

/**
 * 接口生成脚本
 * 从JSON配置文件生成TypeScript接口定义
 *
 * 功能：
 * - 自动分析JSON结构生成接口
 * - 字段名驼峰化（ItemID→itemId）
 * - 项目命名规范（footballer→hero, association→guild）
 * - 生成字段映射和元数据
 */

const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

// 设置工作目录为项目根目录
process.chdir(path.resolve(__dirname, '../..'));

const InterfaceGenerator = require('./libs/interface-generator');

async function main() {
  console.log(chalk.blue.bold('🚀 接口生成工具'));
  console.log(chalk.gray('自动生成TypeScript配置接口\n'));

  const spinner = ora('正在生成接口...').start();

  try {
    const generator = new InterfaceGenerator();
    
    // 执行生成
    const result = await generator.generateAll();
    
    if (result.success) {
      spinner.succeed('接口生成完成！');
      
      console.log(chalk.green(`\n✅ 生成成功: ${result.report.generatedInterfaces.length}/${result.report.totalFiles} 个接口`));
      console.log(chalk.cyan(`📊 总字段数: ${result.report.totalFields} 个`));
      
      // 显示生成的接口列表
      if (result.report.generatedInterfaces.length > 0) {
        console.log(chalk.blue('\n📋 生成的接口:'));
        result.report.generatedInterfaces.forEach(({ tableName, fieldsCount, isEmpty }) => {
          const status = isEmpty ? chalk.yellow('(空)') : chalk.green(`(${fieldsCount} 字段)`);
          console.log(`  ${chalk.green('✓')} ${tableName}Definition ${status}`);
        });
      }
      
      // 显示错误信息
      if (result.report.errors.length > 0) {
        console.log(chalk.yellow('\n⚠️  部分文件生成失败:'));
        result.report.errors.forEach(error => {
          console.log(`  ${chalk.red('✗')} ${error.file}: ${error.error}`);
        });
      }
      
    } else {
      spinner.fail('接口生成失败');
      console.log(chalk.red(`❌ 错误: ${result.error || result.message}`));
      process.exit(1);
    }
    
  } catch (error) {
    spinner.fail('生成过程中发生错误');
    console.error(chalk.red('❌ 错误详情:'), error.message);
    console.error(chalk.gray(error.stack));
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的Promise拒绝:'), reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = main;
