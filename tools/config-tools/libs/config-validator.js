/**
 * 配置验证工具
 * 验证配置文件的格式、数据完整性和业务规则
 * 使用项目根目录的node_modules
 */

const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

class ConfigValidator {
  constructor() {
    this.configDir = path.resolve('libs/game-config/src/data');
    
    // 验证规则配置
    this.validationRules = {
      // 通用规则
      common: {
        requiredFields: ['id'],
        uniqueFields: ['id'],
        nonEmptyFields: ['name'],
        positiveFields: ['id'],
        maxArrayLength: 1000
      },
      
      // 特定表的规则
      specific: {
        'Footballer': {
          requiredFields: ['heroId', 'heroName'],
          uniqueFields: ['heroId'],
          nonEmptyFields: ['heroName'],
          positiveFields: ['heroId'],
          rangeFields: {
            'position': { min: 1, max: 11 },
            'overall': { min: 1, max: 100 }
          }
        },
        'Item': {
          requiredFields: ['itemId', 'itemName'],
          uniqueFields: ['itemId'],
          nonEmptyFields: ['itemName'],
          positiveFields: ['itemId'],
          rangeFields: {
            'quality': { min: 1, max: 5 },
            'level': { min: 1, max: 100 }
          }
        },
        'Skill': {
          requiredFields: ['skillId', 'skillName'],
          uniqueFields: ['skillId'],
          nonEmptyFields: ['skillName'],
          positiveFields: ['skillId'],
          rangeFields: {
            'level': { min: 1, max: 10 },
            'cooldown': { min: 0, max: 3600 }
          }
        }
      }
    };
  }

  /**
   * 验证所有配置文件
   */
  async validateAll() {
    console.log(chalk.blue('🚀 开始验证配置文件...'));
    
    try {
      // 查找所有JSON配置文件 (使用相对路径)
      const pattern = 'libs/game-config/src/data/*.json';
      const files = glob.sync(pattern);
      
      if (files.length === 0) {
        console.log(chalk.yellow('⚠️  未找到配置文件，请先运行迁移工具'));
        return { success: false, message: '未找到配置文件' };
      }
      
      const report = {
        totalFiles: files.length,
        validFiles: 0,
        invalidFiles: [],
        totalRecords: 0,
        totalErrors: 0,
        totalWarnings: 0,
        validationResults: []
      };
      
      // 逐个验证文件
      for (const file of files) {
        try {
          const tableName = path.basename(file, '.json');
          console.log(chalk.gray(`验证: ${tableName}`));
          
          const result = await this.validateConfig(file, tableName);
          report.validationResults.push(result);
          report.totalRecords += result.recordCount;
          report.totalErrors += result.errors.length;
          report.totalWarnings += result.warnings.length;
          
          if (result.isValid) {
            report.validFiles++;
          } else {
            report.invalidFiles.push({
              file: tableName,
              errors: result.errors.length,
              warnings: result.warnings.length
            });
          }
          
        } catch (error) {
          report.invalidFiles.push({
            file: path.basename(file),
            error: error.message
          });
          console.log(chalk.red(`❌ ${path.basename(file)}: ${error.message}`));
        }
      }
      
      // 输出报告
      this.printValidationReport(report);
      
      return {
        success: report.invalidFiles.length === 0,
        report
      };
      
    } catch (error) {
      console.log(chalk.red('❌ 验证过程中发生错误:'), error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 验证单个配置文件
   */
  async validateConfig(jsonFile, tableName) {
    // 读取JSON数据
    const jsonData = await fs.readJson(jsonFile);
    
    const result = {
      tableName,
      recordCount: 0,
      isValid: true,
      errors: [],
      warnings: [],
      stats: {}
    };
    
    // 基础格式验证
    if (!Array.isArray(jsonData)) {
      result.errors.push('配置文件格式错误，期望数组格式');
      result.isValid = false;
      return result;
    }
    
    result.recordCount = jsonData.length;
    
    if (jsonData.length === 0) {
      result.warnings.push('配置文件为空');
      return result;
    }
    
    // 获取验证规则
    const rules = this.getValidationRules(tableName);
    
    // 数据验证
    this.validateDataStructure(jsonData, rules, result);
    this.validateDataIntegrity(jsonData, rules, result);
    this.validateBusinessRules(jsonData, rules, result);
    
    // 生成统计信息
    result.stats = this.generateStats(jsonData);
    
    // 输出验证结果
    this.printConfigValidationResult(result);
    
    return result;
  }

  /**
   * 获取验证规则
   */
  getValidationRules(tableName) {
    const commonRules = this.validationRules.common;
    const specificRules = this.validationRules.specific[tableName] || {};
    
    return {
      requiredFields: [...(commonRules.requiredFields || []), ...(specificRules.requiredFields || [])],
      uniqueFields: [...(commonRules.uniqueFields || []), ...(specificRules.uniqueFields || [])],
      nonEmptyFields: [...(commonRules.nonEmptyFields || []), ...(specificRules.nonEmptyFields || [])],
      positiveFields: [...(commonRules.positiveFields || []), ...(specificRules.positiveFields || [])],
      rangeFields: { ...(commonRules.rangeFields || {}), ...(specificRules.rangeFields || {}) },
      maxArrayLength: specificRules.maxArrayLength || commonRules.maxArrayLength
    };
  }

  /**
   * 验证数据结构
   */
  validateDataStructure(data, rules, result) {
    const sample = data[0];
    const sampleFields = Object.keys(sample);
    
    // 检查必需字段
    for (const field of rules.requiredFields) {
      if (!sampleFields.includes(field)) {
        result.errors.push(`缺少必需字段: ${field}`);
        result.isValid = false;
      }
    }
    
    // 检查字段一致性
    const inconsistentRecords = [];
    for (let i = 1; i < data.length; i++) {
      const currentFields = Object.keys(data[i]);
      if (currentFields.length !== sampleFields.length) {
        inconsistentRecords.push(i);
      }
    }
    
    if (inconsistentRecords.length > 0) {
      result.warnings.push(`${inconsistentRecords.length} 条记录的字段数量不一致`);
    }
  }

  /**
   * 验证数据完整性
   */
  validateDataIntegrity(data, rules, result) {
    // 检查唯一性
    for (const field of rules.uniqueFields) {
      const values = data.map(item => item[field]).filter(v => v != null);
      const uniqueValues = new Set(values);
      
      if (values.length !== uniqueValues.size) {
        result.errors.push(`字段 ${field} 存在重复值`);
        result.isValid = false;
      }
    }
    
    // 检查非空字段
    for (const field of rules.nonEmptyFields) {
      const emptyCount = data.filter(item => 
        !item[field] || item[field] === '' || item[field] === null
      ).length;
      
      if (emptyCount > 0) {
        result.warnings.push(`字段 ${field} 有 ${emptyCount} 条记录为空`);
      }
    }
    
    // 检查正数字段
    for (const field of rules.positiveFields) {
      const negativeCount = data.filter(item => 
        typeof item[field] === 'number' && item[field] <= 0
      ).length;
      
      if (negativeCount > 0) {
        result.errors.push(`字段 ${field} 有 ${negativeCount} 条记录不是正数`);
        result.isValid = false;
      }
    }
    
    // 检查范围字段
    for (const [field, range] of Object.entries(rules.rangeFields)) {
      const outOfRangeCount = data.filter(item => {
        const value = item[field];
        return typeof value === 'number' && (value < range.min || value > range.max);
      }).length;
      
      if (outOfRangeCount > 0) {
        result.errors.push(`字段 ${field} 有 ${outOfRangeCount} 条记录超出范围 [${range.min}, ${range.max}]`);
        result.isValid = false;
      }
    }
  }

  /**
   * 验证业务规则
   */
  validateBusinessRules(data, rules, result) {
    // 检查数组长度
    if (data.length > rules.maxArrayLength) {
      result.warnings.push(`记录数量 ${data.length} 超过建议最大值 ${rules.maxArrayLength}`);
    }
    
    // 检查数据类型一致性
    const sample = data[0];
    for (const [field, sampleValue] of Object.entries(sample)) {
      const sampleType = typeof sampleValue;
      const inconsistentTypes = data.filter(item => 
        item[field] != null && typeof item[field] !== sampleType
      ).length;
      
      if (inconsistentTypes > 0) {
        result.warnings.push(`字段 ${field} 有 ${inconsistentTypes} 条记录类型不一致`);
      }
    }
    
    // 检查关联字段（如果存在）
    this.validateRelationships(data, result);
  }

  /**
   * 验证关联关系
   */
  validateRelationships(data, result) {
    // 检查常见的关联字段
    const relationshipFields = [
      { parent: 'parentId', child: 'id' },
      { parent: 'categoryId', child: 'id' },
      { parent: 'typeId', child: 'id' }
    ];
    
    for (const relation of relationshipFields) {
      if (data[0][relation.parent] !== undefined) {
        const parentIds = data.map(item => item[relation.parent]).filter(id => id != null);
        const childIds = data.map(item => item[relation.child]).filter(id => id != null);
        const childIdSet = new Set(childIds);
        
        const orphanCount = parentIds.filter(id => !childIdSet.has(id)).length;
        if (orphanCount > 0) {
          result.warnings.push(`有 ${orphanCount} 个 ${relation.parent} 引用了不存在的记录`);
        }
      }
    }
  }

  /**
   * 生成统计信息
   */
  generateStats(data) {
    const stats = {
      recordCount: data.length,
      fieldCount: Object.keys(data[0] || {}).length,
      fieldTypes: {},
      nullCounts: {},
      uniqueCounts: {}
    };
    
    if (data.length === 0) return stats;
    
    const sample = data[0];
    
    // 统计字段类型
    for (const [field, value] of Object.entries(sample)) {
      stats.fieldTypes[field] = typeof value;
      
      // 统计空值数量
      stats.nullCounts[field] = data.filter(item => 
        item[field] == null || item[field] === ''
      ).length;
      
      // 统计唯一值数量
      const uniqueValues = new Set(data.map(item => item[field]));
      stats.uniqueCounts[field] = uniqueValues.size;
    }
    
    return stats;
  }

  /**
   * 打印单个配置验证结果
   */
  printConfigValidationResult(result) {
    if (result.isValid && result.warnings.length === 0) {
      console.log(chalk.green(`✅ ${result.tableName} (${result.recordCount} 条记录)`));
    } else if (result.isValid) {
      console.log(chalk.yellow(`⚠️  ${result.tableName} (${result.recordCount} 条记录, ${result.warnings.length} 个警告)`));
    } else {
      console.log(chalk.red(`❌ ${result.tableName} (${result.recordCount} 条记录, ${result.errors.length} 个错误)`));
    }
  }

  /**
   * 打印验证报告
   */
  printValidationReport(report) {
    console.log('\n' + chalk.blue('📊 配置验证报告'));
    console.log(chalk.gray('='.repeat(50)));
    
    console.log(`总文件数: ${chalk.cyan(report.totalFiles)}`);
    console.log(`验证通过: ${chalk.green(report.validFiles)}`);
    console.log(`验证失败: ${chalk.red(report.invalidFiles.length)}`);
    console.log(`总记录数: ${chalk.cyan(report.totalRecords)}`);
    console.log(`总错误数: ${chalk.red(report.totalErrors)}`);
    console.log(`总警告数: ${chalk.yellow(report.totalWarnings)}`);
    
    if (report.validationResults.length > 0) {
      console.log(`\n${chalk.blue('详细结果:')}`);
      report.validationResults.forEach(result => {
        const status = result.isValid ? 
          (result.warnings.length > 0 ? chalk.yellow('⚠️ ') : chalk.green('✅')) : 
          chalk.red('❌');
        
        console.log(`  ${status} ${result.tableName}: ${result.recordCount} 条记录`);
        
        if (result.errors.length > 0) {
          result.errors.forEach(error => {
            console.log(`    ${chalk.red('错误:')} ${error}`);
          });
        }
        
        if (result.warnings.length > 0) {
          result.warnings.forEach(warning => {
            console.log(`    ${chalk.yellow('警告:')} ${warning}`);
          });
        }
      });
    }
    
    console.log(chalk.gray('='.repeat(50)));
    
    if (report.totalErrors === 0) {
      if (report.totalWarnings === 0) {
        console.log(chalk.green('🎉 所有配置文件验证通过！'));
      } else {
        console.log(chalk.yellow('✅ 验证通过，但有警告信息需要注意'));
      }
    } else {
      console.log(chalk.red('❌ 验证失败，请修复错误后重新验证'));
    }
  }
}

module.exports = ConfigValidator;
