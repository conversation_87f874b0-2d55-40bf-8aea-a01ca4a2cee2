# MongoDB用户管理脚本 V3.0 使用指南

## 🎯 **设计理念**

基于您的深刻洞察，我们采用了**简化架构**：

1. **每个服务实例有独立的.env.database配置**
2. **配置文件直接包含完整URI**（包含正确的用户名）
3. **mongodb.config.ts只负责动态生成数据库名**
4. **一个强大的create-mongodb-users.js脚本解决所有需求**

## 📋 **核心优势**

### **vs 之前的复杂方案**
- ❌ 删除了冗余的mongodb-user-manager-v2.js
- ❌ 删除了不必要的动态用户名拼接
- ✅ 保留了一个功能强大的脚本
- ✅ 配置文件直接可用，无需额外处理

### **架构清晰度**
```
每个实例的配置文件 → 包含完整URI → 直接使用
                                ↓
                        只需动态生成数据库名
```

## 🛠️ **使用方法**

### **基础使用**

```bash
# 1. 默认创建（3个区服，7个服务）
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --file tools/create-mongodb-users.js

# 2. 指定区服
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVER_IDS=['server_001','server_002'];" --file tools/create-mongodb-users.js

# 3. 指定服务
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVICES=['character','hero'];" --file tools/create-mongodb-users.js
```

### **高级功能**

```bash
# 4. 测试模式（不实际创建）
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var DRY_RUN=true;" --file tools/create-mongodb-users.js

# 5. 自定义密码
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var DEFAULT_PASSWORD='NewPassword123';" --file tools/create-mongodb-users.js

# 6. 静默模式
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var VERBOSE=false;" --file tools/create-mongodb-users.js

# 7. 组合使用
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVER_IDS=['server_001']; var SERVICES=['character','hero']; var DRY_RUN=true;" --file tools/create-mongodb-users.js
```

## 📊 **实际执行示例**

### **创建单个区服的用户**

```bash
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVER_IDS=['server_001'];" --file tools/create-mongodb-users.js
```

**输出示例**：
```
🚀 MongoDB用户创建脚本 V3.0 启动
============================================================
📊 执行配置:
   - 执行模式: 正常模式
   - 区服列表: server_001 (1个)
   - 服务列表: auth,character,hero,economy,social,activity,match (7个)
   - 默认密码: FslcxeE2...
   - 详细输出: 开启
   - 预计用户数: 8 (管理员:1 + 全局:1 + 区服:6)

✅ 管理员用户创建成功
📋 创建全局服务用户...
✅ 认证服务（全局）用户创建成功: auth-admin -> auth_db

📋 创建区服服务用户...
✅ 角色服务（区服）用户创建成功: character-admin-server_001 -> character_db_server_001
✅ 球员服务（区服）用户创建成功: hero-admin-server_001 -> hero_db_server_001
✅ 经济服务（区服）用户创建成功: economy-admin-server_001 -> economy_db_server_001
✅ 社交服务（区服）用户创建成功: social-admin-server_001 -> social_db_server_001
✅ 活动服务（区服）用户创建成功: activity-admin-server_001 -> activity_db_server_001
✅ 比赛服务（区服）用户创建成功: match-admin-server_001 -> match_db_server_001

============================================================
📊 执行结果统计报告
============================================================
执行时间: 2.34 秒
总用户数: 8
创建成功: 8 个
已存在: 0 个
创建失败: 0 个
成功率: 100%

🎉 所有用户创建完成！权限隔离配置成功！

🔗 生成的连接字符串:
============================================================

# AUTH 服务:
AUTH_MONGODB_URI=******************************************************

# CHARACTER 服务:
# server_001 实例:
CHARACTER_MONGODB_URI=**********************************************************************

# HERO 服务:
# server_001 实例:
HERO_MONGODB_URI=*****************************************************************

# ... 其他服务类似

📖 使用说明:
1. 将上述连接字符串复制到对应服务的 .env.database 文件中
2. 每个区服实例使用对应的连接字符串
3. 设置 SERVER_ID 环境变量来指定区服（如：SERVER_ID=server_001）
4. 认证服务使用全局连接字符串，不受 SERVER_ID 影响
```

## 🔧 **配置文件使用**

### **正确的.env.database配置**

```bash
# apps/character/.env.database（server_001实例）
CHARACTER_MONGODB_URI=**********************************************************************
CHARACTER_MONGODB_MAX_POOL_SIZE=15
# ... 其他配置

# apps/character/.env.database（server_002实例）
CHARACTER_MONGODB_URI=**********************************************************************
CHARACTER_MONGODB_MAX_POOL_SIZE=15
# ... 其他配置
```

### **mongodb.config.ts的简化逻辑**

```typescript
export const createMongoConfig = (configService: ConfigService, serviceName: string) => {
  const serverId = configService.get<string>('SERVER_ID');
  
  // 直接使用完整URI（包含正确的用户名）
  const uri = configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_URI`);
  
  // 只需要动态生成数据库名
  const dbName = generateDatabaseName(serviceName, serverId);
  
  return { uri, dbName, ...options };
};
```

## 🎯 **最佳实践**

### **1. 测试先行**
```bash
# 先用测试模式验证配置
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var DRY_RUN=true;" --file tools/create-mongodb-users.js
```

### **2. 分批创建**
```bash
# 先创建认证服务
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVICES=['auth'];" --file tools/create-mongodb-users.js

# 再创建业务服务
mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVICES=['character','hero'];" --file tools/create-mongodb-users.js
```

### **3. 权限验证**
```bash
# 使用脚本生成的验证命令测试权限隔离
mongosh "**********************************************************************************************" --eval "db.test.find()"
```

## ✅ **总结**

通过这次优化，我们实现了：

1. **架构简化**：删除了冗余脚本，统一使用一个强大的工具
2. **配置清晰**：每个实例的配置文件直接可用，无需额外处理
3. **功能丰富**：支持测试模式、自定义参数、详细报告等
4. **易于使用**：一条命令解决所有需求

这个方案完美解决了您提出的两个核心问题，既保持了功能的强大性，又大大简化了使用复杂度。

# MongoDB 7.0 管理员用户创建问题解决方案

您遇到的问题是在副本集环境下尝试创建用户时出现的 `NotWritablePrimary` 错误。以下是完整的解决方案：

## 问题分析

1. **MongoDB 7.0 默认管理员用户**：
    - 全新安装的 MongoDB 7.0 **没有默认管理员用户**
    - 首次启动时允许本地无认证访问

2. **错误原因**：
   ```bash
   MongoServerError[NotWritablePrimary]: not primary
   ```
    - 您连接的节点不是副本集的 PRIMARY 节点
    - 用户管理操作必须在 PRIMARY 节点上执行

## 解决方案

### 方法一：在 PRIMARY 节点上创建用户

1. **确认副本集状态**：
   ```javascript
   rs.status()
   ```

   如果未初始化，执行初始化命令：
```javascript
    // mongodb安装完毕无管理员账户时使用 mongosh --host *************** --port 27017
    
    rs.initiate({
      _id: "rs0",
      version: 1,
      members: [
        { _id: 0, host: "***************:27017" }
      ]
    })
```
   查找 `"stateStr": "PRIMARY"` 的节点

2. **连接到 PRIMARY 节点**：
   ```bash
   mongo --host <primary-node-ip> --port 27017
   ```

3. **创建管理员用户**：
   ```javascript
   use admin
   db.createUser({ 
     user: "admin", 
     pwd: "password", 
     roles: [{ role: "root", db: "admin" }] 
   })
   ```

### 方法二：单节点模式创建用户（开发环境）

1. **临时转换为单节点模式**：
   ```bash
   # 停止 MongoDB
   sudo systemctl stop mongod
   
   # 以单节点模式启动
   mongod --port 27017 --dbpath /var/lib/mongodb --noauth --nounixsocket
   ```

2. **创建用户**：
   ```bash
   mongo
   use admin
   db.createUser({ 
     user: "admin", 
     pwd: "password", 
     roles: [{ role: "root", db: "admin" }] 
   })
   ```

3. **重启为副本集模式**：
   ```bash
   # 停止单节点实例
   use admin
   db.shutdownServer()
   
   # 以副本集模式启动
   sudo systemctl start mongod
   ```

### 方法三：强制重新配置副本集

1. **强制重新选举 PRIMARY**：
   ```javascript
   // 在任意节点执行
   cfg = rs.conf()
   cfg.members[0].priority = 10
   rs.reconfig(cfg, { force: true })
   ```

2. **等待选举完成**：
   ```javascript
   rs.status() // 检查新PRIMARY
   ```

3. **在新PRIMARY上创建用户**

## 生产环境最佳实践

1. **初始化脚本示例**：
   ```javascript
   // init-replica-set.js
   rs.initiate({
     _id: "rs0",
     members: [
       { _id: 0, host: "mongo1:27017", priority: 2 },
       { _id: 1, host: "mongo2:27017", priority: 1 },
       { _id: 2, host: "mongo3:27017", arbiterOnly: true }
     ]
   })
   
   // 等待选举完成
   sleep(5000)
   
   use admin
   db.createUser({
     user: "clusterAdmin",
     pwd: "securePassword",
     roles: ["clusterAdmin"]
   })
   ```

2. **执行脚本**：
   ```bash
   mongo --host mongo1:27017 init-replica-set.js
   ```

## 验证用户创建

```bash
# 使用新用户连接验证
mongo -u admin -p password --authenticationDatabase admin
```

## 常见问题排查

1. **防火墙问题**：
   ```bash
   # 检查节点间通信
   telnet mongo2 27017
   ```

2. **认证配置**：
   ```yaml
   # /etc/mongod.conf
   security:
     authorization: enabled
     keyFile: /path/to/keyfile  # 副本集必须
   ```

3. **日志检查**：
   ```bash
   tail -f /var/log/mongodb/mongod.log | grep -i auth
   ```

通过以上步骤，您应该能够成功创建管理员用户并解决 `NotWritablePrimary` 错误。