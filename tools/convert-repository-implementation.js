#!/usr/bin/env node

/**
 * Repository层实现逻辑转换工具
 * 
 * 功能：
 * 1. 自动将Repository方法体包装为RepositoryResultWrapper.wrap()
 * 2. 智能识别不同类型的数据库操作
 * 3. 保持原有逻辑不变，只添加Result包装
 * 4. 处理try-catch块的转换
 * 
 * 使用方法：
 * node tools/convert-repository-implementation.js [选项]
 * 
 * 选项：
 * --dry-run              预览模式，不实际修改文件
 * --target=path          指定文件路径
 * --verbose              详细输出
 * 
 * 示例：
 * node tools/convert-repository-implementation.js --target=apps/character/src/common/repositories/character.repository.ts --dry-run
 */

const fs = require('fs');
const path = require('path');

class RepositoryImplementationConverter {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.targetPath = options.targetPath;
    this.verbose = options.verbose || false;
    this.stats = {
      methodsConverted: 0,
      errors: []
    };
  }

  /**
   * 执行转换
   */
  async convert() {
    if (!this.targetPath) {
      console.error('❌ 请指定目标文件路径 --target=path');
      return;
    }

    console.log('🚀 开始Repository实现逻辑转换...');
    console.log(`📂 目标文件: ${this.targetPath}`);
    console.log('');

    await this.processFile(this.targetPath);
    this.printStats();
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      console.log(`📄 处理文件: ${filePath}`);

      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.convertFileContent(content);

      if (content !== modifiedContent) {
        if (this.dryRun) {
          console.log(`   ✏️ [DRY RUN] 文件将被修改`);
          if (this.verbose) {
            this.showDiff(content, modifiedContent);
          }
        } else {
          fs.writeFileSync(filePath, modifiedContent, 'utf8');
          console.log(`   ✅ 文件已修改`);
        }
      } else {
        console.log(`   ℹ️ 文件无需修改`);
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`   ❌ 处理文件失败: ${error.message}`);
    }
  }

  /**
   * 转换文件内容
   */
  convertFileContent(content) {
    let modified = content;

    // 1. 添加import语句（如果需要）
    if (!content.includes('RepositoryResultWrapper')) {
      modified = this.addImportStatement(modified);
    }

    // 2. 转换方法实现
    modified = this.convertMethodImplementations(modified);

    return modified;
  }

  /**
   * 添加import语句
   */
  addImportStatement(content) {
    // 查找Result相关的import
    const resultImportRegex = /import\s*{[^}]*Result[^}]*}\s*from\s*['"][^'"]*result\.type['"];?/;
    const match = content.match(resultImportRegex);

    if (match) {
      // 如果已有Result import，添加RepositoryResultWrapper
      const existingImport = match[0];
      if (!existingImport.includes('RepositoryResultWrapper')) {
        const newImport = existingImport.replace(
          /}\s*from/,
          ', RepositoryResultWrapper } from'
        );
        return content.replace(existingImport, newImport);
      }
    } else {
      // 添加新的import
      const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?\s*\n/g;
      let lastImportMatch;
      let match;
      
      while ((match = importRegex.exec(content)) !== null) {
        lastImportMatch = match;
      }

      if (lastImportMatch) {
        const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
        const importStatement = "import { Result, RepositoryResultWrapper } from '@libs/common/types/result.type';\n";
        
        return content.slice(0, insertPosition) + 
               importStatement + 
               content.slice(insertPosition);
      }
    }

    return content;
  }

  /**
   * 转换方法实现
   */
  convertMethodImplementations(content) {
    let modified = content;

    // 匹配async方法
    const methodRegex = /async\s+(\w+)\s*\([^)]*\)\s*:\s*Promise<Result<[^>]+>>\s*{([\s\S]*?)(?=\n\s*(?:async\s+\w+|}\s*$|\/\*\*|\n\s*$))/g;

    let match;
    while ((match = methodRegex.exec(content)) !== null) {
      const methodName = match[1];
      const methodBody = match[2];
      const fullMatch = match[0];

      if (this.verbose) {
        console.log(`     🔧 转换方法: ${methodName}`);
      }

      // 检查是否已经使用了Result包装
      if (methodBody.includes('RepositoryResultWrapper') || methodBody.includes('ResultUtils')) {
        continue; // 已经转换过，跳过
      }

      const convertedMethod = this.convertSingleMethod(methodName, methodBody, fullMatch);
      if (convertedMethod !== fullMatch) {
        modified = modified.replace(fullMatch, convertedMethod);
        this.stats.methodsConverted++;
      }
    }

    return modified;
  }

  /**
   * 转换单个方法
   */
  convertSingleMethod(methodName, methodBody, fullMatch) {
    // 检查方法体结构
    const hasTryCatch = methodBody.includes('try') && methodBody.includes('catch');
    
    if (hasTryCatch) {
      return this.convertTryCatchMethod(methodName, methodBody, fullMatch);
    } else {
      return this.convertSimpleMethod(methodName, methodBody, fullMatch);
    }
  }

  /**
   * 转换包含try-catch的方法
   */
  convertTryCatchMethod(methodName, methodBody, fullMatch) {
    // 提取try块中的内容
    const tryMatch = methodBody.match(/try\s*{([\s\S]*?)}\s*catch/);
    if (!tryMatch) return fullMatch;

    const tryContent = tryMatch[1].trim();
    
    // 确定包装器类型
    const wrapperMethod = this.getWrapperMethod(methodName, tryContent);
    
    // 构建新的方法体
    const newMethodBody = `{
    return await RepositoryResultWrapper.${wrapperMethod}(async () => {
${this.indentCode(tryContent, 6)}
    });
  }`;

    return fullMatch.replace(methodBody, newMethodBody);
  }

  /**
   * 转换简单方法（无try-catch）
   */
  convertSimpleMethod(methodName, methodBody, fullMatch) {
    const cleanBody = methodBody.trim().replace(/^{\s*/, '').replace(/\s*}$/, '');
    
    // 确定包装器类型
    const wrapperMethod = this.getWrapperMethod(methodName, cleanBody);
    
    // 构建新的方法体
    const newMethodBody = `{
    return await RepositoryResultWrapper.${wrapperMethod}(async () => {
${this.indentCode(cleanBody, 6)}
    });
  }`;

    return fullMatch.replace(methodBody, newMethodBody);
  }

  /**
   * 根据方法名和内容确定包装器方法
   */
  getWrapperMethod(methodName, content) {
    // 分析返回类型
    if (methodName.includes('count') || methodName.includes('Count')) {
      return 'wrapCount';
    }
    
    if (methodName.includes('exists') || methodName.includes('has')) {
      return 'wrapBoolean';
    }
    
    if (methodName.startsWith('find') && (methodName.includes('All') || methodName.includes('List') || content.includes('.find('))) {
      return 'wrapArray';
    }
    
    if (methodName.startsWith('find') || content.includes('findOne') || content.includes('findById')) {
      return 'wrapNullable';
    }
    
    // 默认使用通用包装器
    return 'wrap';
  }

  /**
   * 代码缩进
   */
  indentCode(code, spaces) {
    const indent = ' '.repeat(spaces);
    return code.split('\n').map(line => {
      return line.trim() ? indent + line : line;
    }).join('\n');
  }

  /**
   * 显示差异
   */
  showDiff(original, modified) {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    console.log('   📋 变更预览:');
    
    let diffCount = 0;
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length) && diffCount < 20; i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine !== modifiedLine) {
        if (originalLine) {
          console.log(`   - ${originalLine}`);
        }
        if (modifiedLine) {
          console.log(`   + ${modifiedLine}`);
        }
        diffCount++;
      }
    }
    
    if (diffCount >= 20) {
      console.log('   ... (更多变更)');
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 转换统计:');
    console.log(`  🔧 转换方法数: ${this.stats.methodsConverted}`);
    
    if (this.stats.errors.length > 0) {
      console.log(`  ❌ 错误数量: ${this.stats.errors.length}`);
      this.stats.errors.forEach(error => {
        console.log(`     ${error.file}: ${error.error}`);
      });
    }
    
    if (this.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
      console.log('   移除 --dry-run 参数来执行实际修改');
    } else {
      console.log('\n✅ 转换完成！');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose') {
      options.verbose = true;
    } else if (arg.startsWith('--target=')) {
      options.targetPath = arg.split('=')[1];
    }
  }
  
  const converter = new RepositoryImplementationConverter(options);
  converter.convert().catch(console.error);
}

module.exports = RepositoryImplementationConverter;
