#!/usr/bin/env node

/**
 * 为Repository层方法添加Result返回类型
 *
 * 功能：
 * 1. 扫描指定目录下所有repository文件
 * 2. 找到Repository类的方法
 * 3. 分析当前返回的数据类型
 * 4. 添加Promise<Result<T>>返回类型
 * 5. 添加必要的import语句
 * 6. 默认忽略apps/auth和apps/gateway目录
 *
 * 使用方法：
 * node tools/add-repository-result-types.js [选项]
 *
 * 选项：
 * --dry-run              预览模式，不实际修改文件
 * --target=path          指定扫描路径（默认：apps）
 * --ignore=dir1,dir2     指定忽略的目录（默认：apps/auth,apps/gateway）
 * --ignore=none          不忽略任何目录
 * --verbose              详细输出
 *
 * 示例：
 * node tools/add-repository-result-types.js --dry-run --verbose
 * node tools/add-repository-result-types.js --target=apps/character
 * node tools/add-repository-result-types.js --ignore=apps/auth,apps/gateway,apps/match
 */

const fs = require('fs');
const path = require('path');

class RepositoryResultTypeMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.targetPath = options.targetPath || 'apps';
    this.ignoreDirs = options.ignoreDirs || ['apps/auth', 'apps/gateway'];
    this.verbose = options.verbose || false;
    this.stats = {
      filesScanned: 0,
      filesModified: 0,
      methodsModified: 0,
      dirsIgnored: 0,
      errors: []
    };
  }

  /**
   * 执行迁移
   */
  async migrate() {
    console.log('🚀 开始Repository层Result类型迁移...');
    console.log(`📂 扫描目录: ${this.targetPath}`);
    console.log(`🚫 忽略目录: ${this.ignoreDirs.join(', ')}`);
    console.log('');

    await this.scanDirectory(this.targetPath);
    this.printStats();
  }

  /**
   * 扫描目录
   */
  async scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.error(`❌ 目录不存在: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 检查是否应该忽略此目录
        if (this.shouldIgnoreDirectory(fullPath)) {
          this.stats.dirsIgnored++;
          if (this.verbose) {
            console.log(`🚫 忽略目录: ${fullPath}`);
          }
          continue;
        }

        await this.scanDirectory(fullPath);
      } else if (stat.isFile() && this.isRepositoryFile(item)) {
        await this.processFile(fullPath);
      }
    }
  }

  /**
   * 检查是否应该忽略目录
   */
  shouldIgnoreDirectory(dirPath) {
    const normalizedPath = path.normalize(dirPath).replace(/\\/g, '/');
    return this.ignoreDirs.some(ignoreDir => {
      const normalizedIgnoreDir = path.normalize(ignoreDir).replace(/\\/g, '/');
      return normalizedPath.includes(normalizedIgnoreDir);
    });
  }

  /**
   * 检查是否为repository文件
   */
  isRepositoryFile(filename) {
    return filename.endsWith('.repository.ts');
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      this.stats.filesScanned++;
      console.log(`📄 扫描文件: ${filePath}`);

      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.modifyFileContent(content, filePath);

      if (content !== modifiedContent) {
        this.stats.filesModified++;
        
        if (this.dryRun) {
          console.log(`   ✏️ [DRY RUN] 文件将被修改`);
          if (this.verbose) {
            this.showDiff(content, modifiedContent);
          }
        } else {
          fs.writeFileSync(filePath, modifiedContent, 'utf8');
          console.log(`   ✅ 文件已修改`);
        }
      } else {
        console.log(`   ℹ️ 文件无需修改`);
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`   ❌ 处理文件失败: ${error.message}`);
    }
  }

  /**
   * 修改文件内容
   */
  modifyFileContent(content, filePath) {
    let modified = content;
    let hasModifications = false;

    // 1. 添加import语句（如果需要）
    if (this.needsImport(content)) {
      modified = this.addImportStatement(modified);
      hasModifications = true;
    }

    // 2. 修改Repository方法的返回类型
    const methodMatches = this.findRepositoryMethods(modified);
    
    for (const match of methodMatches) {
      const modifiedMethod = this.addReturnType(match);
      if (modifiedMethod !== match.fullMatch) {
        modified = modified.replace(match.fullMatch, modifiedMethod);
        this.stats.methodsModified++;
        hasModifications = true;
        console.log(`     🔧 修改方法: ${match.methodName} -> ${match.returnType}`);
      }
    }

    return modified;
  }

  /**
   * 检查是否需要添加import
   */
  needsImport(content) {
    return !content.includes('Result') && 
           (content.includes('Repository') || content.includes('async'));
  }

  /**
   * 添加import语句
   */
  addImportStatement(content) {
    // 找到最后一个import语句的位置
    const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?\s*\n/g;
    let lastImportMatch;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      lastImportMatch = match;
    }

    if (lastImportMatch) {
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      const importStatement = "import { Result, ResultUtils, ExceptionToResultUtils } from '@libs/common/types/result.type';\n";
      
      return content.slice(0, insertPosition) + 
             importStatement + 
             content.slice(insertPosition);
    } else {
      // 如果没有找到import语句，在文件开头添加
      return "import { Result, ResultUtils, ExceptionToResultUtils } from '@libs/common/types/result.type';\n" + content;
    }
  }

  /**
   * 查找所有Repository方法
   */
  findRepositoryMethods(content) {
    const methods = [];
    
    if (this.verbose) {
      console.log('     🔍 开始查找Repository方法...');
    }
    
    // 匹配Repository类中的async方法
    const methodRegex = /async\s+(\w+)\s*\([^)]*\)\s*(?::\s*Promise<[^>]+>)?\s*{/g;
    
    let match;
    while ((match = methodRegex.exec(content)) !== null) {
      const methodName = match[1];
      const fullMatch = match[0];
      
      // 跳过构造函数和私有方法
      if (methodName === 'constructor' || methodName.startsWith('_')) {
        continue;
      }
      
      if (this.verbose) {
        console.log(`     📍 找到方法: ${methodName}`);
      }
      
      // 分析返回类型
      const returnType = this.analyzeReturnType(content, match.index, methodName);
      
      methods.push({
        methodName,
        fullMatch,
        index: match.index,
        returnType,
        hasReturnType: fullMatch.includes('Promise<Result<') || fullMatch.includes(': Result<')
      });
    }

    if (this.verbose) {
      console.log(`     📊 总共找到 ${methods.length} 个Repository方法`);
    }

    return methods;
  }

  /**
   * 分析方法的返回类型
   */
  analyzeReturnType(content, methodStartIndex, methodName) {
    // 基于Repository方法名推断类型
    return this.inferTypeFromRepositoryMethod(methodName);
  }

  /**
   * 根据Repository方法推断返回类型
   */
  inferTypeFromRepositoryMethod(methodName) {
    // 基于方法名推断类型的映射表
    const typeMapping = {
      // 创建操作
      'create': 'CharacterDocument',
      'save': 'CharacterDocument',
      'insert': 'CharacterDocument',
      
      // 查询操作
      'findById': 'CharacterDocument | null',
      'findByCharacterId': 'CharacterDocument | null',
      'findByUserId': 'CharacterDocument[]',
      'findByName': 'CharacterDocument | null',
      'findByOpenId': 'CharacterDocument | null',
      'findOne': 'CharacterDocument | null',
      'find': 'CharacterDocument[]',
      'findAll': 'CharacterDocument[]',
      'findWithPagination': 'PaginationResult<CharacterDocument>',
      'findByLevelRange': 'CharacterDocument[]',
      
      // 更新操作
      'update': 'CharacterDocument',
      'updateById': 'CharacterDocument',
      'updateOne': 'CharacterDocument',
      'updateMany': 'number',
      'bulkUpdate': 'number',
      
      // 删除操作
      'delete': 'boolean',
      'deleteById': 'boolean',
      'deleteOne': 'boolean',
      'deleteMany': 'number',
      'softDelete': 'CharacterDocument',
      'remove': 'boolean',
      
      // 存在性检查
      'exists': 'boolean',
      'existsById': 'boolean',
      'existsByName': 'boolean',
      
      // 计数操作
      'count': 'number',
      'countDocuments': 'number',
      'getCount': 'number',
      'getOnlineCount': 'number',
      'getServerStats': 'any'
    };
    
    // 精确匹配
    if (typeMapping[methodName]) {
      return typeMapping[methodName];
    }
    
    // 模糊匹配
    if (methodName.startsWith('find')) {
      if (methodName.includes('ById') || methodName.includes('One')) {
        return 'CharacterDocument | null';
      } else {
        return 'CharacterDocument[]';
      }
    }
    
    if (methodName.startsWith('create') || methodName.startsWith('save')) {
      return 'CharacterDocument';
    }
    
    if (methodName.startsWith('update')) {
      return 'CharacterDocument';
    }
    
    if (methodName.startsWith('delete') || methodName.startsWith('remove')) {
      return 'boolean';
    }
    
    if (methodName.startsWith('exists') || methodName.startsWith('has')) {
      return 'boolean';
    }
    
    if (methodName.startsWith('count') || methodName.startsWith('get') && methodName.includes('Count')) {
      return 'number';
    }
    
    // 默认返回any
    return 'any';
  }

  /**
   * 为方法添加返回类型
   */
  addReturnType(methodMatch) {
    if (methodMatch.hasReturnType) {
      // 如果已经有Result返回类型，跳过
      return methodMatch.fullMatch;
    }

    // 在方法签名中添加返回类型
    const returnTypeAnnotation = `: Promise<Result<${methodMatch.returnType}>>`;
    
    // 查找方法参数的结束位置
    const parenIndex = methodMatch.fullMatch.lastIndexOf(')');
    
    if (parenIndex !== -1) {
      return methodMatch.fullMatch.slice(0, parenIndex + 1) + 
             returnTypeAnnotation + 
             methodMatch.fullMatch.slice(parenIndex + 1);
    }
    
    return methodMatch.fullMatch;
  }

  /**
   * 显示差异
   */
  showDiff(original, modified) {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    console.log('   📋 变更预览:');
    
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine !== modifiedLine) {
        if (originalLine) {
          console.log(`   - ${originalLine}`);
        }
        if (modifiedLine) {
          console.log(`   + ${modifiedLine}`);
        }
      }
    }
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log('\n📊 迁移统计:');
    console.log(`  📁 扫描文件数: ${this.stats.filesScanned}`);
    console.log(`  ✏️ 修改文件数: ${this.stats.filesModified}`);
    console.log(`  🔧 修改方法数: ${this.stats.methodsModified}`);
    console.log(`  🚫 忽略目录数: ${this.stats.dirsIgnored}`);
    
    if (this.stats.errors.length > 0) {
      console.log(`  ❌ 错误数量: ${this.stats.errors.length}`);
      this.stats.errors.forEach(error => {
        console.log(`     ${error.file}: ${error.error}`);
      });
    }
    
    if (this.dryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改文件');
      console.log('   移除 --dry-run 参数来执行实际修改');
    } else {
      console.log('\n✅ 迁移完成！');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--verbose') {
      options.verbose = true;
    } else if (arg.startsWith('--target=')) {
      options.targetPath = arg.split('=')[1];
    } else if (arg.startsWith('--ignore=')) {
      const ignoreDirs = arg.split('=')[1];
      if (ignoreDirs === 'none') {
        options.ignoreDirs = [];
      } else {
        options.ignoreDirs = ignoreDirs.split(',');
      }
    }
  }
  
  const migrator = new RepositoryResultTypeMigrator(options);
  migrator.migrate().catch(console.error);
}

module.exports = RepositoryResultTypeMigrator;
