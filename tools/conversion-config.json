{"conversionRules": {"repository": {"description": "Repository层：数据访问层，直接包装异常为Result", "returnTypePattern": "Promise<Result<{originalType}>>", "wrapperMethod": "ExceptionToResultUtils.wrapAsync", "imports": ["Result", "ResultUtils", "ExceptionToResultUtils"], "excludeMethods": ["constructor"], "includePatterns": ["*.repository.ts", "*Repository.ts"]}, "service": {"description": "Service层：业务逻辑层，Result传递模式", "returnTypePattern": "Promise<Result<{originalType}>>", "conversionMode": "result-chain", "imports": ["Result", "ResultUtils"], "excludeMethods": ["constructor"], "includePatterns": ["*.service.ts", "*Service.ts"], "resultChainPatterns": [{"pattern": "await this.{repositoryName}.{methodName}(", "replacement": "const {varName}Result = await this.{repositoryName}.{methodName}(\n    if (!ResultUtils.isSuccess({varName}Result)) {\n      return {varName}Result as Result<{returnType}>;\n    }"}]}, "controller": {"description": "Controller层：接口层，统一响应格式", "returnTypePattern": "Promise<MicroserviceResponse>", "conversionMode": "microservice-response", "imports": ["MicroserviceResponse", "MicroserviceResponseUtils", "ResultUtils"], "excludeMethods": ["constructor"], "includePatterns": ["*.controller.ts", "*Controller.ts"], "messagePatternOnly": true, "responsePattern": "MicroserviceResponseUtils.fromResult(result, payload.injectedContext?.requestId)"}}, "errorCodeMapping": {"NotFoundException": "RESOURCE_NOT_FOUND", "BadRequestException": "INVALID_PARAMETER", "UnauthorizedException": "PERMISSION_DENIED", "ForbiddenException": "PERMISSION_DENIED", "ConflictException": "RESOURCE_CONFLICT", "InternalServerErrorException": "UNKNOWN_ERROR", "MongoError": {"11000": "RESOURCE_ALREADY_EXISTS", "121": "DOCUMENT_VALIDATION_ERROR", "default": "DATABASE_ERROR"}}, "importPaths": {"Result": "@libs/common/types/result.type", "ResultUtils": "@libs/common/types/result.type", "ExceptionToResultUtils": "@libs/common/types/result.type", "MicroserviceResponse": "@libs/common/types/result.type", "MicroserviceResponseUtils": "@libs/common/types/result.type"}, "conversionOptions": {"preserveComments": true, "addTypeAnnotations": true, "generateBackup": true, "validateAfterConversion": true, "batchSize": 10, "parallelProcessing": false}, "excludeFiles": ["*.spec.ts", "*.test.ts", "*.d.ts", "node_modules/**", "dist/**", "build/**"], "includeDirectories": ["apps/*/src/**", "libs/*/src/**"], "rollbackConfig": {"createBackupBeforeConversion": true, "backupDirectory": "./backups", "generateRollbackScript": true, "includeGitCommands": true}}