{"name": "football-game-server", "version": "1.0.0", "description": "Football Game Server", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"start:all": "concurrently \"npm run start:gateway\" \"npm run start:auth\" \"npm run start:character\" \"npm run start:hero\" \"npm run start:match\" \"npm run start:economy\" \"npm run start:social\" \"npm run start:activity\"", "start:gateway": "nest start gateway --watch", "start:auth": "nest start auth --watch", "start:character": "nest start character --watch", "start:match": "nest start match --watch", "start:hero": "nest start hero --watch", "start:social": "nest start social --watch", "start:activity": "nest start activity --watch", "start:economy": "nest start economy --watch", "start:transaction-test": "nest start transaction-test --watch", "start:gateway:dev": "cross-env NODE_ENV=development nest start gateway --watch", "start:auth:dev": "cross-env NODE_ENV=development nest start auth --watch", "start:character:dev": "cross-env NODE_ENV=development nest start character --watch", "start:match:dev": "cross-env NODE_ENV=development nest start match --watch", "start:hero:dev": "cross-env NODE_ENV=development nest start hero --watch", "start:social:dev": "cross-env NODE_ENV=development nest start social --watch", "start:activity:dev": "cross-env NODE_ENV=development nest start activity --watch", "start:economy:dev": "cross-env NODE_ENV=development nest start economy --watch", "start:gateway:debug": "nest start gateway --debug --watch", "start:auth:debug": "nest start auth --debug --watch", "start:character:debug": "nest start character --debug --watch", "start:match:debug": "nest start match --debug --watch", "start:hero:debug": "nest start hero --debug --watch", "start:social:debug": "nest start social --debug --watch", "start:activity:debug": "nest start activity --debug --watch", "start:economy:debug": "nest start economy --debug --watch", "start:gateway:prod": "cross-env NODE_ENV=production nest start gateway --watch", "start:auth:prod": "cross-env NODE_ENV=production nest start auth --watch", "start:character:prod": "cross-env NODE_ENV=production nest start character --watch", "start:match:prod": "cross-env NODE_ENV=production nest start match --watch", "start:hero:prod": "cross-env NODE_ENV=production nest start hero --watch", "start:social:prod": "cross-env NODE_ENV=production nest start social --watch", "start:activity:prod": "cross-env NODE_ENV=production nest start activity --watch", "start:economy:prod": "cross-env NODE_ENV=production nest start economy --watch", "build:all": "npm run build:gateway && npm run build:auth && npm run build:character && npm run build:hero && npm run build:match && npm run build:economy && npm run build:social && npm run build:activity", "build:gateway": "nest build gateway", "build:auth": "nest build auth", "build:character": "nest build character", "build:hero": "nest build hero", "build:match": "nest build match", "build:economy": "nest build economy", "build:social": "nest build social", "build:activity": "nest build activity", "build:transaction-test": "nest build transaction-test", "build": "npm run build:gateway && npm run build:auth && npm run build:character && npm run build:hero && npm run build:match && npm run build:economy && npm run build:social && npm run build:activity", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/football-manager-server/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test:websocket": "node scripts/websocket-test.js", "test:security": "node scripts/security-test.js", "test:redis": "node scripts/run-redis-test-suite.js", "test:redis:comprehensive": "node scripts/redis-comprehensive-test.js", "test:redis:microservice": "node scripts/redis-microservice-test.js", "test:redis:basic": "node scripts/redis-basic-test.js", "test:redis:advanced": "node scripts/redis-advanced-service-test.js", "test:redis:performance": "node scripts/redis-performance-service-test.js", "test:redis:lock": "node scripts/redis-lock-service-test.js", "test:redis:bloom": "node scripts/redis-bloom-filter-test.js", "test:redis:cache-patterns": "node scripts/redis-cache-patterns-test.js", "test:redis:full-suite": "node scripts/run-redis-test-suite.js", "test:comprehensive": "npm run test:api && npm run test:websocket && npm run test:security && npm run test:redis", "test:performance": "npm run test:load && npm run test:stress && npm run test:concurrent", "test:stress": "node scripts/stress-test.js", "test:concurrent": "node scripts/concurrent-test.js", "test:all": "node scripts/run-all-tests.js", "services:start": "node scripts/service-manager.js start", "services:stop": "node scripts/service-manager.js stop", "services:restart": "node scripts/service-manager.js restart", "services:status": "node scripts/service-manager.js status", "services:cleanup": "node scripts/service-manager.js cleanup", "test:report": "node scripts/generate-test-report.js", "migrate:payload": "node tools/migrate-payload-types.js", "migrate:payload:dry-run": "node tools/migrate-payload-types.js --dry-run", "test:payload-migration": "node tools/test-payload-migration.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "backup:full": "node scripts/backup-cli.js full", "backup:incremental": "node scripts/backup-cli.js incremental", "backup:restore": "node scripts/backup-cli.js restore", "backup:list": "node scripts/backup-cli.js list", "backup:status": "node scripts/backup-cli.js status", "backup:cleanup": "node scripts/backup-cli.js cleanup", "backup:verify": "node scripts/backup-cli.js verify", "config:setup": "node tools/config-tools/setup-configs.js", "config:migrate": "node tools/config-tools/migrate-configs.js", "config:generate": "node tools/config-tools/generate-interfaces.js", "config:facade": "node tools/config-tools/generate-facade.js", "config:validate": "node tools/config-tools/validate-configs.js", "config:build": "npm run config:generate && npm run config:facade"}, "dependencies": {"@graphql-tools/executor-http": "^1.3.3", "@graphql-tools/stitch": "^9.4.24", "@graphql-tools/wrap": "^10.1.0", "@nestjs/axios": "^3.1.3", "@nestjs/bull": "^10.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.3.0", "@nestjs/throttler": "^5.0.0", "@nestjs/websockets": "^10.0.0", "@willsoto/nestjs-prometheus": "^6.0.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "bull": "^4.11.0", "cache-manager-redis-store": "^3.0.1", "chokidar": "^3.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "colors": "^1.4.0", "compression": "^1.7.4", "consul": "^0.40.0", "dotenv": "^17.0.1", "express-rate-limit": "^7.0.0", "express-slow-down": "^2.0.0", "fs-extra": "^11.1.0", "geoip-lite": "^1.4.10", "graphql": "^16.11.0", "helmet": "^7.0.0", "http-proxy-middleware": "^2.0.6", "ioredis": "^5.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "moment": "^2.29.4", "mongoose": "^7.8.7", "nest-winston": "^1.9.4", "nodemailer": "^6.9.7", "passport": "^0.6.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prom-client": "^15.1.3", "qrcode": "^1.5.3", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "snappy": "^7.2.2", "socket.io": "^4.7.0", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "tar": "^6.2.0", "twilio": "^4.19.3", "uuid": "^9.0.0", "winston": "^3.10.0", "winston-elasticsearch": "^0.19.0", "ws": "^8.18.3"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/http-proxy-middleware": "^1.0.0", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.11.0", "chalk": "^4.1.2", "commander": "^11.1.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "glob": "^11.0.3", "jest": "^29.5.0", "mongodb-memory-server": "^9.1.3", "ora": "^5.4.1", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-morph": "^26.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/game-config(|/.*)$": "<rootDir>/libs/game-config/src/$1", "^@app/game-constants(|/.*)$": "<rootDir>/libs/game-constants/src/$1", "^@app/game-utils(|/.*)$": "<rootDir>/libs/game-utils/src/$1", "^@app/game-types(|/.*)$": "<rootDir>/libs/game-types/src/$1", "^@port-manager(|/.*)$": "<rootDir>/libs/common/src/port-manager/$1", "^@common/redis$": "<rootDir>/libs/common/src/redis", "^@common/(.*)$": "<rootDir>/libs/common/src/$1", "^@libs/(.*)$": "<rootDir>/libs/$1/src", "^@shared/(.*)$": "<rootDir>/libs/shared/src/$1"}}}